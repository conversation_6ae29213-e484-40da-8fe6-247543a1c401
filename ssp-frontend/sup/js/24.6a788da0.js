(window.webpackJsonp=window.webpackJsonp||[]).push([[24],{"5Rlz":function(e,t,r){"use strict";r("vIW0")},"7a1e":function(e,t,r){"use strict";r.r(t);var a=r("AHSE"),n=r("KDzZ");for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,function(){return n[e]})}(i);r("5Rlz");var o=r("gp09"),l=Object(o.a)(n.default,a.render,a.staticRenderFns,!1,null,"206521ee",null);t.default=l.exports},AHSE:function(e,t,r){"use strict";var a=r("T0y/");r.o(a,"render")&&r.d(t,"render",function(){return a.render}),r.o(a,"staticRenderFns")&&r.d(t,"staticRenderFns",function(){return a.staticRenderFns})},BUMC:function(e,t,r){},FGCB:function(e,t,r){"use strict";r.r(t);var a=r("rSAH"),n=r.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,function(){return a[e]})}(i);t.default=n.a},IAgT:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"}),t("el-button",{attrs:{type:"info",icon:"el-icon-arrow-left"},on:{click:e.goBack}},[e._v("返回\n         ")])],1),t("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[t("el-tab-pane",{attrs:{label:"订单信息",name:"first"}})],1),t("div",{staticClass:"form"},[t("el-form",{ref:"dataForm",staticClass:"item-form",attrs:{id:"orderData2",model:e.orderData,"label-position":"left","label-width":"110px"}},[t("div",{staticClass:"fromBox"},[t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"订单号"}},[t("el-input",{attrs:{value:e.orderData.data.orderNum}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"医疗机构"}},[t("el-input",{attrs:{value:e.orderData.data.hospitalName}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"采购时间"}},[t("el-input",{attrs:{value:e._f("formatTime")(e.orderData.data.submitTime)}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"采购平台"}},["1"==e.orderData.data.source?t("el-input",{attrs:{value:"深圳市药品电子交易平台"}}):e._e(),"2"==e.orderData.data.source?t("el-input",{attrs:{value:"广东省药品电子交易平台"}}):e._e(),"3"==e.orderData.data.source?t("el-input",{attrs:{value:"广州市药品电子交易平台"}}):e._e()],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"支付状态"}},["0"==e.orderData.data.payStatus?t("el-input",{attrs:{value:"未支付"}}):e._e(),"1"==e.orderData.data.payStatus?t("el-input",{attrs:{value:"已支付"}}):e._e(),"2"==e.orderData.data.payStatus?t("el-input",{attrs:{value:"部分支付"}}):e._e()],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"订单状态"},scopedSlots:e._u([{key:"default",fn:function(r){return["0"==e.orderData.data.orderStatus?t("el-input",{attrs:{value:"待确认"}}):e._e(),"1"==e.orderData.data.orderStatus?t("el-input",{attrs:{value:"待发货"}}):e._e(),"2"==e.orderData.data.orderStatus?t("el-input",{attrs:{value:"部分发货"}}):e._e(),"3"==e.orderData.data.orderStatus?t("el-input",{attrs:{value:"已发货"}}):e._e(),"4"==e.orderData.data.orderStatus?t("el-input",{attrs:{value:"已完成"}}):e._e(),"5"==e.orderData.data.orderStatus?t("el-input",{attrs:{value:"已取消"}}):e._e()]}}])})],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"预警状态"}},[t("el-input",{attrs:{value:e.orderData.data.warning}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"合计"}},[t("el-input",{attrs:{value:e.orderData.data.totalPrice+"元"}})],1)],1)],1)],1)])],1),t("div",{staticClass:"memberTab"},[t("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.curTab,callback:function(t){e.curTab=t},expression:"curTab"}},[t("el-tab-pane",{attrs:{label:"订单明细信息"}},[t("item-detail",{ref:"orderItem",attrs:{name:"0",orderItem:e.orderData.orderItem,operateHide:!0}})],1)],1)],1)],1)},t.staticRenderFns=[]},INKj:function(e,t,r){"use strict";r.r(t);var a=r("vVNS"),n=r.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,function(){return a[e]})}(i);t.default=n.a},KDzZ:function(e,t,r){"use strict";r.r(t);var a=r("LcKc"),n=r.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,function(){return a[e]})}(i);t.default=n.a},"LOm/":function(e,t,r){"use strict";var a=r("UtLg");r.o(a,"render")&&r.d(t,"render",function(){return a.render}),r.o(a,"staticRenderFns")&&r.d(t,"staticRenderFns",function(){return a.staticRenderFns})},LcKc:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var a=i(r("Q9c5")),n=i(r("DWNM"));function i(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[n.default],name:"payVoucher-list",data:function(){return{downloadUrl:a.default.baseContext+"/file/download"}},props:{orderPays:{type:Array,default:function(){}},payStatus:{type:String,default:""}},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ")+(t.getHours()<10?"0"+t.getHours()+":":t.getHours()+":")+(t.getMinutes()<10?"0"+t.getMinutes()+":":t.getMinutes()+":")+(t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds())}},mounted:function(){},methods:{downloadFile:function(e,t){var r=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,a=document.createElement("a");a.href=r,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(r)},deleteOrderPay:function(e){this.$emit("deleteOrderPay",e)},editOrderPay:function(e){this.$emit("editOrderPay",e)}}}},"T0y/":function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"item-item"},[t("div",{staticClass:"flex-row-default"},[t("div",{staticClass:"box-card box-right"},[t("div",{staticClass:"text item"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:!1,expression:"false"}],staticClass:"data_table",staticStyle:{width:"100%"},attrs:{"header-cell-style":{background:"#f5f7fa"},data:e.orderPays,border:"","highlight-current-row":""}},[t("el-table-column",{attrs:{label:"序号",width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(t.$index+1)+"\n                        ")]}}])}),t("el-table-column",{attrs:{label:"支付金额(元)",align:"center",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(t.row.payPrice)+"\n                        ")]}}])}),t("el-table-column",{attrs:{width:"180px",label:"支付时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(e._f("formatTime")(t.row.payTime))+"\n                        ")]}}])}),t("el-table-column",{attrs:{width:"300px",label:"支付凭证",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return e._l(JSON.parse(r.row.docInfo),function(r,a){return t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.downloadFile(r.docId,r.name)}}},[e._v("\n                                "+e._s(r.name)+"\n                            ")])})}}])}),t("el-table-column",{attrs:{label:"备注",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(t.row.remark)+"\n                        ")]}}])}),t("el-table-column",{attrs:{width:"180px",label:"数据上传时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(e._f("formatTime")(t.row.creationTime))+"\n                        ")]}}])}),"itemPayVoucher"==e.$route.name&&"1"!=e.payStatus?t("el-table-column",{attrs:{label:"操作",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.editOrderPay(r.row)}}},[e._v("编辑")]),t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.deleteOrderPay(r.row)}}},[e._v("删除")])]}}],null,!1,2948716754)}):e._e()],1)],1)])])])},t.staticRenderFns=[]},UtLg:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"item-item"},[t("div",{staticClass:"flex-row-default"},[t("div",{staticClass:"box-card box-right"},[t("div",{staticClass:"text item"},[t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{border:"",data:e.deliveryItemDataList,"highlight-current-row":""}},[t("el-table-column",{attrs:{prop:"itemNo",label:"配送明细号",width:"180"}}),t("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"180"}}),t("el-table-column",{attrs:{prop:"approvalNumber",label:"批准文号",width:"150"}}),t("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"220"}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"单价(元)",width:"100"}}),t("el-table-column",{attrs:{prop:"num",label:"发货数量"}}),t("el-table-column",{attrs:{prop:"amount",label:"发货金额"}}),t("el-table-column",{attrs:{prop:"deliveryTime",label:"配送时间"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s(e._f("formatTime")(r.row.deliveryTime)))])]}}])}),t("el-table-column",{attrs:{prop:"warning",label:"预警状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.warning&&"1"!=r.row.warning&&"4"!=r.row.warning?e._e():t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("正常")])],1),r.row.warning&&"1"!=r.row.warning&&"4"!=r.row.warning?t("div",[t("el-popover",{attrs:{placement:"top-start",width:"250",trigger:"hover"}},[e._l(e.getWaring(r.row.warning),function(r,a){return t("div",{key:a,staticClass:"text item"},[e._v("\n                                    "+e._s(a+1+"、"+r.name)+"\n                                  ")])}),t("el-tag",{attrs:{slot:"reference",type:"danger"},slot:"reference"},[e._v("查看预警")])],2)],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"country",label:"是否国家集采",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return["1"==r.row.country?t("div",[t("el-tag",{attrs:{type:"primary"}},[e._v("是")])],1):e._e(),"1"!=r.row.country?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("否")])],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"stockStatus",label:"入库状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.stockStatus&&"0"!=r.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未入库")])],1),"1"==r.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已入库")])],1):e._e(),"2"==r.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分入库")])],1):e._e()]}}])})],1)],1)])],1)])]),t("el-dialog",{attrs:{title:"发票信息",visible:e.dialogVisible,width:"70%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",{staticClass:"dialog-content"},[t("invoice-item",{ref:"deliveryItem",attrs:{name:"0",deliveryItemId:this.deliveryItemId}})],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("关闭")])],1)])],1)},t.staticRenderFns=[]},mxJk:function(e,t,r){"use strict";r("BUMC")},orAR:function(e,t,r){"use strict";r.r(t);var a=r("ujDQ"),n=r("FGCB");for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,function(){return n[e]})}(i);r("w0NE");var o=r("gp09"),l=Object(o.a)(n.default,a.render,a.staticRenderFns,!1,null,"40d9b83a",null);t.default=l.exports},rSAH:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var a=s(r("yDCl")),n=s(r("7a1e")),i=s(r("cjK8")),o=s(r("Q9c5")),l=s(r("DWNM"));function s(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[l.default],name:"orderDetail",data:function(){return{activeName:"first",curTab:"0",dialogVisible:!1,orderData:{data:{},orderItem:[],hospital:{}},listLoading:!1,tableHeight:100}},components:{itemDetail:i.default,payVoucher:n.default,deliveryItemList:a.default},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ")+(t.getHours()<10?"0"+t.getHours()+":":t.getHours()+":")+(t.getMinutes()<10?"0"+t.getMinutes()+":":t.getMinutes()+":")+(t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds())}},watch:{},mounted:function(){this.$route.query.orderId&&this.getOrderDetail()},methods:{goBack:function(){this.$router.go(-1)},handleClick:function(e,t){this.curTab=e.index},getOrderDetail:function(){var e=this,t=this.openLoading("查询中"),r=o.default.baseContext+"/supervise/supOrder/show/"+this.$route.query.orderId;this.$http_post(r,{}).then(function(r){1==r.state?(e.orderData=r.row,t.close()):(t.close(),e.$alert(r.message))})}}}},ujDQ:function(e,t,r){"use strict";var a=r("IAgT");r.o(a,"render")&&r.d(t,"render",function(){return a.render}),r.o(a,"staticRenderFns")&&r.d(t,"staticRenderFns",function(){return a.staticRenderFns})},vIW0:function(e,t,r){},vVNS:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var a=o(r("pU08")),n=o(r("DWNM")),i=o(r("Q9c5"));function o(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[n.default],name:"deliveryItemList",data:function(){return{warningData:[],deliveryItemId:"",dialogVisible:!1,deliveryItemDataList:[]}},props:{deliveryCode:{type:String,default:"#"},orderCode:{type:String,default:"#"},orderItemId:{type:String,default:"#"}},components:{invoiceItem:a.default},computed:{goPath:{get:function(){return"#"!=this.orderCode?"/order/orderDetail/invoice/invoiceItemList":"#"!=this.deliveryCode?"/delivery/deliveryDetail/invoice/invoiceItemList":"#"!=this.orderItemId?"/sup/delivery/deliveryItemList":void 0},set:function(e){}}},watch:{orderCode:function(e){e&&this.onDeliveryQuery()},deliveryCode:function(e){e&&this.onDeliveryQuery()},orderItemId:function(e){e&&this.onDeliveryQuery()}},mounted:function(){this.onDeliveryQuery(),this.getDictItem("WARNING")},methods:{getDictItem:function(e){var t=this,r=this.openLoading();this.$http_post(i.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(a){var n=a.rows;1==a.state?("SOURCE"==e&&(t.drugSourceList=n),"WARNING"==e&&(t.warningData=n),r.close()):(r.close(),t.$message.error(a.message))})},getWaringType:function(e){for(var t=0;t<this.warningData.length;t++)if(this.warningData[t].value==e)return this.warningData[t].label},getWaring:function(e){for(var t=[],r=e.split(","),a=0;a<r.length;a++){var n=this.getWaringType(r[a]);n&&t.push({name:n})}return t},showInvoice:function(e){this.deliveryItemId=e,this.dialogVisible=!0},onDeliveryQuery:function(){var e=this,t={orderCode:"#",orderItemId:"#"};"#"!=this.orderCode&&(t={orderCode:this.orderCode}),"#"!=this.orderItemId&&(t={orderItemId:this.orderItemId}),"#"!=this.deliveryCode&&(t={deliveryCode:this.deliveryCode});var r=this.openLoading(),a=i.default.baseContext+"/supervise/supDeliveryItem/getDeliveryItemByCode";this.$http_post(a,t).then(function(t){1==t.state?(e.deliveryItemDataList=t.rows,r.close()):(r.close(),e.$alert(t.message))})}},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e),r=t.getFullYear()+"-",a=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",n=t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ";t.getHours(),t.getHours(),t.getMinutes(),t.getMinutes(),t.getSeconds(),t.getSeconds();return r+a+n}}}},w0NE:function(e,t,r){"use strict";r("xSuj")},xSuj:function(e,t,r){},yDCl:function(e,t,r){"use strict";r.r(t);var a=r("LOm/"),n=r("INKj");for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,function(){return n[e]})}(i);r("mxJk");var o=r("gp09"),l=Object(o.a)(n.default,a.render,a.staticRenderFns,!1,null,"1125e6fb",null);t.default=l.exports}}]);