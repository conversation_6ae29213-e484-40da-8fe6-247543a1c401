@font-face{font-family:element-icons;src:url(../../static/theme/fonts/element-icons.woff) format("woff"),url(../../static/theme/fonts/element-icons.ttf) format("truetype");font-weight:400;font-style:normal}
body {
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-smoothing: antialiased;
	text-rendering: optimizeLegibility;
	font-family: Yuanti SC,PingFangSC,helvetica neue,hiragino sans gb,arial,microsoft yahei ui,microsoft yahei,simsun,"sans-serif"!important
}

body,html {
	height: 100%
}

html {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	font-size: 14px
}

body,dd,dl,dt,h1,h2,h3,h4,html,li,p,ul {
	margin: 0;
	padding: 0;
	font-weight: 400;
	list-style: none
}

*,:after,:before {
	-webkit-box-sizing: inherit;
	box-sizing: inherit
}

a,a:active,a:focus,a:hover,div:focus {
	outline: none
}

a,a:focus,a:hover {
	cursor: pointer;
	color: inherit;
	text-decoration: none
}
::-webkit-scrollbar {
	width:4px;
	height: 4px;
	background-color: transparent;
}
::-webkit-scrollbar-track {
	border-radius:10px;
}
::-webkit-scrollbar-thumb {
	border-radius:10px;
	background: #e2e2e2;
}

.clearfix:after {
	visibility: hidden;
	display: block;
	font-size: 0;
	content: " ";
	clear: both;
	height: 0
}

.text-left {
	text-align: left
}

.text-right {
	text-align: right
}

.text-center {
	text-align: center
}

.pull-right {
	float: right!important
}

.pull-left {
	float: left!important
}

.text-muted {
	color: #999!important
}

.h100 {
	height: 100%
}

.w100 {
	width: 100%
}

.f12 {
	font-size: 12px!important
}

.f14 {
	font-size: 14px!important
}

.f16 {
	font-size: 16px!important
}

.f18 {
	font-size: 18px!important
}

.f20 {
	font-size: 20px!important
}

.p15 {
	padding: 15px!important
}

.border {
	border: 1px solid #ececec
}

.no-border {
	border: 0!important
}

.ml30 {
	margin-left: 30px!important
}

.ml20 {
	margin-left: 20px!important
}

.ml15 {
	margin-left: 15px!important
}

.ml10 {
	margin-left: 10px!important
}

.ml5 {
	margin-left: 5px!important
}

.mr5 {
	margin-right: 5px!important
}

.mr15 {
	margin-right: 15px!important
}

.mt10 {
	margin-top: 10px!important
}

.mt15 {
	margin-top: 15px!important
}

.mt20 {
	margin-top: 20px!important
}

.mt30 {
	margin-top: 30px!important
}

.p0 {
	padding: 0!important
}

.p10 {
	padding: 10px!important
}

.p20 {
	padding: 20px!important
}

.p30 {
	padding: 30px!important
}

.pl0 {
	padding-left: 0!important
}

.bgw {
	background-color: #fff
}

.bgg {
	background-color: #f8f8f8
}

.border-r {
	border-right: 1px solid #ececec
}

.fixed-width {
	width: 1080px;
	margin: auto
}

.flex-sb {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between
}

.greybg {
	background: #f8f8f8;
	width: 100%
}

.dialog-footer {
	text-align: center;
	margin: 15px auto
}

.svg-icon {
	width: 1em;
	height: 1em;
	vertical-align: -.15em;
	fill: currentColor;
	overflow: hidden
}
.show{
	-webkit-transition: all .3s cubic-bezier(.7,.3,.1,1);
	transition: all .3s cubic-bezier(.7,.3,.1,1);
}
.flex{
	&-row{
		display: flex;
		flex-direction: row;
		align-items: center;
	}
	&-column{
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	&-row-default{
		display: flex;
		flex-direction: row;
	}
	&-column-default{
		display: flex;
		flex-direction: column;
	}
}
/* 文本超出行数隐藏显示省略号 */
.multi-line-overflow{
	display: -webkit-box;
	-webkit-box-orient: vertical;
	overflow: hidden;
	//-webkit-line-clamp: 4;//行数
}
//返回按钮一行
.on-operating{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin-top: -20px;
	height: 50px;
	line-height: 50px;
	button{
		color: #666;
		&:hover{
			color: #2f54eb;
		}
	}
}
//内容区域(可滚动区域)
.content-among{
	position: absolute;
	top: 50px;
	left: 0px;
	right: 0px;
	bottom: 80px;
	overflow-y: auto;
}
//底部操作按钮
.bottom-btn{
	height: 120px !important;
	line-height: 120px !important;
	position: absolute;
	left: 0px;
	right: 0px;
	bottom: -40px;
	text-align: center;
	background: white;
	z-index: 99;
	button{
		padding: 15px 50px !important;
		margin: 0px 15px !important;
	}
}
//多选下拉框
.el-select-dropdown__item{
	span{
		padding-right: 20px;
	}
}
//数据计算-计算管理操作样式
.cal-compute-operating{
	/deep/.el-button{
		margin: 5px 5px!important;
		width: 100px;
	}
}
//头部产品服务
.header-product{
	.content{
		align-items: flex-start;
		.item{
			width: 20%;
			padding: 0px 10px;
			.title{
				width: 100%;
				font-size: 16px;
				text-align: center;
				font-weight: bold;
				padding: 10px 0px;
				border-bottom: 1px solid #ccc;
				color: black;
			}
			ul{
				padding-top: 10px;
				li{
					padding: 10px 15px;
					cursor: pointer;
					position: relative;
					span{
						padding-right: 2px;
					}
					.add,.remove{
						display: none;
						position: absolute;
						right: 0px;
						&:focus{
							outline: none;
						}
					}
					.add{
						color: #1CB5D5;
					}
					.remove{
						color: #F56C6C;
					}
					&:hover{
						color: #1CB5D5;
					}
				}
				.disabled{
					color: #ccc !important;
				}
			}
			&-edit{
				ul{
					li{
						.add,.remove{
							display: block;
						}
					}
				}
			}
		}
	}
	.edit{
		display: block;
		text-align: right;
		i{
			margin-left: 10px;
			font-size: 17px;
			cursor: pointer;
		}
	}
}
//弹窗边距调整
.qz-dialog-padding{
	/deep/ .el-dialog__body{
		padding: 10px 20px!important;
	}
}
//表数据超出隐藏显示省略号
.qz-table-hide{
  /deep/ th{
    background-color: #f5f7fa!important;
  }
  /deep/ td{
    .cell{
      white-space: nowrap!important;
      overflow: hidden;
    }
  }
}
.qz-table-new{
  /deep/ th{
    background-color: #f5f7fa!important;
    color:#1F2A3D
  }
  /deep/ td{
    .cell{
      white-space: nowrap!important;
      overflow: hidden;
      color:#1F2A3D
    }
  }
}
//主题事项管理头部搜索框
.qz-theme-top-search{
  .item{
    margin-bottom: 10px;
    &>span{
      display: inline-block;
      min-width: 60px;
      margin-right: 10px;
    }
    .el-select{
      width: 100%;
      padding-right: 0px;
    }
  }
  .operating{
    justify-content: center;
  }
}
