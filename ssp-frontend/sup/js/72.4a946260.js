(window.webpackJsonp=window.webpackJsonp||[]).push([[72],{"8XNa":function(e,t,s){Object.defineProperty(t,"__esModule",{value:!0});var a=r(s("Q9c5")),n=r(s("DWNM"));function r(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[n.default],data:function(){return{dataList:[],pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),s=new Date,a=new Date(s.getTime()-6048e5).format("yyyy-MM-dd");e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),s=new Date,a=new Date(s.getTime()-2592e6).format("yyyy-MM-dd");e.$emit("pick",[a,t])}},{text:"最近三个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),s=new Date,a=new Date(s.getTime()-7776e6).format("yyyy-MM-dd");e.$emit("pick",[a,t])}}]},params:{page:1,limit:10,records:0,status:"",title:"",sendTime:"",receiveUserId:""},userList:[],tableHeight:100}},props:{},computed:{},watch:{},mounted:function(){var e=this;this.getUserList(),this.onQuery(),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}},methods:{revert:function(e){var t=this;this.$alert("确定撤回【"+e.title+"】数据吗","确认信息",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,type:"warning"}).then(function(){var s=t.openLoading();t.$http_post(a.default.baseContext+"/supervise/supOfficial/revert/"+e.id,null).then(function(e){1==e.state?(t.$message.success("撤回成功"),t.onQuery(),s.close()):(s.close(),t.$message.error(e.message))})}).catch(function(e){console.log(e)})},time:function(e,t){if(void 0==e.sendTime||""==e.sendTime)return"";var s=new Date(e.sendTime);return s.getFullYear()+"-"+((s.getMonth()+1<10?"0"+(s.getMonth()+1):s.getMonth()+1)+"-")+(s.getDate()<10?"0"+s.getDate()+" ":s.getDate()+" ")+(s.getHours()<10?"0"+s.getHours()+":":s.getHours()+":")+(s.getMinutes()<10?"0"+s.getMinutes()+":":s.getMinutes()+":")+(s.getSeconds()<10?"0"+s.getSeconds():s.getSeconds())},del:function(e){var t=this;this.$alert("确定删除【"+e.title+"】数据吗","确认信息",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,type:"warning"}).then(function(){var s=t.openLoading();t.$http_post(a.default.baseContext+"/supervise/supOfficial/delete/"+e.id,null).then(function(e){1==e.state?(t.$message.success("删除成功"),t.onQuery(),s.close()):(s.close(),t.$message.error(e.message))})}).catch(function(e){console.log(e)})},handleCurrentChange:function(e){},onSearch:function(e){"reset"==e?(this.params={page:1,limit:10,records:0,status:"",title:"",sendTime:"",receiveUserId:""},this.onQuery()):""!=this.params.status||""!=this.params.title||""!=this.sendTime||""!=this.receiveUserId?(this.params.page=1,this.onQuery()):this.$message.warning("请输入查询条件")},onPageClick:function(e){this.params.page=e,this.onQuery()},onQuery:function(){var e=this,t=this.params,s=this.openLoading(),n=a.default.baseContext+"/supervise/supOfficial/list";this.$http_post(n,t).then(function(t){if(1==t.state){var a=t.rows;e.dataList=a,e.params.records=t.records,s.close()}else s.close(),e.$alert(t.message)})},getUserList:function(){var e=this,t=a.default.baseContext+"/bsp/pubUser/all";this.$http_post(t,{}).then(function(t){1==t.state?e.userList=t.rows:e.$alert(t.message)})}},beforeDestroy:function(){window.onresize=null}}},FZ8S:function(e,t,s){"use strict";s.r(t);var a=s("knAY"),n=s("YJMX");for(var r in n)["default"].indexOf(r)<0&&function(e){s.d(t,e,function(){return n[e]})}(r);s("h7bz");var i=s("gp09"),l=Object(i.a)(n.default,a.render,a.staticRenderFns,!1,null,"38de8699",null);t.default=l.exports},YJMX:function(e,t,s){"use strict";s.r(t);var a=s("8XNa"),n=s.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){s.d(t,e,function(){return a[e]})}(r);t.default=n.a},bWba:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left"},[t("el-row",{attrs:{gutter:10}},[t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("标题")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入发件标题"},model:{value:e.params.title,callback:function(t){e.$set(e.params,"title",t)},expression:"params.title"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("收件人")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:e.params.receiveUserId,callback:function(t){e.$set(e.params,"receiveUserId",t)},expression:"params.receiveUserId"}},e._l(e.userList,function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.id}})}),1)],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("发送时间")]),t("div",{staticClass:"searchInput"},[t("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:e.params.sendTime,callback:function(t){e.$set(e.params,"sendTime",t)},expression:"params.sendTime"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("状态")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",placeholder:"请选择公文状态"},model:{value:e.params.status,callback:function(t){e.$set(e.params,"status",t)},expression:"params.status"}},[t("el-option",{attrs:{label:"暂存",value:"0"}}),t("el-option",{attrs:{label:"已发送",value:"1"}})],1)],1)])])],1)],1),t("div",{staticClass:"right"},[t("div",{staticStyle:{"margin-bottom":"5px"}},[t("el-button",{attrs:{size:"small",icon:"el-icon-edit",type:"primary"},on:{click:function(t){return e.$router.push({path:"sendPage",query:{editType:"add"}})}}},[e._v("我要发文")])],1),t("div",[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1)])]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.dataList,"highlight-current-row":"",height:e.tableHeight,border:""},on:{"current-change":e.handleCurrentChange}},[t("el-table-column",{attrs:{label:"公文标题",prop:"title"}}),t("el-table-column",{attrs:{label:"发送人",prop:"sendUserName"}}),t("el-table-column",{attrs:{label:"查阅次数",prop:"num"}}),t("el-table-column",{attrs:{label:"状态",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(s){return["0"==s.row.status?t("div",[t("el-tag",{attrs:{type:""}},[e._v("暂存")])],1):e._e(),"1"==s.row.status?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已发送")])],1):e._e()]}}])}),t("el-table-column",{attrs:{label:"发送时间",prop:"sendTime",formatter:e.time}}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"200"},scopedSlots:e._u([{key:"default",fn:function(s){return["0"==s.row.num&&"1"==s.row.status?t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.revert(s.row)}}},[e._v("撤回")]):e._e(),"0"==s.row.status?t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.$router.push({path:"sendPage",query:{id:s.row.id,editType:"edit"}})}}},[e._v("编辑")]):e._e(),"0"==s.row.status?t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.del(s.row)}}},[e._v("删除")]):e._e(),"1"==s.row.status?t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.$router.push({path:"sendPage",query:{id:s.row.id,editType:"show"}})}}},[e._v("查看")]):e._e()]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)])],1)},t.staticRenderFns=[]},h7bz:function(e,t,s){"use strict";s("u8F6")},knAY:function(e,t,s){"use strict";var a=s("bWba");s.o(a,"render")&&s.d(t,"render",function(){return a.render}),s.o(a,"staticRenderFns")&&s.d(t,"staticRenderFns",function(){return a.staticRenderFns})},u8F6:function(e,t,s){}}]);