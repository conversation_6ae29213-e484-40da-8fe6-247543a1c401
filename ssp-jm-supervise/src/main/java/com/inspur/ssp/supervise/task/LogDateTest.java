package com.inspur.ssp.supervise.task;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 日志按日期分文件夹测试类
 * 用于验证日志配置是否按日期正确生成文件夹和文件
 */
public class LogDateTest {
    
    private static final Logger logger = LoggerFactory.getLogger(LogDateTest.class);
    private static final Logger jobLogger = LoggerFactory.getLogger("job");
    private static final Logger startupLogger = LoggerFactory.getLogger("com.inspur.StartupApplication");
    
    public static void main(String[] args) {
        System.out.println("开始测试日志按日期分文件夹功能...");
        
        // 测试主日志记录器
        testMainLogger();
        
        // 测试INFO级别日志记录器
        testInfoLogger();
        
        // 测试ERROR级别日志记录器
        testErrorLogger();
        
        // 测试定时任务日志记录器
        testJobLogger();
        
        // 测试启动日志记录器
        testStartupLogger();
        
        System.out.println("日志按日期分文件夹测试完成！");
        System.out.println("请检查以下目录结构：");
        System.out.println("1. ./logs/yyyy-MM-dd/supervise.yyyy-MM-dd.0.log - 主日志文件");
        System.out.println("2. ./logs/info/yyyy-MM-dd/supervise-info.yyyy-MM-dd.0.log - INFO级别日志");
        System.out.println("3. ./logs/error/yyyy-MM-dd/supervise-error.yyyy-MM-dd.0.log - ERROR级别日志");
        System.out.println("4. ./logs/job/yyyy-MM-dd/supervise-job.yyyy-MM-dd.0.log - 定时任务日志");
        System.out.println("5. ./logs/startup/yyyy-MM-dd/supervise-startup.yyyy-MM-dd.0.log - 启动日志");
    }
    
    private static void testMainLogger() {
        System.out.println("测试主日志记录器...");
        for (int i = 0; i < 10; i++) {
            logger.info("主日志测试消息 - 第{}条", i + 1);
            logger.debug("主日志DEBUG消息 - 第{}条", i + 1);
            logger.warn("主日志WARN消息 - 第{}条", i + 1);
            if (i % 3 == 0) {
                logger.error("主日志ERROR消息 - 第{}条", i + 1);
            }
        }
    }
    
    private static void testInfoLogger() {
        System.out.println("测试INFO级别日志记录器...");
        for (int i = 0; i < 10; i++) {
            logger.info("INFO级别日志测试消息 - 第{}条", i + 1);
        }
    }
    
    private static void testErrorLogger() {
        System.out.println("测试ERROR级别日志记录器...");
        for (int i = 0; i < 5; i++) {
            logger.error("ERROR级别日志测试消息 - 第{}条", i + 1);
            try {
                throw new RuntimeException("测试异常 - 第" + (i + 1) + "个");
            } catch (Exception e) {
                logger.error("捕获到测试异常", e);
            }
        }
    }
    
    private static void testJobLogger() {
        System.out.println("测试定时任务日志记录器...");
        for (int i = 0; i < 10; i++) {
            jobLogger.info("定时任务日志测试消息 - 第{}条", i + 1);
            jobLogger.debug("定时任务DEBUG消息 - 第{}条", i + 1);
        }
    }
    
    private static void testStartupLogger() {
        System.out.println("测试启动日志记录器...");
        for (int i = 0; i < 10; i++) {
            startupLogger.info("启动日志测试消息 - 第{}条", i + 1);
            startupLogger.debug("启动DEBUG消息 - 第{}条", i + 1);
        }
    }
}
