.cms-vip .el-date-table td.today span{
	color:#59a9ff;
}
.cms-vip .el-date-table td.current:not(.disabled) span{
	background:#59a9ff;
}
.cms-vip .el-date-table td.available:hover{
	color:#59a9ff;
}
.cms-vip .el-button--text{
	color:#59a9ff;
}
.cms-vip .el-date-picker__header-label.active, .cms-vip .el-date-picker__header-label:hover{
	color:#59a9ff;
}
.cms-vip .el-button--text:focus, .cms-vip .el-button--text:hover{
	color:#59a9ff;
}
.cms-vip .el-button.is-plain:focus, .cms-vip .el-button.is-plain:hover{
	border-color:#59a9ff;
	color:#59a9ff;
}
.cms-vip .el-input.is-active .el-input__inner, .cms-vip .el-input__inner:focus{
	border-color:#59a9ff;
}
.cms-vip .el-time-panel__btn.confirm{
	color:#59a9ff;
}
.cms-vip .el-range-editor.is-active, .cms-vip .el-range-editor.is-active:hover{
	border-color:#59a9ff;
}
.cms-vip .el-date-table td.end-date span, .cms-vip .el-date-table td.start-date span{
	background: #59a9ff;
}
.cms-vip .el-button:focus, .cms-vip .el-button:hover{
	border-color:#59a9ff;
	color:#59a9ff;
	background: #e4f1ff;
}
.cms-vip .el-checkbox__inner:hover{
	border-color:#59a9ff;
}
.cms-vip .el-checkbox__input.is-checked .el-checkbox__inner, .cms-vip .el-checkbox__input.is-indeterminate .el-checkbox__inner{
	border-color:#59a9ff;
	background: #59a9ff;
}
.cms-vip .el-select .el-input.is-focus .el-input__inner{
	border-color:#59a9ff;
}
.cms-vip .el-select-dropdown__item span{
    font-size: 0.2rem;
}
.cms-vip .el-select-dropdown__item.selected{
	color:#59a9ff;
}
.cms-vip .el-input-number__decrease:hover, .cms-vip .el-input-number__increase:hover{
	color:#59a9ff;
}
.cms-vip .el-input-number__decrease:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled), .cms-vip .el-input-number__increase:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled){
	border-color:#59a9ff;
}
.cms-vip .el-textarea__inner:focus{
	border-color:#59a9ff;
}
.cms-vip .el-tag .el-tag__close:hover{
	background:#59a9ff;	
}
.cms-vip .el-checkbox__input.is-focus .el-checkbox__inner{
	border-color:#59a9ff;
}
.cms-vip .el-select .el-input__inner:focus{
	border-color:#59a9ff;
}
.cms-vip .el-radio__input.is-disabled .el-radio__inner:hover{
	border-color: #e4e7ed;
}
.cms-vip .el-button--primary{
	border-color:#59a9ff;
	background:#59a9ff;
}