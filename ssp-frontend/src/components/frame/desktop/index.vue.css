/*样式初始化-开始*/
/*设置字符集*/
@charset "utf-8";

html,
body {
	width: 100%;
	height: 100%;
	font-size: 62.5%;
	line-height: 1;
	font-family: <PERSON> YaHei, Microsoft YaHei !important;
	font-weight: 500;
	color: #101010;
	background: #FFF;
	overflow: hidden;
	-webkit-text-size-adjust: none;
}

body { /*禁用文本选择*/
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

/* *{text-shadow:0 0 .01rem #ccc !important;} */
/* *:not([class*="icon"]):not([class*="mui-amount"]):not([class*="chaoshi"]):not([class*="top-nav"]):not([href*="ju.taobao.com"]):not([class*="Icon"]):not([class*="prev"]):not([class*="next"]):not([class*="pay-"]):not([class*="tm-shop-list-"]):not(b):not(ins):not(i):not(s){font-family:"Microsoft YaHei"!important;} */

* {
	margin: 0;
	padding: 0;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	max-width: 9999px;
	max-height: 9999px;
	font-size: 1.6px;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
	display: block;
}

ol,
li,
ul,
dl,
dt,
dd {
	list-style: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-size: inherit;
	font-weight: normal;
}

address,
em,
i,
b {
	display: inline-block;
	font-weight: normal;
	font-style: normal;
}

img {
	vertical-align: top;
	border: 0;
	-ms-interpolation-mode: bicubic;
}

/* 清除图片边距 */
/* a, button, input, textarea, select { -webkit-tap-highlight-color: rgba(255, 255, 255, 0); -webkit-appearance: none; appearance: none; border-radius: 0; border: 0; outline: none; font-size: 100%; font-family: inherit; } 消除输入框和按钮的原生外观,清除移动端点击时默认的背景色 */
a,
button {
	-webkit-tap-highlight-color: rgba(255, 255, 255, 0);
	-webkit-appearance: none;
	appearance: none;
	border-radius: 0;
	border: 0;
	outline: none;
	font-size: 100%;
	font-family: inherit;
}

/* 消除输入框和按钮的原生外观,清除移动端点击时默认的背景色 */
a,
a:hover,
a:active,
a:visited {
	color: inherit;
	text-decoration: none;
	outline: 0;
}

input::-webkit-input-placeholder {
	color: #EEEEEE;
}

/* 修改webkit表单输入框placeholder的样式 */
input:focus::-webkit-input-placeholder {
	color: #EEEEEE;
}

/* 修改webkit表单输入框光标的样式 */
input::-webkit-input-speech-button {
	display: none;
}

/* android 上去掉语音输入按钮 */
::-webkit-scrollbar {
	display: none;
}

/*默认滚动条样式修改ie9 google Firefox 等高版本浏览器有效
/*样式初始化-结束*/
/*公共样式-开始*/
/*字体颜色*/
.s-ftc1 {
	color: #FFF;
}

/*字体-*/
.s-ftc2 {
	color: #59A9FF;
}

/*字体-标题、主要文本*/
.s-ftc3 {
	color: #333333;
}

/*字体-次要文本*/
.s-ftc4 {
	color: #727272;
}

/*字体-说明性、标签文本、系统图标*/
.s-ftc5 {
	color: #404244;
}

/*字体-按提示性文本*/
/*背景颜色*/
.s-bgc-main {
	background-color: #2990FF;
}

/*背景-主色*/
.s-bgc-btn1 {
	background-color: #59A9FF;
}

/*背景-按钮1*/
.s-bgc-btn2 {
	background-color: #d71318;
}

/*背景-按钮2*/
.s-bgc-default {
	background-color: #FFF;
}

/*背景-默认*/
/*边框颜色*/
.s-bdc- {
	border-color: #EEE;
}

/*边框-默认*/
/*边框-链接*/

/*字体大小定义 */
.s-fts1 {
	font-size: .3rem;
}

/*字体大小-用于资讯标题、插画弹框标题*/
.s-fts2 {
	font-size: .2rem;
	;
}

/*字体大小-导航、个人中心用户名、功能模块大标题、提示框标题、底部按钮文字*/
/*下面是功能样式*/
.f-floatleft {
	float: left;
}

.f-floatright {
	float: right;
}

.f-clearboth {
	zoom: 1;
}

.f-clearboth:after {
	content: "";
	display: block;
	clear: both;
}

/* flex 弹性盒子*/
.flex {
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
}

.flex-v {
	-webkit-box-orient: vertical;
	-webkit-flex-direction: column;
	-ms-flex-direction: column;
	flex-direction: column;
}

.flex-1 {
	-webkit-box-flex: 1;
	-webkit-flex: 1;
	-ms-flex: 1;
	flex: 1;
}

.flex-align-center {
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;
}

.flex-align-start {
	-webkit-box-align: flex-start;
	-webkit-align-items: flex-start;
	-ms-flex-align: flex-start;
	align-items: flex-start;
}

.flex-justify-center {
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
}

.flex-justify-between {
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-ms-flex-pack: justify;
	justify-content: space-between;
}

.flex-justify-around {
	-webkit-box-pack: justify;
	-webkit-justify-content: space-around;
	-ms-flex-pack: justify;
	justify-content: space-around;
}

.flex-wrap {
	flex-wrap: wrap;
}

/*下面是自定义辅助样式(以.lc为前缀（浪潮英文首字母）尽量不要使用驼峰命名法，使用“-”或“_”隔开)*/
.lc-select-no {
	-webkit-user-select: none;
	user-select: none;
}

/*禁止移动端用户进行复制.选择, 即禁止页面文字选中 ，此属性不继承，一般加在body上规定整个body的文字都不会被选中*/
.lc-callout-no {
	-webkit-touch-callout: none;
}

/*禁用长按页面时的弹出菜单, 即阻止屏幕长按的默认事件*/
.lc-scroll {
	-webkit-overflow-scrolling: touch;
}

/*给滚动元素添加该属性使手机端滑动更流畅*/
.lc-click {
	cursor: pointer;
}

.lc-table {
	border-spacing: 0;
	border-collapse: collapse;
}

/*合并边框,间距为0*/
.lc-ellipsis1 {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/*内容超出一行裁剪*/
.lc-ellipsis2 {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
}

/*超出 2 行裁剪*/
.lc-border {
	border-width: 1px;
	border-style: solid;
}

.lc-border-t {
	border-top-width: 1px;
	border-top-style: solid;
}

.lc-border-b {
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

.lc-border-l {
	border-left-width: 1px;
	border-left-style: solid;
}

.lc-border-r {
	border-right-width: 1px;
	border-right-style: solid;
}

.lc-border-radius {
	border-radius: .12rem;
}

.lc-text-center {
	text-align: center;
}

.lc-text-right {
	text-align: right;
}

.lc-font-w {
	font-weight: bold;
}

.lc-mg-r10 {
	margin-right: 1px;
}

.lc-mg-auto {
	margin: 0 auto;
}

.lc-box-shadw {
	box-shadow: 0 .02rem .1rem .04rem rgba(89, 169, 255, 0.2)
}

/*左右模块阴影*/
.lc-box-shadwB {
	box-shadow: 0 .02rem .1rem .04rem rgba(83, 83, 83, 0.2);
}

/*头部log区域阴影*/
/* 路由点击颜色 */
.lc-active {
	background: #59A9FF;
	color: #FFF !important;
}

.lc-active b {
	border-bottom-color: #FFF;
}

.lc-active span,
.lc-active i {
	color: #FFF !important;
}

.btn {
	text-align: center;
	height: .84rem;
	width: 2.9rem;
	background: #59A9FF;
	color: #FFF;
	font-size: .26rem;
	font-weight: bold;
	border-radius: .12rem;
}

/*公共样式-结束*/
/*下面动画样式*/
.view-enter-active,
.view-leave-active {
	/*过程添加过渡*/
	transition: all 0.1s;
}

.view-enter,
.view-leave-to {
	/*进入前和离开后的样式*/
	opacity: 0;
}

.view-enter-to,
.view-leave {
	/*进入后和离开前的样式(可省)*/
	opacity: 1;
}
.hidden-ellipsis{
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.btn-orange{
	background:#ff9630;
}
.btn-desabled{
	background:rgba(89,169,255,0.5);
}
/* 文本超出行数隐藏显示省略号  使用时添加限制行数-webkit-line-clamp: 4; */
.multi-line-overflow{
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.btn-link{
	background:none;
	color:#2990ff;
	border:none;
}