(window.webpackJsonp=window.webpackJsonp||[]).push([[75],{"6KGN":function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var a=s(r("Q9c5")),o=s(r("DWNM")),l=s(r("XRYr"));function s(e){return e&&e.__esModule?e:{default:e}}t.default={name:"generateOrderItem",mixins:[o.default],data:function(){return{tableHeight:100,drugBox:!1,orderBox:!1,showDrugDetailBox:!1,fileList:[],downloadUrl:a.default.baseContext+"/file/download",orderData:{hospital:{},source:"4",orderItem:[],totalPrice:0,hospitalName:"",submitTime:"",orderNum:"",userName:"",address:""},headers:{},drug:{list:[],params:{page:1,limit:10,records:0,country:"2",catalogName:"",specs:"",dosageForm:"",category:""},catalogNameList:[]},drugDetail:{},rules:{amount:[{validator:function(e,t,r){0==/^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/.test(t)?r(new Error("请输入大于零的数量")):r()},trigger:"change"},{validator:function(e,t,r){0==/^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/.test(t)?r(new Error("请输入大于零的数量")):r()},trigger:"blur"},{type:"number",required:!0,message:"采购数量不能为空",trigger:"blur"}],invoiceCode:[{required:!0,message:"发票代码不能为空",trigger:"blur"}],invoiceNo:[{required:!0,message:"发票号不能为空",trigger:"blur"}],deliveryName:[{required:!0,message:"配送企业不能为空",trigger:"blur"}],name:[{required:!0,message:"医疗机构不能为空",trigger:"blur"}],userName:[{required:!0,message:"订单创建人不能为空",trigger:"blur"}],submitTime:[{type:"date",required:!0,message:"日期不能为空",trigger:"blur"}]}}},computed:{uploadUrl:function(){return a.default.baseContext+"/file/upload"}},mounted:function(){var e=this,t=l.default.doCloundRequest(a.default.app_key,a.default.app_security,"");this.headers["x-aep-appkey"]=t["x-aep-appkey"],this.headers["x-aep-signature"]=t["x-aep-signature"],this.headers["x-aep-timestamp"]=t["x-aep-timestamp"],this.headers["x-aep-nonce"]=t["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,this.getHospital(),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}},methods:{onSuccess:function(e,t,r,a){if("1"!=t.state)return this.$message.error(t.message),this.$refs.upload.clearFiles(),!1;r.docId=t.row.id;var o={docId:t.row.id,name:t.row.name},l=[];null!=this.orderData.orderItem&&this.orderData.orderItem.length>0&&this.orderData.orderItem.forEach(function(t){t.code==e.code&&(t.docIdList?t.docIdList.push(o):(l.push(o),t.docIdList=l))})},onError:function(e,t,r){this.rLoading.close(),this.$message.error("上传失败,请重试")},handlePreview:function(e){var t=this.downloadUrl+"?docId="+encodeURI(encodeURI(e.docId))+"&fileName="+e.name,r=document.createElement("a");r.href=t,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(t)},beforeUpload:function(e){return!(e.size/1024/1024>10)||(this.$message.error("上传文件过大，请分开上传，单个文件最大为10M。"),!1)},beforeRemove:function(e,t){return!this.beforeUpload(e)||this.$confirm("确定移除 "+e.name+"？")},handleRemove:function(e,t){null!=this.orderData.orderItem&&this.orderData.orderItem.length>0&&this.orderData.orderItem.forEach(function(r){r.code==e.code&&r.docIdList.some(function(e,a){if(e.docId==t.response.row.id)return r.docIdList.splice(a,1),!0})})},downloadFile:function(e,t){var r=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,a=document.createElement("a");a.href=r,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(r)},getHospital:function(){var e=this,t=this.openLoading(),r=this.$store.getters.curUser.id;this.$http_post(a.default.baseContext+"/supervise/supHospitalUser/userHospital/"+r,{}).then(function(r){t.close(),1==r.state?e.orderData.hospital=r.row:e.$alert(r.message)})},goBack:function(){this.$router.push({name:"generateOrder"})},getPrice:function(e,t){if(e){var r=e.match(/\d+/g);return t?(t/r.join("")).toFixed(3):""}return""},addOrder:function(e,t){t.unitPrice?(t.stockStatus="1",t.systemContrast="1",this.orderData.orderItem.push(t),this.onDrugQuery(),this.drugBox=!1):this.$message.error("该药品暂未设置价格")},delOrder:function(e,t){t.splice(e,1)},showDrugDetail:function(e){this.drugDetail=e,this.showDrugDetailBox=!0},validOrderData:function(){var e=this;if(null==this.orderData.orderItem||0==this.orderData.orderItem.length)return this.$alert("请先选择药品"),!1;var t=0;this.orderData.orderItem.forEach(function(e,r){e.itemPrice=e.amount*e.unitPrice,t+=e.itemPrice}),this.orderData.totalPrice=t,this.$refs.form.validate(function(t){t&&(e.orderBox=!0)})},saveOrderData:function(){var e=this;this.$refs.confirmOrderDataForm.validate(function(t){if(t){var r=e.openLoading("订单提交中...");e.$http_post(a.default.baseContext+"/supervise/supOrder/saveHospitalOrder",e.orderData,!0).then(function(t){1==t.state?(r.close(),e.orderBox=!1,e.$confirm("下单成功，前往【订单列表】查看订单详情",{confirmButtonText:"确定",showCancelButton:!1,type:"success"}).then(function(){e.$router.push({path:"generateOrder"})}).catch(function(e){console.log(e)})):(r.close(),e.$alert(t.message))})}})},onDrugSearch:function(e){"reset"==e?(this.drug.params.country="2",this.drug.params.catalogName="",this.drug.params.specs="",this.drug.params.dosageForm="",this.drug.params.category="",this.onDrugQuery()):(this.drug.params.page=1,this.onDrugQuery())},onDrugQuery:function(){var e=this,t=this.drug.params;this.drugBox=!0;var r=a.default.baseContext+"/supervise/supDrugDetail/getSupDrugList",o=this.openLoading();this.$http_post(r,t).then(function(t){1==t.state?(null!=e.orderData.orderItem&&e.orderData.orderItem.length>0&&e.orderData.orderItem.forEach(function(e){t.rows.forEach(function(t){e.code==t.code&&(t.isSelected=!0)})}),e.drug.list=t.rows,e.drug.params.records=t.records):e.$alert(t.message),o.close()})},onDrugPageClick:function(e){this.drug.params.page=e,this.onDrugQuery()}},beforeDestroy:function(){window.onresize=null}}},Ef3D:function(e,t,r){},Mp0c:function(e,t,r){"use strict";var a=r("TdpW");r.o(a,"render")&&r.d(t,"render",function(){return a.render}),r.o(a,"staticRenderFns")&&r.d(t,"staticRenderFns",function(){return a.staticRenderFns})},TdpW:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"top flex-row"},[t("span",[e._v("已选择药品列表")]),t("div",[t("el-button",{staticClass:"item",attrs:{type:"info",plain:""},on:{click:e.goBack}},[e._v("返回")]),t("el-button",{attrs:{type:"success"},on:{click:e.validOrderData}},[e._v("提交订单")]),t("el-button",{attrs:{type:"primary"},on:{click:e.onDrugQuery}},[e._v("选择药品")])],1)]),t("div",[t("el-form",{ref:"form",attrs:{id:"orderData",rules:e.rules,model:e.orderData}},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.orderData.orderItem,"highlight-current-row":"",height:e.tableHeight}},[t("el-table-column",{attrs:{prop:"busiCode",label:"药品编码",width:"130",align:"left"}}),t("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150",align:"left"}}),t("el-table-column",{attrs:{prop:"category",label:"类别",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return["1"==r.row.category?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("中药")])],1):e._e(),"2"==r.row.category?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("西药")])],1):e._e(),"1"!=r.row.category&&"2"!=r.row.category?t("div",[t("el-tag",{attrs:{type:"info"}},[e._v("其它")])],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"specs",label:"规格",align:"left",width:"100"}}),t("el-table-column",{attrs:{prop:"packingSpecs",label:"包装规格",width:"100",align:"left"}}),t("el-table-column",{attrs:{prop:"dosageForm",label:"剂型",width:"100",align:"left"}}),t("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",align:"left",width:"120","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{label:"最小单位价(元)",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n            "+e._s(e.getPrice(t.row.packingSpecs,t.row.unitPrice))+"\n          ")]}}])}),t("el-table-column",{attrs:{prop:"unitPrice",label:"商品价(元)",width:"100",align:"center"}}),t("el-table-column",{attrs:{prop:"amount",label:"数量",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-form-item",{attrs:{prop:"orderItem."+r.$index+".amount",rules:e.rules.amount}},[t("el-input",{attrs:{type:"number",placeholder:"数量"},model:{value:r.row.amount,callback:function(t){e.$set(r.row,"amount",e._n(t))},expression:"scope.row.amount"}})],1)]}}])}),t("el-table-column",{attrs:{prop:"fileList",label:"凭证信息",width:"180"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-form-item",[t("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{headers:e.headers,action:e.uploadUrl,"on-preview":e.handlePreview,"on-remove":function(t){return e.handleRemove(r.row,t)},"before-upload":e.beforeUpload,"before-remove":e.beforeRemove,"on-success":function(t,a,o){return e.onSuccess(r.row,t,a,o)},"on-error":e.onError,accept:".gif,.jpg,.jpeg,.bmp,.png,.doc,.docx,.txt,.xlsx,.xls,.pdf",multiple:"","file-list":e.fileList}},[t("el-button",{attrs:{size:"small",type:"primary",plain:""}},[e._v("点击上传")])],1)],1)]}}])}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"200"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{attrs:{type:"warning",size:"small"},on:{click:function(t){return e.delOrder(r.$index,e.orderData.orderItem)}}},[e._v("删除")]),t("el-button",{attrs:{type:"info",size:"small"},on:{click:function(t){return e.showDrugDetail(r.row)}}},[e._v("查看详情")])]}}])})],1)],1)],1),e.drugBox?t("el-dialog",{attrs:{title:"选择药品",visible:e.drugBox,top:"8vh","close-on-click-modal":!1,"close-on-press-escape":!1,width:"90%"},on:{"update:visible":function(t){e.drugBox=t}}},[t("div",{staticClass:"dialog-content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"},[t("span",[e._v("通用名")]),t("el-input",{attrs:{placeholder:"请输入通用名"},model:{value:e.drug.params.catalogName,callback:function(t){e.$set(e.drug.params,"catalogName",t)},expression:"drug.params.catalogName"}}),t("span",[e._v("药品规格")]),t("el-input",{attrs:{placeholder:"请输入药品规格"},model:{value:e.drug.params.specs,callback:function(t){e.$set(e.drug.params,"specs",t)},expression:"drug.params.specs"}}),t("span",[e._v("剂型")]),t("el-input",{attrs:{placeholder:"请输入药品剂型"},model:{value:e.drug.params.dosageForm,callback:function(t){e.$set(e.drug.params,"dosageForm",t)},expression:"drug.params.dosageForm"}}),t("span",[e._v("类别")]),t("el-select",{attrs:{placeholder:"请选择药品类别"},model:{value:e.drug.params.category,callback:function(t){e.$set(e.drug.params,"category",t)},expression:"drug.params.category"}},[t("el-option",{attrs:{label:"全部",value:""}}),t("el-option",{attrs:{label:"中药",value:"1"}}),t("el-option",{attrs:{label:"西药",value:"2"}})],1),t("el-button",{attrs:{type:"primary",plain:"",size:"medium",icon:"el-icon-search"},on:{click:e.onDrugSearch}},[e._v("\n            查询\n          ")]),t("el-button",{attrs:{size:"medium",plain:"",icon:"el-icon-search"},on:{click:function(t){return e.onDrugSearch("reset")}}},[e._v("重置\n          ")])],1)]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.drug.list,"highlight-current-row":"",height:.85*e.tableHeight}},[t("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"100",align:"left"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-tag",{attrs:{type:"success"}},[e._v("线下采购")])]}}],null,!1,3954887128)}),t("el-table-column",{attrs:{prop:"busiCode",label:"药品编码",width:"100"}}),t("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150"}}),t("el-table-column",{attrs:{prop:"dosageForm",label:"剂型"}}),t("el-table-column",{attrs:{prop:"specs",label:"规格"}}),t("el-table-column",{attrs:{prop:"packingSpecs",label:"包装规格"}}),t("el-table-column",{attrs:{prop:"unit",label:"单位"}}),t("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"200"}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"最小单位价(元)",width:"120"},scopedSlots:e._u([{key:"default",fn:function(r){return[e.getPrice(r.row.packingSpecs,r.row.unitPrice)?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v(e._s(e.getPrice(r.row.packingSpecs,r.row.unitPrice)))])],1):t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("暂无")])],1)]}}],null,!1,3933721978)}),t("el-table-column",{attrs:{prop:"unitPrice",label:"商品价(元)"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.unitPrice?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v(e._s(r.row.unitPrice))])],1):t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("暂无")])],1)]}}],null,!1,1209003898)}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(r){return[1==r.row.isSelected?t("el-tag",{attrs:{type:"info",round:"",plain:"",size:"small"}},[e._v("已选择\n                ")]):e._e(),1!=r.row.isSelected?t("el-button",{attrs:{type:"primary",round:"",plain:"",size:"small"},on:{click:function(t){return e.addOrder(r.$index,r.row)}}},[e._v("选择药品\n                ")]):e._e()]}}],null,!1,861087298)})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.drug.params.records,"page-size":e.drug.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onDrugPageClick}})],1)],1)])],1),t("div",{staticClass:"orderBox-dialog-footer flex-row"},[t("span"),t("span",[t("el-button",{on:{click:function(t){e.drugBox=!1}}},[e._v("关闭")])],1)])]):e._e(),e.orderBox?t("el-dialog",{attrs:{title:"确认订单",visible:e.orderBox,"close-on-click-modal":!1,"close-on-press-escape":!1,width:"90%"},on:{"update:visible":function(t){e.orderBox=t}}},[t("div",{staticClass:"orderBox-dialog-content"},[t("el-form",{ref:"confirmOrderDataForm",staticClass:"item-form",attrs:{id:"confirmOrderData",rules:e.rules,model:e.orderData,"label-width":"90px"}},[t("div",{staticStyle:{"font-size":"16px",margin:"5px"}},[t("span",[e._v("【 线下采购】订单 :")])]),t("div",{staticClass:"orderBox-content"},[t("div",{staticClass:"header-table"},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.orderData.orderItem,border:"","highlight-current-row":""}},[t("el-table-column",{attrs:{prop:"busiCode",label:"药品编码",width:"150"}}),t("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"200"}}),t("el-table-column",{attrs:{prop:"category",label:"类别"},scopedSlots:e._u([{key:"default",fn:function(r){return["1"==r.row.category?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("中药")])],1):e._e(),"2"==r.row.category?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("西药")])],1):e._e(),"1"!=r.row.category&&"2"!=r.row.category?t("div",[t("el-tag",{attrs:{type:"info"}},[e._v("其它")])],1):e._e()]}}],null,!1,*********)}),t("el-table-column",{attrs:{prop:"specs",label:"规格"}}),t("el-table-column",{attrs:{prop:"packingSpecs",label:"包装规格"}}),t("el-table-column",{attrs:{prop:"dosageForm",label:"剂型"}}),t("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"120","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"最小单位价(元)",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                  "+e._s(e.getPrice(t.row.packingSpecs,t.row.unitPrice))+"\n                ")]}}],null,!1,2736697647)}),t("el-table-column",{attrs:{prop:"unitPrice",label:"商品价(元)",width:"110"}}),t("el-table-column",{attrs:{prop:"amount",label:"采购数量",width:"100"}}),t("el-table-column",{attrs:{prop:"itemPrice",label:"价格(元)",width:"100"}}),t("el-table-column",{attrs:{prop:"docIdList",label:"凭证信息",width:"200"},scopedSlots:e._u([{key:"default",fn:function(r){return e._l(r.row.docIdList||"[]",function(r,a){return t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.downloadFile(r.docId,r.name)}}},[e._v("\n                    "+e._s(r.name)+"\n                  ")])})}}],null,!1,3671547641)})],1)],1),t("div",{staticClass:"fromBox",staticStyle:{"margin-top":"5px"}},[t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"订单号",prop:"orderNum"}},[t("el-input",{attrs:{placeholder:"若不输入,系统将自动生成订单号"},model:{value:e.orderData.orderNum,callback:function(t){e.$set(e.orderData,"orderNum",t)},expression:"orderData.orderNum"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"医疗机构"}},[t("el-input",{attrs:{placeholder:"请输入医疗机构"},model:{value:e.orderData.hospital.name,callback:function(t){e.$set(e.orderData.hospital,"name",t)},expression:"orderData.hospital.name"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"发票代码",prop:"invoiceCode"}},[t("el-input",{attrs:{placeholder:"请输入发票代码"},model:{value:e.orderData.invoiceCode,callback:function(t){e.$set(e.orderData,"invoiceCode",t)},expression:"orderData.invoiceCode"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"发票号",prop:"invoiceNo"}},[t("el-input",{attrs:{placeholder:"请输入发票号"},model:{value:e.orderData.invoiceNo,callback:function(t){e.$set(e.orderData,"invoiceNo",t)},expression:"orderData.invoiceNo"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"采购时间",prop:"submitTime"}},[t("el-date-picker",{attrs:{type:"date",placeholder:"选择日期时间"},model:{value:e.orderData.submitTime,callback:function(t){e.$set(e.orderData,"submitTime",t)},expression:"orderData.submitTime"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"配送企业",prop:"deliveryName"}},[t("el-input",{attrs:{placeholder:"请输入配送企业"},model:{value:e.orderData.deliveryName,callback:function(t){e.$set(e.orderData,"deliveryName",t)},expression:"orderData.deliveryName"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"地址",prop:"address"}},[t("el-input",{attrs:{placeholder:"请输入订单地址"},model:{value:e.orderData.address,callback:function(t){e.$set(e.orderData,"address",t)},expression:"orderData.address"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"合计(元)",prop:"totalPrice"}},[t("el-input",{model:{value:e.orderData.totalPrice,callback:function(t){e.$set(e.orderData,"totalPrice",t)},expression:"orderData.totalPrice"}})],1)],1)],1)],1)])])],1),t("div",{staticClass:"orderBox-dialog-footer flex-row"},[t("span"),t("span",[t("el-button",{on:{click:function(t){e.orderBox=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.saveOrderData}},[e._v("确 定")])],1)])]):e._e(),t("el-dialog",{attrs:{title:"【"+e.drugDetail.catalogName+"】药品详情",visible:e.showDrugDetailBox,width:"45%"},on:{"update:visible":function(t){e.showDrugDetailBox=t}}},[t("div",[t("el-form",{ref:"showDrugDetailForm",staticClass:"item-form",attrs:{model:e.drugDetail,"label-width":"110px"}},[t("div",{staticClass:"fromBox"},[t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"采购平台"}},[e._v("\n                线下采购\n              ")])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"药品本位码"}},[e._v("\n                "+e._s(e.drugDetail.standardCode)+"\n              ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"通用名"}},[e._v("\n                "+e._s(e.drugDetail.catalogName)+"\n              ")])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"商品名"}},[e._v("\n                "+e._s(e.drugDetail.goodsName)+"\n              ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"商品价格"}},[e._v("\n                "+e._s(e.drugDetail.unitPrice+"元")+"\n              ")])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"最小单位价"}},[e._v("\n                "+e._s(e.getPrice(e.drugDetail.packingSpecs,e.drugDetail.unitPrice)+"元")+"\n              ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"药品编码"}},[e._v("\n                "+e._s(e.drugDetail.code)+"\n              ")])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"药品类别"}},["1"==e.drugDetail.category?t("span",[e._v("中药")]):e._e(),"2"==e.drugDetail.category?t("span",[e._v("西药")]):e._e(),"1"!=e.drugDetail.category&&"2"!=e.drugDetail.category?t("span",[e._v("其它")]):e._e()])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"剂型"}},[e._v("\n                "+e._s(e.drugDetail.dosageForm)+"\n              ")])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"规格"}},[e._v("\n                "+e._s(e.drugDetail.specs)+"\n              ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"包装规格"}},[e._v("\n                "+e._s(e.drugDetail.packingSpecs)+"\n              ")])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"单位"}},[e._v("\n                "+e._s(e.drugDetail.unit)+"\n              ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"医保目录"}},["1"==e.drugDetail.medicalInsurance?t("span",[e._v("甲类")]):e._e(),"2"==e.drugDetail.medicalInsurance?t("span",[e._v("乙类")]):e._e()])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"国家集中集采"}},["1"==e.drugDetail.country?t("span",[e._v("是")]):e._e(),"0"==e.drugDetail.country?t("span",[e._v("否")]):e._e()])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"生产企业",prop:"company"}},[e._v("\n                "+e._s(e.drugDetail.drugCompanyName)+"\n              ")])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"批准文号"}},[e._v("\n                "+e._s(e.drugDetail.approvalNumber)+"\n              ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{staticClass:"col-left",attrs:{label:"备注"}},[e._v("\n                "+e._s(e.drugDetail.remark)+"\n              ")])],1)],1)],1)])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.showDrugDetailBox=!1,e.drugDetail={}}}},[e._v("关 闭")])],1)])],1)},t.staticRenderFns=[]},ZgdW:function(e,t,r){"use strict";r("Ef3D")},kG5N:function(e,t,r){"use strict";r.r(t);var a=r("Mp0c"),o=r("zWzm");for(var l in o)["default"].indexOf(l)<0&&function(e){r.d(t,e,function(){return o[e]})}(l);r("ZgdW");var s=r("gp09"),n=Object(s.a)(o.default,a.render,a.staticRenderFns,!1,null,"71a2c96e",null);t.default=n.exports},zWzm:function(e,t,r){"use strict";r.r(t);var a=r("6KGN"),o=r.n(a);for(var l in a)["default"].indexOf(l)<0&&function(e){r.d(t,e,function(){return a[e]})}(l);t.default=o.a}}]);