(window.webpackJsonp=window.webpackJsonp||[]).push([[93],{Crjg:function(t,a,i){"use strict";var e=i("W0Lq");i.o(e,"render")&&i.d(a,"render",function(){return e.render}),i.o(e,"staticRenderFns")&&i.d(a,"staticRenderFns",function(){return e.staticRenderFns})},Lrfa:function(t,a,i){},W0Lq:function(t,a){Object.defineProperty(a,"__esModule",{value:!0});a.render=function(){var t=this,a=t._self._c;return a("div",{staticClass:"singleSite"},[a("div",{staticClass:"search-header flex-row"},[a("div",{staticClass:"left"},[a("span",{staticClass:"tilte"},[t._v(t._s(t.title.startDate)+"至"+t._s(t.title.endDate)+t._s(t.title.name)+"统计分析")])]),a("div",{staticClass:"right flex-row"},[a("span",[t._v("医疗机构")]),a("el-select",{staticStyle:{width:"20%"},attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:t.itemParams.hospital,callback:function(a){t.$set(t.itemParams,"hospital",a)},expression:"itemParams.hospital"}},t._l(t.hospitalList,function(t){return a("el-option",{key:t.name,attrs:{label:t.name,value:t.id+","+t.name}})}),1),a("span",[t._v("订单时间")]),a("el-date-picker",{staticStyle:{width:"20%","margin-right":"10px"},attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"monthrange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:t.itemParams.submitTime,callback:function(a){t.$set(t.itemParams,"submitTime",a)},expression:"itemParams.submitTime"}}),a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:function(a){return t.getNum()}}},[t._v("查询")])],1)]),a("div",{staticClass:"overview"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"change felx flex-sb"},[a("div",{staticClass:"detail1",staticStyle:{width:"60%"}},[a("div",{staticClass:"title flex flex-row"},[a("div",{staticClass:"icon flex flex-row"},[a("i",{staticClass:"iconfont icon-zongrenkou"})]),a("span",[t._v("采购金额(元)")])]),a("div",{staticClass:"dataView"},[a("ul",{staticClass:"flex flex-row"},[a("li",[a("div",[a("p",[t._v(t._s(t.cityData.totalPrice))]),a("span",[t._v("总金额")])])]),a("li",[a("div",[a("p",[t._v(t._s(t.cityData.gdTotalPrice+"("+this.totalMix.gdPrice+"%)"))]),a("span",[t._v("省平台")])])]),a("li",[a("div",[a("p",[t._v(t._s(t.cityData.szTotalPrice+"("+this.totalMix.szPrice+"%)"))]),a("span",[t._v("深圳平台")])])]),a("li",[a("div",[a("p",[t._v(t._s(t.cityData.gzTotalPrice+"("+this.totalMix.gzPrice+"%)"))]),a("span",[t._v("广州平台")])])]),a("li",{staticStyle:{width:"0"}},[a("div")])])]),a("div",{staticClass:"bg"},[a("div",{staticClass:"bg1"}),a("div",{staticClass:"bg2"})])]),a("div",{staticClass:"detail2",staticStyle:{width:"40%"}},[a("div",{staticClass:"title flex flex-row"},[a("div",{staticClass:"icon flex flex-row"},[a("i",{staticClass:"iconfont icon-zengjia1"})]),a("span",[t._v("采购品种")])]),a("div",{staticClass:"dataView"},[a("ul",{staticClass:"flex flex-row"},[a("li",[a("div",[a("p",[t._v(t._s(t.cityData.totalCount))]),a("span",[t._v("总品种")])])]),a("li",[a("div",[a("p",[t._v(t._s(t.cityData.gdTotalCount+"("+this.totalMix.gdCount+"%)"))]),a("span",[t._v("省平台")])])]),a("li",[a("div",[a("p",[t._v(t._s(t.cityData.szTotalCount+"("+this.totalMix.szCount+"%)"))]),a("span",[t._v("深圳平台")])])]),a("li",[a("div",[a("p",[t._v(t._s(t.cityData.gzTotalCount+"("+this.totalMix.gzCount+"%)"))]),a("span",[t._v("广州平台")])])]),a("li",{staticStyle:{width:"0"}})])]),a("div",{staticClass:"bg"},[a("div",{staticClass:"bg1"}),a("div",{staticClass:"bg2"})])])])])],1)],1),a("div",{staticClass:"dataview"},[a("el-row",[a("el-col",{staticClass:"data-l",attrs:{span:16}},[a("div",{staticClass:"grid-content bg-purple"},[a("div",[a("h2",[t._v("采购金额趋势(元)")]),a("div",{ref:"orderPriceEcharts",staticClass:"bar-chart"})])])]),a("el-col",{staticClass:"data-2",attrs:{span:8}},[a("div",{staticClass:"grid-content bg-purple"},[a("div",[a("h2",[t._v("异常明细比例")]),a("div",{ref:"warringOrderItemEcharts",staticClass:"bar-chart"})])]),a("div",{staticClass:"grid-content bg-purple",staticStyle:{"margin-top":"10px"}},[a("div",[a("h2",[t._v("采购品种趋势")]),a("div",{ref:"orderAmountEcharts",staticClass:"bar-chart"})])])])],1)],1)])},a.staticRenderFns=[]},nvka:function(t,a,i){"use strict";i.r(a);var e=i("Crjg"),s=i("nzNd");for(var r in s)["default"].indexOf(r)<0&&function(t){i.d(a,t,function(){return s[t]})}(r);i("z5kU");var o=i("gp09"),l=Object(o.a)(s.default,e.render,e.staticRenderFns,!1,null,"26d73870",null);a.default=l.exports},nzNd:function(t,a,i){"use strict";i.r(a);var e=i("skCJ"),s=i.n(e);for(var r in e)["default"].indexOf(r)<0&&function(t){i.d(a,t,function(){return e[t]})}(r);a.default=s.a},skCJ:function(t,a,i){Object.defineProperty(a,"__esModule",{value:!0});var e=r(i("DWNM")),s=r(i("Q9c5"));function r(t){return t&&t.__esModule?t:{default:t}}a.default={mixins:[e.default],name:"orderStatistics",data:function(){return{cityData:{},drugSourceList:[],myChart:"",itemParams:{submitTime:[],hospital:""},title:{startDate:"",endDate:"",name:""},totalMix:{gdPrice:"",szPrice:"",gzPrice:"",offlinePrice:"",gdCount:"",szCount:"",gzCount:"",offlineCount:""},hospitalList:[],pickerOptions:{shortcuts:[{text:"最近六个月",onClick:function(t){var a=new Date,i=new Date;i.setMonth(i.getMonth()-6),t.$emit("pick",[i,a])}},{text:"最近一年",onClick:function(t){var a=new Date,i=new Date;i.setMonth(i.getMonth()-12),t.$emit("pick",[i,a])}},{text:"本年至今",onClick:function(t){var a=new Date,i=new Date((new Date).getFullYear(),0);t.$emit("pick",[i,a])}}]}}},watch:{},mounted:function(){this.initSubmitTime(),this.getDictItem("SOURCE"),this.getHospitalList()},methods:{initSubmitTime:function(){var t=new Date,a=new Date;a.setMonth(a.getMonth()-6),this.itemParams.submitTime=[a,t],this.getNum()},getHospitalList:function(){var t=this,a=s.default.baseContext+"/supervise/supHospital/all";this.$http_post(a,{}).then(function(a){1==a.state?t.hospitalList=a.row:t.$alert(a.message)})},getDictItem:function(t){var a=this,i=this.openLoading();this.$http_post(s.default.baseContext+"/supervise/supDictItem/getItemList",{code:t}).then(function(e){var s=e.rows;1==e.state?("SOURCE"==t&&(a.drugSourceList=s),i.close()):(i.close(),a.$message.error(e.message))})},getDictItemName:function(t){for(var a=0;a<this.drugSourceList.length;a++)if(this.drugSourceList[a].value==t)return this.drugSourceList[a].label},getDictItemValue:function(t){for(var a=0;a<this.drugSourceList.length;a++)if(this.drugSourceList[a].label==t)return this.drugSourceList[a].value},showOrderList:function(t,a,i){this.itemParams.type=t,this.itemParams.hospitalName=a,this.itemParams.source=i,this.$router.push({path:"orderStatisticsList",query:this.itemParams})},getNum:function(){var t=this;console.log(this.itemParams),this.title.startDate=this.itemParams.submitTime[0].format("yyyy-MM"),this.title.endDate=this.itemParams.submitTime[1].format("yyyy-MM"),this.title.name=this.itemParams.hospital?this.itemParams.hospital.split(",")[1]:"江门市",this.itemParams.hospitalId=this.itemParams.hospital?this.itemParams.hospital.split(",")[0]:"";var a=this.openLoading();this.$http_post(s.default.baseContext+"/supervise/statistics/getCityStatistics",this.itemParams).then(function(i){1==i.state?null!=i.row?(t.cityData=i.row.cityData,t.totalMix.gdPrice=t.cityData.gdTotalPrice/t.cityData.totalPrice,t.totalMix.gdPrice=Number(100*t.totalMix.gdPrice).toFixed(1),t.totalMix.szPrice=t.cityData.szTotalPrice/t.cityData.totalPrice,t.totalMix.szPrice=Number(100*t.totalMix.szPrice).toFixed(1),t.totalMix.gzPrice=t.cityData.gzTotalPrice/t.cityData.totalPrice,t.totalMix.gzPrice=Number(100*t.totalMix.gzPrice).toFixed(1),t.totalMix.offlinePrice=t.cityData.offlineTotalPrice/t.cityData.totalPrice,t.totalMix.offlinePrice=Number(100*t.totalMix.offlinePrice).toFixed(1),t.totalMix.gdCount=t.cityData.gdTotalCount/t.cityData.totalCount,t.totalMix.gdCount=Number(100*t.totalMix.gdCount).toFixed(1),t.totalMix.szCount=t.cityData.szTotalCount/t.cityData.totalCount,t.totalMix.szCount=Number(100*t.totalMix.szCount).toFixed(1),t.totalMix.gzCount=t.cityData.gzTotalCount/t.cityData.totalCount,t.totalMix.gzCount=Number(100*t.totalMix.gzCount).toFixed(1),t.totalMix.offlineCount=t.cityData.offlineTotalCount/t.cityData.totalCount,t.totalMix.offlineCount=Number(100*t.totalMix.offlineCount).toFixed(1),t.initOrderPriceEcharts(i.row.orderPriceData),t.initWarringOrderItemEcharts(i.row.cityData),t.initOrderAmountData(i.row.orderAmountData)):t.$message.error("系统异常"):null!=i.message?t.$message.error(i.message):t.$message.error("系统异常"),a.close()}).catch(function(t){a.close(),console.log(t)})},initOrderPriceEcharts:function(t){var a=this,i=[],e=[],s=[],r=[],o=[],l=[];t.forEach(function(t){i.push(t.submitTime),e.push(t.totalPrice),s.push(t.gdTotalPrice),r.push(t.szTotalPrice),o.push(t.gzTotalPrice),l.push(t.offlineTotalPrice)});var n={title:{text:""},tooltip:{trigger:"axis"},legend:{data:["总采购金额","省平台采购金额","深圳平台采购金额","广州平台采购金额","线下采购金额"]},toolbox:{show:!0,feature:{saveAsImage:{show:!0}}},calculable:!0,xAxis:[{axisLabel:{interval:0,rotate:45,fontSize:14},type:"category",boundaryGap:!1,data:i}],grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},yAxis:[{axisLabel:{formatter:"{value} ",fontSize:14},type:"value"}],series:[{name:"总采购金额",type:"line",data:e,smooth:!0,areaStyle:{color:"rgba(105,164,246,0.5)"},itemStyle:{color:"rgba(105,164,246,0.5)"}},{name:"省平台采购金额",type:"line",data:s,smooth:!0,areaStyle:{color:"rgba(244,185,74,0.5)"},itemStyle:{color:"rgba(244,185,74,0.5)"}},{name:"深圳平台采购金额",type:"line",data:r,smooth:!0,areaStyle:{color:"rgba(212,113,110,0.5)"},itemStyle:{color:"rgba(212,113,110,0.5)"}},{name:"广州平台采购金额",type:"line",data:o,smooth:!0,areaStyle:{color:"rgba(170,232,198,0.5)"},itemStyle:{color:"rgba(170,232,198,0.5)"}},{name:"线下采购金额",type:"line",data:l,smooth:!0,areaStyle:{color:"rgba(220,131,230,0.5)"},itemStyle:{color:"rgba(220,131,230,0.5)"}}]};this.myChart=this.$echarts.init(this.$refs.orderPriceEcharts),this.myChart.setOption(n),window.onresize=function(){a.myChart.resize()}},initWarringOrderItemEcharts:function(t){var a=this,i={title:{text:" "},tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{orient:"vertical",left:"right",data:["正常明细数","非推荐异常数","超时未配送异常数","超时未入库异常数","超时未支付异常数"]},color:["rgba(0,128,0,0.8)","rgba(256,165,0,0.8)","rgba(175,175,36,0.8)","rgba(105,164,246,0.8)","rgba(194,53,49,0.8)"],series:[{name:"订单明细比例",type:"pie",radius:"55%",center:["35%","60%"],data:[{value:t.zcItemCount,name:"正常明细数"},{value:t.tjItemCount,name:"非推荐异常数"},{value:t.psItemCount,name:"超时未配送异常数"},{value:t.rkItemCount,name:"超时未入库异常数"},{value:t.zfItemCount,name:"超时未支付异常数"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 1)"}}}]};this.myChart=this.$echarts.init(this.$refs.warringOrderItemEcharts),this.myChart.setOption(i),window.onresize=function(){a.myChart.resize()}},initOrderAmountData:function(t){var a=this,i=[],e=[];t.forEach(function(t){i.push(t.submitTime),e.push(t.drugSum)});var s={title:{text:""},tooltip:{trigger:"axis"},legend:{data:["总采购品种数"]},toolbox:{show:!0,feature:{saveAsImage:{show:!0}}},calculable:!0,xAxis:[{axisLabel:{interval:0,rotate:45,fontSize:14},type:"category",boundaryGap:!1,data:i}],grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},yAxis:[{axisLabel:{formatter:"{value} ",fontSize:14},type:"value"}],series:[{name:"总采购品种数",type:"line",data:e,smooth:!0,areaStyle:{color:"rgba(220,131,230,0.5)"},itemStyle:{color:"rgba(220,131,230,0.5)"}}]};this.myChart=this.$echarts.init(this.$refs.orderAmountEcharts),this.myChart.setOption(s),window.onresize=function(){a.myChart.resize()}},created:function(){window.addEventListener("resize",this.chartsHeight)},chartsHeight:function(){void 0!=this.myChart&&""!=this.myChart&&this.myChart.resize()}}}},z5kU:function(t,a,i){"use strict";i("Lrfa")}}]);