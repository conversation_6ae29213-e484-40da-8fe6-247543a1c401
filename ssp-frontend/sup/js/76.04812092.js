(window.webpackJsonp=window.webpackJsonp||[]).push([[76],{"1Xvx":function(t,e,a){"use strict";var r=a("OW2d");a.o(r,"render")&&a.d(e,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return r.staticRenderFns})},"6JZe":function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var r=n(a("Q9c5")),s=n(a("DWNM"));function n(t){return t&&t.__esModule?t:{default:t}}e.default={name:"countryPurchase",mixins:[s.default],data:function(){return{drugSourceList:[],warningData:[],tableHeight:100,dialogVisible:!1,dataList:[],params:{page:1,limit:10,records:0,orderNum:"",warning:"",status:""},orderData:{data:{},orderItem:[],hospital:{}}}},props:{},computed:{},watch:{},methods:{getDictItemName:function(t){for(var e=0;e<this.drugSourceList.length;e++)if(this.drugSourceList[e].value==t)return this.drugSourceList[e].label},getWaringType:function(t){for(var e=0;e<this.warningData.length;e++)if(this.warningData[e].value==t)return this.warningData[e].label},getWaring:function(t){for(var e=[],a=t.split(","),r=0;r<a.length;r++){var s=this.getWaringType(a[r]);s&&e.push({name:s})}return e},getDictItem:function(t){var e=this,a=this.openLoading();this.$http_post(r.default.baseContext+"/supervise/supDictItem/getItemList",{code:t}).then(function(r){var s=r.rows;1==r.state?("WARNING"==t&&(e.warningData=s),"SOURCE"==t&&(e.drugSourceList=s),a.close()):(a.close(),e.$message.error(r.message))})},showOrder:function(t){this.$router.push({name:"orderDetail",query:{orderId:t.id}})},onSearch:function(t){"reset"==t?(this.params.orderNum="",this.params.warning="",this.params.status="",this.onQuery()):""!=this.params.orderNum||""!=this.params.warning||""!=this.params.status?(this.params.page=1,this.onQuery()):this.$message.warning("请输入查询条件")},onPageClick:function(t){this.params.page=t,this.onQuery()},onQuery:function(){var t=this,e=this.params,a=this.openLoading(),s=r.default.baseContext+"/supervise/supOrder/list";this.$http_post(s,e).then(function(e){1==e.state?(t.dataList=e.rows,t.params.records=e.records,a.close()):(a.close(),t.$alert(e.message))})},time:function(t,e){if(void 0==t.submitTime||""==t.submitTime)return"";var a=new Date(t.submitTime);return a.getFullYear()+"-"+((a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-")+(a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ")+(a.getHours()<10?"0"+a.getHours()+":":a.getHours()+":")+(a.getMinutes()<10?"0"+a.getMinutes()+":":a.getMinutes()+":")+(a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds())},init:function(){}},mounted:function(){var t=this;this.$route.query.warning&&(this.params.warning=this.$route.query.warning),this.onQuery(),this.getDictItem("WARNING"),this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null},filters:{formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}}}},"9Vz5":function(t,e,a){"use strict";a.r(e);var r=a("1Xvx"),s=a("inob");for(var n in s)["default"].indexOf(n)<0&&function(t){a.d(e,t,function(){return s[t]})}(n);a("D+za");var o=a("gp09"),l=Object(o.a)(s.default,r.render,r.staticRenderFns,!1,null,"24c9a557",null);e.default=l.exports},"D+za":function(t,e,a){"use strict";a("H6iS")},H6iS:function(t,e,a){},OW2d:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left flex-row"},[e("span",[t._v("订单号")]),e("el-input",{attrs:{placeholder:"请输入订单号"},model:{value:t.params.orderNum,callback:function(e){t.$set(t.params,"orderNum",e)},expression:"params.orderNum"}}),e("span",[t._v("预警状态")]),e("el-select",{attrs:{placeholder:"请选择状态"},model:{value:t.params.warning,callback:function(e){t.$set(t.params,"warning",e)},expression:"params.warning"}},t._l(t.warningData,function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1),e("span",[t._v("订单状态")]),e("el-select",{attrs:{placeholder:"全部"},model:{value:t.params.status,callback:function(e){t.$set(t.params,"status",e)},expression:"params.status"}},[e("el-option",{key:"",attrs:{label:"全部",value:""}}),e("el-option",{key:"1",attrs:{label:"已提交",value:"1"}}),e("el-option",{key:"2",attrs:{label:"已完成",value:"2"}})],1),e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1)]),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.dataList,"highlight-current-row":"",height:t.tableHeight}},[e("el-table-column",{attrs:{prop:"orderNum",label:"订单号",align:"center",width:"230"}}),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",align:"center",width:"220"}}),e("el-table-column",{attrs:{prop:"totalPrice",label:"订单总金额(元)",align:"center",width:"200"}}),e("el-table-column",{attrs:{prop:"status",label:"订单状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return["0"==a.row.status?e("span",[t._v("暂存")]):t._e(),"1"==a.row.status?e("span",[t._v("已提交")]):t._e(),"2"==a.row.status?e("span",[t._v("已完成")]):t._e()]}}])}),e("el-table-column",{attrs:{prop:"paySurplus",label:"所剩支付天数",align:"center",width:"120"}}),e("el-table-column",{attrs:{prop:"payStatus",label:"支付状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return["0"!=a.row.payStatus&&a.row.payStatus||a.row.stockStatus&&"0"!=a.row.stockStatus?t._e():e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("未支付")])],1),"0"!=a.row.payStatus&&a.row.payStatus||!a.row.stockStatus||"0"==a.row.stockStatus?t._e():e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("未支付")])],1),"1"==a.row.payStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已支付")])],1):t._e(),"2"==a.row.payStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分支付")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"payStatus",label:"入库状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.stockStatus&&"0"!=a.row.stockStatus?t._e():e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("未入库")])],1),"1"==a.row.stockStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已入库")])],1):t._e(),"2"==a.row.stockStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分入库")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"warning",label:"预警状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.warning?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("正常")])],1):t._e(),"1"!=a.row.warning?e("div",[e("el-popover",{attrs:{placement:"top-start",width:"250",trigger:"hover"}},[t._l(t.getWaring(a.row.warning),function(a,r){return e("div",{key:a.itemValue,staticClass:"text item"},[t._v("\n                                        "+t._s(r+1+"、"+a.name)+"\n                                    ")])}),e("el-tag",{attrs:{slot:"reference",type:"danger"},slot:"reference"},[t._v("查看预警")])],2)],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"submitTime",formatter:t.time,label:"采购时间",align:"center"}}),e("el-table-column",{attrs:{label:"操作",align:"right",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showOrder(a.row)}}},[t._v("查看详情")])]}}])})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)]),e("el-dialog",{attrs:{title:"【"+t.orderData.data.orderNum+"】订单详情",visible:t.dialogVisible,top:"10vh",width:"80%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("div",{staticClass:"dialog-content"},[e("el-form",{ref:"orderForm",staticClass:"item-form",attrs:{id:"orderData2",model:t.orderData,"label-width":"90px"}},[e("div",{staticClass:"fromBox",staticStyle:{"margin-bottom":"10px"}},[e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"订单号"}},[t._v("\n                                "+t._s(t.orderData.data.orderNum)+"\n                            ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"医疗机构"}},[t._v("\n                                "+t._s(t.orderData.data.hospitalName)+"\n                            ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"采购时间"}},[e("span"),t._v(t._s(t._f("formatTime")(t.orderData.data.submitTime))+"\n                            ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"合计"}},[t._v("\n                                "+t._s(t.orderData.data.totalPrice)+"元\n                            ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"提交人"}},[t._v("\n                                "+t._s(t.orderData.data.userName)+"元\n                            ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"订单状态"}},["0"==t.orderData.data.status?e("span",[t._v("暂存")]):t._e(),"1"==t.orderData.data.status?e("span",[t._v("已提交")]):t._e(),"2"==t.orderData.data.status?e("span",[t._v("已完成")]):t._e()])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"支付状态"}},["0"==t.orderData.data.payStatus?e("span",[t._v("未支付")]):t._e(),"1"==t.orderData.data.payStatus?e("span",[t._v("已支付")]):t._e()])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"支付时间"}},[t._v("\n                                "+t._s(t._f("formatTime")(t.orderData.data.payTime))+"\n                            ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"预警状态"}},["0"==t.orderData.data.warning?e("span",[t._v("\n                                    正常\n                                ")]):t._e(),"1"==t.orderData.data.warning?e("span",[t._v("\n                                    订单列表存在非系统推荐\n                                ")]):t._e()])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"订单结束时间"}},[t._v("\n                                "+t._s(t._f("formatTime")(t.orderData.data.endTime))+"\n                            ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{"label-width":"140px",label:"医院地址(收货地址)"}},[null!=t.orderData.hospital?e("span",[t._v(t._s(t.orderData.hospital.address))]):t._e()])],1)],1)],1),e("div",[e("span",[t._v("药品订单列表：")]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.orderData.orderItem,border:"","highlight-current-row":""}},[e("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"200"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",[e("el-tag",{attrs:{type:"-1"==a.row.source?"warning":"success"}},[t._v(t._s(t.getDictItemName(a.row.source)))])],1)]}}])}),e("el-table-column",{attrs:{prop:"busiCode",label:"采购平台平台编码",width:"150"}}),e("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"200"}}),e("el-table-column",{attrs:{align:"center",prop:"country",label:"是否国家集中集采"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.country?e("span",[t._v("是")]):e("span",[t._v("否")])]}}])}),e("el-table-column",{attrs:{prop:"category",label:"类别"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.category?e("span",[t._v("中药\n                                ")]):t._e(),"2"==a.row.category?e("span",[t._v("西药\n                                ")]):t._e(),"1"!=a.row.category&&"2"!=a.row.category?e("span",[t._v("\n                                    其它\n                                ")]):t._e()]}}])}),e("el-table-column",{attrs:{prop:"specs",label:"规格"}}),e("el-table-column",{attrs:{prop:"dosageForm",label:"剂型"}}),e("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"200"}}),e("el-table-column",{attrs:{prop:"unitPrice",label:"单价(元)"}}),e("el-table-column",{attrs:{prop:"amount",label:"采购数量",width:"100"}}),e("el-table-column",{attrs:{prop:"itemPrice",label:"价格(元)",width:"100"}}),e("el-table-column",{attrs:{prop:"systemContrast",label:"是否系统推荐",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.systemContrast?e("span",[t._v("是")]):t._e(),"0"==a.row.systemContrast?e("span",[t._v("否(原因："+t._s(a.row.reason)+")")]):t._e()]}}])})],1)],1)])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")])],1)])],1)},e.staticRenderFns=[]},inob:function(t,e,a){"use strict";a.r(e);var r=a("6JZe"),s=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(t){a.d(e,t,function(){return r[t]})}(n);e.default=s.a}}]);