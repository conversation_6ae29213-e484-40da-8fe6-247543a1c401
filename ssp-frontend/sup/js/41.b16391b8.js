(window.webpackJsonp=window.webpackJsonp||[]).push([[41],{B3fu:function(e,t,a){"use strict";a("D2yo")},D2yo:function(e,t,a){},JkI7:function(e,t,a){"use strict";a.r(t);var o=a("rM63"),s=a("PTGS");for(var r in s)["default"].indexOf(r)<0&&function(e){a.d(t,e,function(){return s[e]})}(r);a("B3fu");var l=a("gp09"),i=Object(l.a)(s.default,o.render,o.staticRenderFns,!1,null,"35dc04da",null);t.default=i.exports},PTGS:function(e,t,a){"use strict";a.r(t);var o=a("YBbn"),s=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,function(){return o[e]})}(r);t.default=s.a},YBbn:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var o=i(a("Q9c5")),s=i(a("DWNM")),r=i(a("XRYr")),l=i(a("rGKd"));function i(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[s.default],data:function(){return{batchList:[],setPriceAble:!1,totalCount:0,drugSourceOption:[],isSetPrice:!0,show:!1,activeName:"first",setPriceDisabled:!1,currentComponent:"",country:"",drugCode:"",isDisabled:!1,uploadData:{source:"",country:""},adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1,importDialogVisible:!1,hospatilImportDialogVisible:!1,provinceImportDialogVisible:!1,brandFold:!1,dataList:[],drugCatalogList:[],params:{hospitalName:"",code:"",specs:"",catalogName:"",page:1,limit:10,records:0,standardCode:"",goodsName:"",source:"",catalogId:"",country:"0",countryType:"",electionScope:"",dosageForm:"",drugCompanyName:"",approvalNumber:"",status:"1"==this.$route.query.country?"":"1",batch:"",medicalInsuranceCode:"",attribute:""},formData:{code:"",drugCompanyName:"",category:"",id:"",source:"",goodsName:"",catalogName:"",catalogId:"",country:"0",countryType:"",electionScope:"",dosageForm:"",specs:"",packingSpecs:"",packing:"",unit:"",qualityLevel:"",company:"",approvalNumber:"",standardCode:"",medicalInsurance:"",attribute:"",adjuvant:"0",net:"0",coefficient:"",remark:"",version:"",status:""},unitList:[{name:"盒",value:"盒"},{name:"支",value:"支"},{name:"片",value:"片"},{name:"丸",value:"丸"},{name:"粒",value:"粒"},{name:"袋",value:"袋"},{name:"瓶",value:"瓶"},{name:"剂",value:"剂"}],categoryList:[{name:"中药",value:"1"},{name:"西药",value:"2"}],drugSourceList:[],hospitalList:[],dialogVisible:!1,tableHeight:550,rules:{catalogId:[{required:!0,message:"请选择或输入通用名",trigger:"change"}],source:[{required:!0,message:"请选择采购平台",trigger:"change"}],category:[{required:!0,message:"请选择药品类别",trigger:"change"}],standardCode:[{required:!0,message:"请输入药品本位码",trigger:"blur"}],specs:[{required:!0,message:"请输入药品规格",trigger:"blur"}]},importRules:{source:[{required:!0,message:"请选择采购平台",trigger:"change"}],country:[{required:!0,message:"请选择是否国集",trigger:"change"}]},isUpload:!1,headers:{},rLoading:{}}},props:{},watch:{$route:function(){this.$route.query.country&&(this.params.country=this.$route.query.country,this.uploadData.country=this.$route.query.country,this.onSearch("reset"),this.getDictItem("SOURCE"),this.getNum()),this.$route.query.setPrice&&(this.params.country="0",this.onSearch("reset"))}},computed:{uploadUrl:function(){return o.default.baseContext+"/file/upload/data"},uploadHospatilUrl:function(){return o.default.baseContext+"/file/upload/hospitalDrugData"},uploadProvinceUrl:function(){return o.default.baseContext+"/file/upload/provinceData"}},methods:{drugDetailExportExcel:function(){var e=this;this.params.country=this.$route.query.country,this.$confirm("确定导出药品目录数据吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.$store.getters.curUser.id||e.$message.error("用户未登录");var t=o.default.baseContext+"/supervise/supDrugDetail/exportDrugDetail";e.$post_blob(t,e.params).then(function(t){if("application/json"==t.type){var a=new FileReader;return a.addEventListener("loadend",function(t){e.$message.error(JSON.parse(t.target.result).message)}),void a.readAsText(t)}var o=URL.createObjectURL(t),s=document.createElement("a");s.href=o,s.target="_blank",0==e.params.country?s.download="非集采药品目录数据.xls":1==e.params.country?s.download="集采药品目录数据.xls":2==e.params.country&&(s.download="线下药品目录数据.xls"),s.click()}).catch(function(e){console.log(e)})}).catch(function(e){console.log(e)})},drugListBySource:function(e){var t={};(t=this.params).source=e,t.country=this.$route.query.country,this.$router.push({name:"drugSourceStatistics",query:t})},formatBatch:function(e){if(void 0==e||""==e)return"";for(var t=0;t<this.batchList.length;t++)if(this.batchList[t].code==e)return this.batchList[t].batchName},getNum:function(){var e=this,t=this.openLoading();this.params.country=this.$route.query.country,this.$http_post(o.default.baseContext+"/supervise/statistics/getDrugCountBySource",this.params).then(function(a){if(1==a.state)if(null!=a.rows){for(var o=a.rows,s=[],r=0,l=0;l<o.length;l++)s.push(o[l]),r+=o[l].count;e.$set(e,"drugSourceOption",s),e.totalCount=r}else e.$message.error("系统异常");else null!=a.message?e.$message.error(a.message):e.$message.error("系统异常");t.close()}).catch(function(e){t.close(),console.log(e)})},selectDrug:function(e){this.params.source=e,this.onQuery()},getDictItem:function(e){var t=this,a=this.openLoading();this.$http_post(o.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(o){var s=o.rows;1==o.state?("SOURCE"==e&&(t.drugSourceList=s),a.close()):(a.close(),t.$message.error(o.message))})},handleClick:function(e,t){this.activeName=e.name,"second"==this.activeName&&(this.setPriceDisabled=!0)},initCurrentComponent:function(){this.currentComponent="drugPrice"},setPrice:function(e){var t=this;this.country=e.country,this.drugCode=e.code,this.show=!0,this.$nextTick(function(){t.$refs.asyncDialog.show=!0})},catalogChange:function(e){var t=this.drugCatalogList.filter(function(t){return t.id==e});this.formData.catalogName=t[0].name,this.formData.category=t[0].category},selectChange:function(e){e.target.value&&(this.formData.catalogName=e.target.value,this.formData.catalogId=e.target.value)},countryChange:function(e){},handleExceed:function(e,t){this.$message.warning("一次只能上传一个Excel文件")},submitUpload:function(){var e=this;this.isUpload?this.$refs.importDataForm.validate(function(t){if(!t)return!1;e.rLoading=e.openLoading("导入中..."),e.$refs.upload.submit()}):this.$message.warning("请先上传文件")},submitHospitalUpload:function(){this.isUpload?(this.rLoading=this.openLoading("导入中..."),this.$refs.hospitalUpload.submit()):this.$message.warning("请先上传文件")},submitProvinceUpload:function(){this.isUpload?(this.rLoading=this.openLoading("导入中..."),this.$refs.provinceUpload.submit()):this.$message.warning("请先上传文件")},beforeUpload:function(e){var t=e.name;return"xlsx"==t.substring(t.length-4)||"xls"==t.substring(t.length-3)||(this.$message.error("上传文件格式只能为【.xlsx】或【.xls】"),!1)},onChange:function(e,t){"ready"==e.status&&(this.isUpload=!0)},beforeRemove:function(e,t){this.isUpload=!1},onSuccess:function(e,t,a){this.rLoading.close(),1==e.state?(this.isUpload=!1,this.importDialogVisible=!1,this.$refs.upload.clearFiles(),this.$alert("导入成功，请前往【药品批次日志】查看导入结果")):(this.isUpload=!1,this.$refs.upload.clearFiles(),this.$alert(e.message)),this.init()},onError:function(e,t,a){this.rLoading.close(),console.log(e),this.$message.error("上传失败,请重试")},drugTemplate:function(){var e=o.default.baseContext+"/file/downloadDrugImportTemplate",t=document.createElement("a");t.href=e,document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(e)},drugImport:function(){this.importDialogVisible=!0},hospitalDrugImport:function(){this.hospatilImportDialogVisible=!0},provinceDrugImport:function(){this.provinceImportDialogVisible=!0},changeFoldState:function(){this.brandFold=!this.brandFold},getHospitalList:function(){var e=this,t=o.default.baseContext+"/supervise/supHospital/all";this.$http_post(t,{}).then(function(t){1==t.state?e.hospitalList=t.row:e.$alert(t.message)})},getDrugCatalogList:function(){var e=this;this.drugCatalogList=[],url=o.default.baseContext+"/supervise/supDrugCatalog/getDrugCatalogList";var t=this.openLoading();this.$http_post(url,null).then(function(a){if(1==a.state){var o=a.rows;console.log("获取通用名集合"),console.log(o);for(var s=0;s<o.length;s++){var r=o[s].category,l="";"1"==r&&(l="中药"),"2"==r&&(l="西药");var i={id:o[s].id,name:o[s].name,category:l};e.drugCatalogList.push(i)}}else e.$alert(a.message);t.close()})},changeStatus:function(e,t){var a=this,s={id:e,status:t},r=this.openLoading(),l=o.default.baseContext+"/supervise/supDrugDetail/updateStatus";this.$http_post(l,s).then(function(e){1==e.state?(a.$message.success("操作成功"),a.init()):a.$alert(e.message),r.close()})},add:function(e,t){var a=this;if("add"==e&&(this.isSetPrice=!1,this.isDisabled=!1,this.formData={drugCompanyName:"",category:"",catalogName:"",id:"",code:"",source:"",goodsName:"",catalogId:"",country:this.$route.query.country,countryType:"",electionScope:"",dosageForm:"",specs:"",packingSpecs:"",packing:"",unit:"",qualityLevel:"",company:"",approvalNumber:"",standardCode:"",medicalInsurance:"",attribute:"",adjuvant:"",net:"",coefficient:"",remark:"",status:"1"},this.dialogVisible=!0,this.activeName="first"),"edit"==e||"show"==e){this.isDisabled=!0,this.isSetPrice="show"==e;var s=this.openLoading();this.$http_post(o.default.baseContext+"/supervise/supDrugDetail/info/"+t.id,{}).then(function(e){1==e.state?null!=e.row?(a.formData={version:e.row.version,catalogName:e.row.catalogName,drugCompanyName:e.row.drugCompanyName,category:e.row.category,id:e.row.id,code:e.row.code,source:e.row.source,catalogId:e.row.catalogId,goodsName:e.row.goodsName,country:e.row.country,countryType:e.row.countryType,electionScope:e.row.electionScope,dosageForm:e.row.dosageForm,specs:e.row.specs,packingSpecs:e.row.packingSpecs,packing:e.row.packing,unit:e.row.unit,qualityLevel:e.row.qualityLevel,company:e.row.company,approvalNumber:e.row.approvalNumber,standardCode:e.row.standardCode,medicalInsurance:e.row.medicalInsurance,attribute:e.row.attribute,adjuvant:e.row.adjuvant,net:e.row.net,coefficient:e.row.coefficient,remark:e.row.remark,status:e.row.status,batch:e.row.batch,medicalInsuranceCode:e.row.medicalInsuranceCode},a.country=e.row.country,a.drugCode=e.row.code,a.setPriceDisabled=!0,a.activeName="first",a.dialogVisible=!0):a.$message.error("系统异常"):null!=e.message?a.$message.error(e.message):a.$message.error("系统异常"),s.close()})}},close:function(){this.show=!1,this.dialogVisible=!1,this.init()},save:function(){var e=this;this.$refs.form.validate(function(t){if(!t)return!1;var a;a=o.default.baseContext+"/supervise/supDrugDetail/edit";var s=e.openLoading();e.$http_post(a,e.formData,!0).then(function(t){s.close(),0==t.state&&e.$alert(t.message),1!=t.state||t.row||(e.$message.success("操作成功"),e.init(),e.close()),1==t.state&&t.row&&e.$alert("该药品已存在，确定升级版本吗","确认信息",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,type:"warning"}).then(function(){e.formData.id=t.row.id,e.formData.code=t.row.code,e.formData.version=t.row.version;var a=e.openLoading();e.$http_post(o.default.baseContext+"/supervise/supDrugDetail/updateVersion",e.formData,!0).then(function(t){a.close(),1==t.state?(e.$message.success("操作成功"),e.init(),e.close()):e.$message.error(t.message)})}).catch(function(e){console.log(e)})})})},del:function(e){var t=this;this.$alert("确定删除【"+e.catalogName+"】数据吗","确认信息",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,type:"warning"}).then(function(){var a=t.openLoading();t.$http_post(o.default.baseContext+"/supervise/supDrugDetail/delete/"+e.id,{source:e.source}).then(function(e){1==e.state?(t.$message.success("删除成功"),t.init(),a.close()):(a.close(),t.$message.error("删除失败，请稍后再试"))})}).catch(function(e){console.log(e)})},onSearch:function(e){"reset"==e?(this.params={hospitalName:"",code:"",specs:"",page:1,limit:10,records:0,standardCode:"",source:"",goodsName:"",catalogId:"",country:"0",countryType:"",electionScope:"",dosageForm:"",drugCompanyName:"",approvalNumber:"",status:"1",catalogName:"",batch:"",medicalInsuranceCode:"",attribute:""},this.init()):(this.params.page=1,this.init()),this.brandFold=!1},onPageClick:function(e){this.params.page=e,this.init()},onQuery:function(){var e=this;!this.$route.query.setPrice&&this.$route.query.country&&(this.params.country=this.$route.query.country);var t=this.params,a=this.openLoading(),s=o.default.baseContext+"/supervise/supDrugDetail/list";this.$http_post(s,t).then(function(t){if(1==t.state){var o=t.rows;e.dataList=o,e.params.records=t.records,a.close()}else a.close(),e.$alert(t.message)})},onHideSearchCon:function(e){for(var t=0;t<e.path.length;t++){var a=e.path[t];if(a==this.$refs.searchCon||a==this.$refs.foldBtn)return}this.brandFold=!1},init:function(){this.getDrugCatalogList(),this.onQuery(),this.getNum()},initRole:function(){var e=this.$store.getters.curUser.roleCode;-1!=e.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=e.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=e.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=e.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)},getDictItemName:function(e){for(var t=0;t<this.drugSourceList.length;t++)if(this.drugSourceList[t].value==e)return this.drugSourceList[t].label},setBatchList:function(e){for(var t=e.rows,a=0;a<t.length;a++)this.batchList.push(t[a])}},mounted:function(){for(var e=this.$store.getters.curUser.roleCode.split(","),t=o.default.smsRole,a=0,s=e.length;a<s;a++)for(var i=0,n=t.length;i<n;i++)e[a]==t[i]&&(this.setPriceAble=!0);var c=r.default.doCloundRequest(o.default.app_key,o.default.app_security,"");this.headers["x-aep-appkey"]=c["x-aep-appkey"],this.headers["x-aep-signature"]=c["x-aep-signature"],this.headers["x-aep-timestamp"]=c["x-aep-timestamp"],this.headers["x-aep-nonce"]=c["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,l.default.getBatchList(this.setBatchList),this.$route.query.country&&(this.uploadData.country=this.$route.query.country,this.getDictItem("SOURCE")),"2"==this.$route.query.country&&this.getHospitalList(),this.init(),this.initRole(),this.initCurrentComponent(),document.addEventListener("click",this.onHideSearchCon)},beforeDestroy:function(){window.onresize=null,document.removeEventListener("click",this.onHideSearchCon)}}},rGKd:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var o=r(a("Q9c5")),s=r(a("ERIh"));function r(e){return e&&e.__esModule?e:{default:e}}var l=o.default.baseContext+"/supervise/supDrugBatch/getBatchList";var i={getBatchList:function(e){s.default.$http_api("GET",l,{},{jsonContentType:!1,responseType:null,addBaseUrl:!0}).then(function(t){1==t.state?function(e){return null!=e&&void 0!==e&&"function"==typeof e}(e)&&e(t):console.warn("查询集采批次失败",t.message)}).catch(function(e){console.warn("查询集采批次失败",e.message)})}};t.default=i},rM63:function(e,t,a){"use strict";var o=a("x9DA");a.o(o,"render")&&a.d(t,"render",function(){return o.render}),a.o(o,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return o.staticRenderFns})},x9DA:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.staticRenderFns=t.render=void 0;var o=function(e){return e&&e.__esModule?e:{default:e}}(a("/umX"));t.render=function(){var e,t=this,a=t._self._c;return a("div",[a("div",{staticClass:"trend"},[a("div",{staticClass:"overview"},[a("el-row",{attrs:{gutter:20}},["1"!=t.$route.query.country&&"2"!=t.$route.query.country?a("el-col",{attrs:{span:6}},[a("div",{staticClass:"change felx flex-sb"},[a("div",{class:"flex flex-row detail1"},[a("div",{staticClass:"icon flex flex-row"},[a("i",{staticClass:"iconfont icon-zongrenkou"})]),a("div",[a("p",[t._v(t._s(t.totalCount))]),a("span",[t._v("总药品数量")])])])])]):t._e(),t._l(t.drugSourceOption,function(e,o){return a("div",{on:{click:function(a){return t.drugListBySource(e.source)}}},[a("el-col",{attrs:{span:6}},[a("div",{staticClass:"change felx flex-sb"},[a("div",{class:"flex flex-row detail"+((o+1)%4==0?4:(o+1)%4)},[a("div",{staticClass:"icon flex flex-row"},[a("i",{staticClass:"iconfont icon-zongrenkou"})]),a("div",[a("p",[t._v(t._s(e.count))]),a("span",[t._v(t._s(e.sourceName+"药品数量"))])])])])])],1)})],2)],1)]),a("div",{ref:"tableH",staticClass:"content"},[a("div",{staticClass:"search-header flex-row"},[a("div",{staticClass:"left"},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("通用名")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入通用名"},model:{value:t.params.catalogName,callback:function(e){t.$set(t.params,"catalogName",e)},expression:"params.catalogName"}})],1)])]),"2"==t.$route.query.country&&(this.adminRole||this.medicalAdmin||this.socialAdmin)?a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("医疗机构")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:(e={size:"small",clearable:""},(0,o.default)(e,"clearable",""),(0,o.default)(e,"filterable",""),(0,o.default)(e,"placeholder","请选择医疗机构"),e),model:{value:t.params.hospitalName,callback:function(e){t.$set(t.params,"hospitalName",e)},expression:"params.hospitalName"}},t._l(t.hospitalList,function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}),1)],1)])]):t._e(),"1"==t.$route.query.country?a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("批次")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择批次"},model:{value:t.params.batch,callback:function(e){t.$set(t.params,"batch",e)},expression:"params.batch"}},t._l(t.batchList,function(e){return a("el-option",{key:e.code,attrs:{label:e.batchName,value:e.code}})}),1)],1)])]):t._e(),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("药品本位码")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入药品本位码"},model:{value:t.params.standardCode,callback:function(e){t.$set(t.params,"standardCode",e)},expression:"params.standardCode"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("医保药品码")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入医保药品代码"},model:{value:t.params.medicalInsuranceCode,callback:function(e){t.$set(t.params,"medicalInsuranceCode",e)},expression:"params.medicalInsuranceCode"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("药品状态")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择药品状态"},model:{value:t.params.status,callback:function(e){t.$set(t.params,"status",e)},expression:"params.status"}},[a("el-option",{attrs:{label:"已上架",value:"1"}}),a("el-option",{attrs:{label:"已下架",value:"0"}})],1)],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("剂型")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入药品剂型"},model:{value:t.params.dosageForm,callback:function(e){t.$set(t.params,"dosageForm",e)},expression:"params.dosageForm"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("规格")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入药品规格"},model:{value:t.params.specs,callback:function(e){t.$set(t.params,"specs",e)},expression:"params.specs"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("生产企业")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入药品生产企业"},model:{value:t.params.drugCompanyName,callback:function(e){t.$set(t.params,"drugCompanyName",e)},expression:"params.drugCompanyName"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("基药属性")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择基药属性"},model:{value:t.params.attribute,callback:function(e){t.$set(t.params,"attribute",e)},expression:"params.attribute"}},[a("el-option",{attrs:{label:"空",value:"0"}}),a("el-option",{attrs:{label:"国基",value:"1"}}),a("el-option",{attrs:{label:"省基",value:"2"}})],1)],1)])])],1)],1),a("div",{staticClass:"right"},[a("div",{staticStyle:{"margin-bottom":"5px"}},[a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),a("el-button",{attrs:{size:"small",type:"primary",plain:"",icon:"el-icon-refresh-left"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1),a("div",{staticStyle:{"margin-bottom":"5px"}},[a("el-button",{attrs:{size:"small",icon:"el-icon-plus",type:"primary"},on:{click:function(e){return t.add("add")}}},[t._v("新增")]),a("el-button",{attrs:{size:"small",type:"warning",icon:"el-icon-download"},on:{click:t.drugImport}},[t._v("导入")])],1),a("div",{staticStyle:{"margin-bottom":"5px","margin-right":"82px"}},[a("el-button",{staticStyle:{width:"215%"},attrs:{size:"small",type:"success",icon:"el-icon-upload"},on:{click:function(e){return t.drugDetailExportExcel("")}}},[t._v("导出")])],1)])]),a("el-row",{staticClass:"con"},[a("div",{staticClass:"con-left"},[a("el-table",{key:Math.random(),staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.dataList,"highlight-current-row":"",height:t.tableHeight,border:""}},[a("el-table-column",{attrs:{prop:"catalogName",label:"通用名",align:"left","min-width":"200px"}}),a("el-table-column",{attrs:{prop:"dosageForm",width:"150",label:"剂型","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"specs",width:"100","show-tooltip-when-overflow":"",label:"规格"}}),a("el-table-column",{attrs:{prop:"unit",width:"80",label:"单位"}}),a("el-table-column",{attrs:{prop:"packingSpecs","show-tooltip-when-overflow":"",label:"包装规格"}}),a("el-table-column",{attrs:{prop:"packing",width:"100","show-tooltip-when-overflow":"",label:"包装材质"}}),a("el-table-column",{attrs:{prop:"attribute",width:"80","show-tooltip-when-overflow":"",label:"基药属性"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",["0"==e.row.attribute?a("div",{attrs:{type:"success"}},[t._v("空")]):t._e(),"1"==e.row.attribute?a("div",{attrs:{type:"success"}},[t._v("国基")]):t._e(),"2"==e.row.attribute?a("div",{attrs:{type:"success"}},[t._v("省基")]):t._e()])]}}])}),"1"==this.params.country||"2"==this.params.country?a("el-table-column",{attrs:{prop:"priceArray",label:"价格(元)",align:"left","min-width":"80px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{type:"info"}},[t._v(t._s(e.row.priceArray&&e.row.priceArray.length>0?e.row.priceArray[0].price:"暂无价格"))])]}}],null,!1,2273702678)}):t._e(),"0"==this.params.country?a("el-table-column",{attrs:{prop:"priceArray",label:"平台价格(元)",align:"left","min-width":"230px"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.priceArray,function(e,o){return a("span",[a("el-tag",{staticStyle:{width:"200px","margin-bottom":"3px"},attrs:{type:"info"}},[t._v(t._s(t.getDictItemName(e.source))+" ： "+t._s(e.price))]),a("br")],1)})}}],null,!1,3118115612)}):t._e(),"0"==t.$route.query.country?a("el-table-column",{attrs:{prop:"source",label:"推荐平台",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",["1"==e.row.source?a("div",{attrs:{type:"success"}},[t._v("深圳平台")]):t._e(),"2"==e.row.source?a("div",{attrs:{type:"success"}},[t._v("省平台")]):t._e(),"3"==e.row.source?a("div",{attrs:{type:"success"}},[t._v("广州平台")]):t._e(),"4"==e.row.source?a("div",{attrs:{type:"success"}},[t._v("线下采购")]):t._e(),"5"==e.row.source?a("div",{attrs:{type:"success"}},[t._v("医院深圳平台")]):t._e(),"6"==e.row.source?a("div",{attrs:{type:"success"}},[t._v("医院省平台")]):t._e(),e.row.source?t._e():a("div",{attrs:{type:"danger"}},[t._v("暂无价格")])])]}}],null,!1,**********)}):t._e(),a("el-table-column",{attrs:{prop:"standardCode",width:"180","show-tooltip-when-overflow":"",label:"药品本位码"}}),a("el-table-column",{attrs:{prop:"medicalInsuranceCode","show-tooltip-when-overflow":"",label:"医保药品代码"}}),a("el-table-column",{attrs:{prop:"qualityLevel",width:"150",label:"质量层次","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"drugCompanyName","min-width":"200px",label:"生产企业","show-tooltip-when-overflow":""}}),"2"==t.$route.query.country?a("el-table-column",{attrs:{prop:"hospitalName",width:"160","show-tooltip-when-overflow":"",label:"医疗机构"}}):t._e(),"1"==this.params.country?a("el-table-column",{attrs:{prop:"batch",width:"120",label:"批次"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t.formatBatch(e.row.batchCode?e.row.batchCode:e.row.batch)))])]}}],null,!1,**********)}):t._e(),a("el-table-column",{attrs:{label:"操作",align:"right","min-width":"250",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.add("show",e.row)}}},[t._v("查看详情")]),"1"==t.$route.query.country&&0==e.row.priceArray.length&&t.setPriceAble||"2"==t.$route.query.country?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.setPrice(e.row)}}},[t._v("设置采购价格")]):t._e(),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.add("edit",e.row)}}},[t._v("编辑")]),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.del(e.row)}}},[t._v("删除")])]}}])})],1),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)]),a("el-dialog",{attrs:{title:"药品","close-on-click-modal":!1,visible:t.dialogVisible,width:"45%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[t.isSetPrice?a("el-tabs",{staticStyle:{"margin-left":"10px","margin-right":"10px"},on:{"tab-click":t.handleClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"药品详情",name:"first"}}),a("el-tab-pane",{attrs:{label:"药品价格",name:"second"}})],1):t._e(),"first"==t.activeName?a("div",{staticClass:"dialog-content"},[a("el-form",{ref:"form",staticClass:"item-form",attrs:{model:t.formData,"label-width":"110px",rules:t.rules}},[a("span",{staticStyle:{color:"red"}},[t._v("提示：当药品本位码与药品规格及包装规格都相同时则视为同一种药！")]),a("div",{staticClass:"fromBox"},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"通用名",prop:"catalogId"}},[a("el-select",{attrs:{filterable:"",placeholder:"请选择通用名",disabled:t.isSetPrice},on:{change:t.catalogChange,blur:t.selectChange},model:{value:t.formData.catalogId,callback:function(e){t.$set(t.formData,"catalogId",e)},expression:"formData.catalogId"}},t._l(t.drugCatalogList,function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}},[a("span",{staticStyle:{float:"left"}},[t._v(t._s(e.name))]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v(t._s(e.category))])])}),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"商品名",prop:"goodsName"}},[a("el-input",{attrs:{placeholder:"请输入商品名",readonly:t.isSetPrice},model:{value:t.formData.goodsName,callback:function(e){t.$set(t.formData,"goodsName",e)},expression:"formData.goodsName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"剂型",prop:"dosageForm"}},[a("el-input",{attrs:{readonly:t.isSetPrice,placeholder:"请输入药品剂型"},model:{value:t.formData.dosageForm,callback:function(e){t.$set(t.formData,"dosageForm",e)},expression:"formData.dosageForm"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"规格",prop:"specs"}},[a("el-input",{attrs:{placeholder:"请输入药品规格,如 100mg",readonly:t.isSetPrice},model:{value:t.formData.specs,callback:function(e){t.$set(t.formData,"specs",e)},expression:"formData.specs"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包装规格",prop:"packingSpecs"}},[a("el-input",{attrs:{readonly:t.isSetPrice,placeholder:"请输入药品包装规格"},model:{value:t.formData.packingSpecs,callback:function(e){t.$set(t.formData,"packingSpecs",e)},expression:"formData.packingSpecs"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"单位",prop:"unit"}},[a("el-select",{attrs:{placeholder:"请选择药品单位",disabled:t.isSetPrice},model:{value:t.formData.unit,callback:function(e){t.$set(t.formData,"unit",e)},expression:"formData.unit"}},t._l(t.unitList,function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})}),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"药本位码",prop:"standardCode"}},[a("el-input",{attrs:{placeholder:"请输入药品本位码",readonly:t.isSetPrice},model:{value:t.formData.standardCode,callback:function(e){t.$set(t.formData,"standardCode",e)},expression:"formData.standardCode"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"药品类别",prop:"category"}},[a("el-select",{attrs:{placeholder:"请选择药品类别",disabled:t.isSetPrice},model:{value:t.formData.category,callback:function(e){t.$set(t.formData,"category",e)},expression:"formData.category"}},t._l(t.categoryList,function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})}),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"医保目录",prop:"medicalInsurance"}},[a("el-select",{attrs:{placeholder:"请选择医保目录类别",disabled:t.isSetPrice},model:{value:t.formData.medicalInsurance,callback:function(e){t.$set(t.formData,"medicalInsurance",e)},expression:"formData.medicalInsurance"}},[a("el-option",{attrs:{label:"甲类",value:"1"}}),a("el-option",{attrs:{label:"乙类",value:"2"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"药品类型",prop:"country"}},[a("el-select",{attrs:{disabled:!0},on:{change:t.countryChange},model:{value:t.formData.country,callback:function(e){t.$set(t.formData,"country",e)},expression:"formData.country"}},[a("el-option",{attrs:{label:"国家集采",value:"1"}}),a("el-option",{attrs:{label:"非国家集采",value:"0"}}),a("el-option",{attrs:{label:"医院线上药品",value:"3"}}),a("el-option",{attrs:{label:"医院线下药品",value:"2"}})],1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"生产企业",prop:"company"}},[a("el-input",{attrs:{readonly:t.isSetPrice,placeholder:"请输入药品生产企业"},model:{value:t.formData.drugCompanyName,callback:function(e){t.$set(t.formData,"drugCompanyName",e)},expression:"formData.drugCompanyName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"批准文号",prop:"approvalNumber"}},[a("el-input",{attrs:{readonly:t.isSetPrice,placeholder:"请输入药品批准文号"},model:{value:t.formData.approvalNumber,callback:function(e){t.$set(t.formData,"approvalNumber",e)},expression:"formData.approvalNumber"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包装材质",prop:"packing"}},[a("el-input",{attrs:{readonly:t.isSetPrice,placeholder:"请输入包装材质 "},model:{value:t.formData.packing,callback:function(e){t.$set(t.formData,"packing",e)},expression:"formData.packing"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"质量层次",prop:"qualityLevel"}},[a("el-input",{attrs:{readonly:t.isSetPrice,placeholder:"请输入药品质量层次"},model:{value:t.formData.qualityLevel,callback:function(e){t.$set(t.formData,"qualityLevel",e)},expression:"formData.qualityLevel"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"基药属性",prop:"attribute"}},[a("el-select",{attrs:{placeholder:"请输入药品基药属性",disabled:t.isSetPrice},model:{value:t.formData.attribute,callback:function(e){t.$set(t.formData,"attribute",e)},expression:"formData.attribute"}},[a("el-option",{attrs:{label:"--请选择--",value:""}}),a("el-option",{attrs:{label:"空",value:"0"}}),a("el-option",{attrs:{label:"国基",value:"1"}}),a("el-option",{attrs:{label:"省基",value:"2"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"医保药品代码",prop:"medicalInsuranceCode"}},[a("el-input",{attrs:{disabled:t.isSetPrice},model:{value:t.formData.medicalInsuranceCode,callback:function(e){t.$set(t.formData,"medicalInsuranceCode",e)},expression:"formData.medicalInsuranceCode"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:"1"==t.formData.country?12:24}},[a("el-form-item",{staticClass:"col-left",attrs:{label:"状态",prop:"status"}},[a("el-switch",{attrs:{"active-value":"1","inactive-value":"0",disabled:t.isSetPrice},model:{value:t.formData.status,callback:function(e){t.$set(t.formData,"status",e)},expression:"formData.status"}})],1)],1),"1"==t.formData.country?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"药品批次",prop:"batch"}},[a("el-select",{model:{value:t.formData.batch,callback:function(e){t.$set(t.formData,"batch",e)},expression:"formData.batch"}},t._l(t.batchList,function(e){return a("el-option",{key:e.code,attrs:{label:e.batchName,value:e.code}})}),1)],1)],1):t._e()],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{staticClass:"col-left",attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",readonly:t.isSetPrice,placeholder:"请填写备注"},model:{value:t.formData.remark,callback:function(e){t.$set(t.formData,"remark",e)},expression:"formData.remark"}})],1)],1)],1)],1)])],1):t._e(),"second"==t.activeName?a("div",{staticClass:"dialog-content"},[a(t.currentComponent,{tag:"component",attrs:{drugCode:t.drugCode,country:t.country,disabled:t.setPriceDisabled}})],1):t._e(),t.isSetPrice?t._e():a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("确 定")])],1),t.isSetPrice?a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("关闭")])],1):t._e()],1),t.importDialogVisible?a("el-dialog",{staticClass:"upload-box",attrs:{title:"药品数据导入",visible:t.importDialogVisible,"close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":"",width:"40%"},on:{"update:visible":function(e){t.importDialogVisible=e}}},[a("el-form",{ref:"importDataForm",attrs:{"label-position":"left","label-width":"110px",rules:t.importRules,model:t.uploadData}},[a("el-form-item",{attrs:{label:"药品来源",prop:"country"}},[a("el-select",{attrs:{placeholder:"请选择药品来源",disabled:!0},model:{value:t.uploadData.country,callback:function(e){t.$set(t.uploadData,"country",e)},expression:"uploadData.country"}},[a("el-option",{attrs:{label:"国家集采",value:"1"}}),a("el-option",{attrs:{label:"非国家集采",value:"0"}}),a("el-option",{attrs:{label:"医院线上药品",value:"3"}}),a("el-option",{attrs:{label:"医院线下药品",value:"2"}})],1)],1),"0"==t.uploadData.country||"1"==t.uploadData.country?a("el-form-item",{attrs:{label:"采购平台",prop:"source"}},[a("el-select",{attrs:{placeholder:"请选择平台"},model:{value:t.uploadData.source,callback:function(e){t.$set(t.uploadData,"source",e)},expression:"uploadData.source"}},[a("el-option",{key:"1",attrs:{label:"深圳市药品交易平台",value:"1"}}),a("el-option",{key:"2",attrs:{label:"广东省药品交易平台",value:"2"}}),a("el-option",{key:"3",attrs:{label:"广州市药品交易平台",value:"3"}})],1)],1):t._e(),a("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{drag:"",limit:1,"on-change":t.onChange,"on-exceed":t.handleExceed,action:t.uploadUrl,headers:t.headers,data:{source:t.uploadData.source,country:t.uploadData.country},accept:".xlsx,.xls","on-success":t.onSuccess,"on-error":t.onError,"before-upload":t.beforeUpload,"before-remove":t.beforeRemove,multiple:!1,"auto-upload":!1}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[t._v("将文件拖到此处，或"),a("em",[t._v("点击上传")])]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("只能上传【 .xlsx / .xls 】文件")])])],1),a("span",{staticStyle:{color:"red"}},[t._v("提示：请先下载 Excel 模板，按照模板中格式进行导入！")]),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.submitUpload}},[t._v("确 定")]),a("el-button",{attrs:{type:"warning"},on:{click:t.drugTemplate}},[t._v("下载模板")]),a("el-button",{on:{click:function(e){t.importDialogVisible=!1}}},[t._v("取消")])],1)],1):t._e(),t.hospatilImportDialogVisible?a("el-dialog",{staticClass:"upload-box",attrs:{title:"医院梳理的药品数据导入",visible:t.hospatilImportDialogVisible,"close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":"",width:"40%"},on:{"update:visible":function(e){t.hospatilImportDialogVisible=e}}},[a("el-form",{ref:"importHospitalDataForm",attrs:{"label-position":"left","label-width":"110px"}},[a("el-upload",{ref:"hospitalUpload",staticClass:"upload-demo",attrs:{drag:"",limit:1,"on-change":t.onChange,"on-exceed":t.handleExceed,action:t.uploadHospatilUrl,headers:t.headers,accept:".xlsx,.xls","on-success":t.onSuccess,"on-error":t.onError,"before-upload":t.beforeUpload,"before-remove":t.beforeRemove,multiple:!1,"auto-upload":!1}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[t._v("将文件拖到此处，或"),a("em",[t._v("点击上传")])]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("只能上传【 .xlsx / .xls 】文件")])])],1),a("span",{staticStyle:{color:"red"}},[t._v("提示：请先下载 Excel 模板，按照模板中格式进行导入！")]),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.submitHospitalUpload}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.hospatilImportDialogVisible=!1}}},[t._v("取消")])],1)],1):t._e(),t.provinceImportDialogVisible?a("el-dialog",{staticClass:"upload-box",attrs:{title:"省药品数据导入",visible:t.provinceImportDialogVisible,"close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":"",width:"40%"},on:{"update:visible":function(e){t.provinceImportDialogVisible=e}}},[a("el-form",{ref:"importProvinceDataForm",attrs:{"label-position":"left","label-width":"110px"}},[a("el-upload",{ref:"provinceUpload",staticClass:"upload-demo",attrs:{drag:"",limit:1,"on-change":t.onChange,"on-exceed":t.handleExceed,action:t.uploadProvinceUrl,headers:t.headers,accept:".xlsx,.xls","on-success":t.onSuccess,"on-error":t.onError,"before-upload":t.beforeUpload,"before-remove":t.beforeRemove,multiple:!1,"auto-upload":!1}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[t._v("将文件拖到此处，或"),a("em",[t._v("点击上传")])]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("只能上传【 .xlsx / .xls 】文件")])])],1),a("span",{staticStyle:{color:"red"}},[t._v("提示：请先下载 Excel 模板，按照模板中格式进行导入！")]),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.submitProvinceUpload}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.provinceImportDialogVisible=!1}}},[t._v("取消")])],1)],1):t._e(),a("el-dialog",{attrs:{title:"药品采购价格",visible:t.show,width:"45%"},on:{"update:visible":function(e){t.show=e}}},[a(t.currentComponent,{ref:"asyncDialog",tag:"component",attrs:{drugCode:t.drugCode,country:t.country},on:{close:t.close}})],1)],1)])},t.staticRenderFns=[]}}]);