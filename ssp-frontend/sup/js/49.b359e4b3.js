(window.webpackJsonp=window.webpackJsonp||[]).push([[49],{"18Ln":function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left"},[t("el-row",{attrs:{gutter:10}},[t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("配送单号")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入配送单号"},model:{value:e.params.deliveryCode,callback:function(t){e.$set(e.params,"deliveryCode",t)},expression:"params.deliveryCode"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("订单号")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入订单号"},model:{value:e.params.orderCode,callback:function(t){e.$set(e.params,"orderCode",t)},expression:"params.orderCode"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("通用名")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入药品通用名"},model:{value:e.params.name,callback:function(t){e.$set(e.params,"name",t)},expression:"params.name"}})],1)])]),this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("医疗机构")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:e.params.hospitalName,callback:function(t){e.$set(e.params,"hospitalName",t)},expression:"params.hospitalName"}},e._l(e.hospitalList,function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}),1)],1)])]):e._e(),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("配送企业")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入配送企业名称"},model:{value:e.params.deliveryName,callback:function(t){e.$set(e.params,"deliveryName",t)},expression:"params.deliveryName"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("配送时间")]),t("div",{staticClass:"searchInput"},[t("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","pickinvoiceVoucherser-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:e.params.deliveryTime,callback:function(t){e.$set(e.params,"deliveryTime",t)},expression:"params.deliveryTime"}})],1)])])],1)],1),t("div",{staticClass:"right"},[t("div",[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1)])]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.dataList,"highlight-current-row":"",height:e.tableHeight,border:""}},[t("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单号",width:"200","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"200","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"deliveryName",label:"配送企业",width:"200","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"name",label:"通用名",width:"200","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"单价（元）",width:"100"}}),t("el-table-column",{attrs:{prop:"num",label:"发货数量"}}),t("el-table-column",{attrs:{prop:"amount",label:"金额(元)"}}),t("el-table-column",{attrs:{prop:"orderCode",label:"订单编号",width:"200"}}),t("el-table-column",{attrs:{prop:"deliveryTime",label:"配送时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e._f("formatTime")(a.row.deliveryTime)))])]}}])}),t("el-table-column",{attrs:{prop:"stockStatus",label:"入库状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.stockStatus&&"0"!=a.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未入库")])],1),"1"==a.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已入库")])],1):e._e(),"2"==a.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分入库")])],1):e._e()]}}])}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showDetail(a.row)}}},[e._v("查看详情")])]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)])],1)},t.staticRenderFns=[]},"7Lwx":function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var s=r(a("Q9c5")),i=r(a("DWNM"));r(a("XRYr"));function r(e){return e&&e.__esModule?e:{default:e}}t.default={name:"delivery-list",mixins:[i.default],data:function(){return{pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=(new Date).format("yyyy-MM-dd HH:mm:ss"),a=new Date,s=new Date(a.getTime()-6048e5).format("yyyy-MM-dd HH:mm:ss");e.$emit("pick",[s,t])}},{text:"最近一个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd HH:mm:ss"),a=new Date,s=new Date(a.getTime()-2592e6).format("yyyy-MM-dd HH:mm:ss");e.$emit("pick",[s,t])}},{text:"最近三个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd HH:mm:ss"),a=new Date,s=new Date(a.getTime()-7776e6).format("yyyy-MM-dd HH:mm:ss");e.$emit("pick",[s,t])}}]},adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1,rLoading:{},warningData:[],tableHeight:100,dataList:[],hospitalList:[],params:{deliveryTime:"",orderCode:"",name:"",page:1,limit:10,records:0,deliveryName:"",deliveryCode:"",hospitalName:""}}},props:{},computed:{},watch:{},methods:{setDate:function(){console.log("初始化时间查询");var e=(new Date).format("yyyy-MM-dd"),t=new Date,a=new Date(t.getTime()-2592e6).format("yyyy-MM-dd");this.params.deliveryTime=[a,e],console.log(this.params.deliveryTime)},initRole:function(){var e=this.$store.getters.curUser.roleCode;-1!=e.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=e.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=e.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=e.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)},getHospitalList:function(){var e=this,t=s.default.baseContext+"/supervise/supHospital/all";this.$http_post(t,{}).then(function(t){1==t.state?e.hospitalList=t.row:e.$alert(t.message)})},getWaringType:function(e){for(var t=0;t<this.warningData.length;t++)if(this.warningData[t].value==e)return this.warningData[t].label},getWaring:function(e){for(var t=[],a=e.split(","),s=0;s<a.length;s++){var i=this.getWaringType(a[s]);i&&t.push({name:i})}return t},getDictItem:function(e){var t=this,a=this.openLoading();this.$http_post(s.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(s){var i=s.rows;1==s.state?("WARNING"==e&&(t.warningData=i),a.close()):(a.close(),t.$message.error(s.message))})},showDetail:function(e){this.$router.push({name:"conMaterialDeliveryDetail",query:{deliveryCode:e.deliveryCode}})},onSearch:function(e){"reset"==e?(this.params.name="",this.params.deliveryCode="",this.params.hospitalName="",this.params.deliveryName="",this.params.orderCode="",this.params.page=1,this.setDate(),this.onQuery()):(this.params.page=1,this.onQuery())},onPageClick:function(e){this.params.page=e,this.onQuery()},onQuery:function(){var e=this,t=this.params;console.log(t);var a=this.openLoading(),i=s.default.baseContext+"/supervise/conMaterialDeliveryItem/getConMaterialDeliveryItemList";this.$http_post(i,t).then(function(t){1==t.state?(e.dataList=t.rows,e.params.records=t.records,a.close()):(a.close(),e.$alert(t.message))})},time:function(e,t){if(void 0==e.submitTime||""==e.submitTime)return"";var a=new Date(e.submitTime),s=a.getFullYear()+"-",i=(a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-",r=a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ";a.getHours(),a.getHours(),a.getMinutes(),a.getMinutes(),a.getSeconds(),a.getSeconds();return s+i+r},init:function(){}},mounted:function(){var e=this;this.setDate(),this.initRole(),this.onQuery(),this.getHospitalList(),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e),a=t.getFullYear()+"-",s=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",i=t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ";t.getHours(),t.getHours(),t.getMinutes(),t.getMinutes(),t.getSeconds(),t.getSeconds();return a+s+i}}}},"81Yr":function(e,t,a){"use strict";a("wBEC")},NIJm:function(e,t,a){"use strict";a.r(t);var s=a("7Lwx"),i=a.n(s);for(var r in s)["default"].indexOf(r)<0&&function(e){a.d(t,e,function(){return s[e]})}(r);t.default=i.a},PkMV:function(e,t,a){"use strict";var s=a("18Ln");a.o(s,"render")&&a.d(t,"render",function(){return s.render}),a.o(s,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return s.staticRenderFns})},"qJ+Y":function(e,t,a){"use strict";a.r(t);var s=a("PkMV"),i=a("NIJm");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,function(){return i[e]})}(r);a("81Yr");var n=a("gp09"),l=Object(n.a)(i.default,s.render,s.staticRenderFns,!1,null,"d453f4c0",null);t.default=l.exports},wBEC:function(e,t,a){}}]);