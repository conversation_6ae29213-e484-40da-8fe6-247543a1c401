(window.webpackJsonp=window.webpackJsonp||[]).push([[91],{B8iv:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var i=n(a("Q9c5")),s=n(a("DWNM"));function n(t){return t&&t.__esModule?t:{default:t}}e.default={name:"todo",mixins:[s.default],data:function(){return{hospitalList:[],rLoading:{},tableHeight:100,dataList:[],params:{page:1,limit:10,records:0,bizNum:"",date:"",status:"",startDate:"",endDate:""},adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1}},props:{},watch:{},computed:{uploadUrl:function(){}},mounted:function(){var t=this;this.initRole(),this.onQuery(),this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}},methods:{onSearch:function(t){"reset"==t?(this.params.bizNum="",this.params.status="",this.params.date="",this.params.startDate="",this.params.endDate="",this.onQuery()):""!=this.params.bizNum||""!=this.params.status||""!=this.params.date?(this.params.page=1,this.onQuery()):this.$message.error("请输入查询条件")},onPageClick:function(t){this.params.page=t,this.onQuery()},onQuery:function(){var t=this;this.params.date&&(this.params.startDate=this.params.date[0],this.params.endDate=this.params.date[1]);var e=this.params,a=this.openLoading(),s=i.default.baseContext+"/supervise/supSettlement/todo";this.$http_post(s,e).then(function(e){1==e.state?(t.dataList=e.rows,t.params.records=e.records,a.close()):(a.close(),t.$alert(e.message))})},deal:function(t){var e=this;"SETTLEMENT"==t.bizType&&(this.medicalAdmin?this.$router.push({name:"todoDetailMedical",query:{settlementId:t.bizId,type:"todo"}}):this.hospitalAdmin&&this.$router.push({name:"todoDetail",query:{settlementId:t.bizId,type:"todo"}})),"RECONCILE"==t.bizType&&(this.medicalAdmin?"4"==t.curNodeId&&this.$confirm("当前环节已到达生成对账表，是否前去生成?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.$router.push({name:"medicalApplyList"})}):this.$router.push({name:"reconcileTodoDetail",query:{reconcileId:t.bizId,type:"todo"}})),"PAYMENT"==t.bizType&&("7"==t.curNodeId?this.$router.push({name:"paymentTodoDetail",query:{reconcileId:t.bizId,type:"todo"}}):"8"==t.curNodeId&&this.$router.push({name:"paymentTodoDetail2",query:{paymentId:t.bizId,type:"todo"}}))},recall:function(t){},initRole:function(){var t=this.$store.getters.curUser.roleCode;-1!=t.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=t.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=t.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=t.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)}},beforeDestroy:function(){window.onresize=null},filters:{formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}}}},RIWc:function(t,e,a){"use strict";a("UU19")},UU19:function(t,e,a){},kn27:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left"},[e("el-row",{attrs:{gutter:10}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("业务编号")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入业务编号"},model:{value:t.params.bizNum,callback:function(e){t.$set(t.params,"bizNum",e)},expression:"params.bizNum"}})],1)])])],1)],1),e("div",{staticClass:"right"},[e("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1)]),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.dataList,"highlight-current-row":"",height:t.tableHeight}},[e("el-table-column",{attrs:{type:"index",label:"序号",width:"50"}}),e("el-table-column",{attrs:{prop:"bizNum",label:"业务编号"}}),e("el-table-column",{attrs:{prop:"bizType",label:"业务阶段"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s("SETTLEMENT"==a.row.bizType?"结算单阶段":"RECONCILE"==a.row.bizType?"对账单阶段":"支付单阶段"))])]}}])}),e("el-table-column",{attrs:{prop:"curNodeName",label:"当前环节"}}),e("el-table-column",{attrs:{prop:"receiveTime",label:"到达时间"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t._f("formatTime")(a.row.receiveTime)))])]}}])}),t.$route.query.type?t._e():e("el-table-column",{attrs:{label:"操作",align:"center",width:"100",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.deal(a.row)}}},[t._v("办理")])]}}],null,!1,3560116521)})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)])],1)},e.staticRenderFns=[]},nekL:function(t,e,a){"use strict";var i=a("kn27");a.o(i,"render")&&a.d(e,"render",function(){return i.render}),a.o(i,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return i.staticRenderFns})},nmp8:function(t,e,a){"use strict";a.r(e);var i=a("nekL"),s=a("u00s");for(var n in s)["default"].indexOf(n)<0&&function(t){a.d(e,t,function(){return s[t]})}(n);a("RIWc");var r=a("gp09"),o=Object(r.a)(s.default,i.render,i.staticRenderFns,!1,null,"6e93937e",null);e.default=o.exports},u00s:function(t,e,a){"use strict";a.r(e);var i=a("B8iv"),s=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,function(){return i[t]})}(n);e.default=s.a}}]);