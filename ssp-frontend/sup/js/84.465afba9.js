(window.webpackJsonp=window.webpackJsonp||[]).push([[84],{"2fyY":function(e,t,a){"use strict";a("upkB")},HnY4:function(e,t,a){"use strict";var r=a("o8Gh");a.o(r,"render")&&a.d(t,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return r.staticRenderFns})},"MAS+":function(e,t,a){"use strict";a.r(t);var r=a("HnY4"),s=a("XLTF");for(var l in s)["default"].indexOf(l)<0&&function(e){a.d(t,e,function(){return s[e]})}(l);a("2fyY");var o=a("gp09"),i=Object(o.a)(s.default,r.render,r.staticRenderFns,!1,null,"4667f8c0",null);t.default=i.exports},TCJ6:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=l(a("Q9c5")),s=l(a("DWNM"));function l(e){return e&&e.__esModule?e:{default:e}}t.default={name:"countryPurchase",mixins:[s.default],data:function(){return{hospital:{},dataList:[],params:{page:1,limit:10,records:0,catalogName:"",hospitalId:"",hospitalName:"",year:(new Date).getFullYear()+""},formData:{id:"",detailId:"",hospitalId:"",year:"",purchase:"",complete:"",addComplete:"0"},dialogVisible:!1,tableHeight:100,rules:{detailId:[{required:!0,message:"请选择药品",trigger:"blur"}],hospitalId:[{required:!0,message:"请选择医疗机构",trigger:"blur"}],year:[{required:!0,message:"请选择年份",trigger:"blur"}],purchase:[{required:!0,message:"请输入约定采购量",trigger:"blur"}],complete:[{required:!0,message:"请输入新增约定采购量",trigger:"blur"}],addComplete:[{required:!0,message:"请输入新增约定采购量",trigger:"blur"}]},catalogNameList:[],hospitalNameList:[]}},props:{},computed:{},watch:{},methods:{rateFormat:function(e){var t=e.complete/e.purchase;return t=isNaN(t)||""===t?"--":Number(100*t).toFixed(2)+"%"},add:function(e,t){var a=this;if("edit"==e){var s=this.openLoading();this.formData.addComplete="0",this.$http_post(r.default.baseContext+"/supervise/supCountryPurchase/info/"+t.id,{}).then(function(e){1==e.state?null!=e.row?(a.formData={id:e.row.id,hospitalId:a.hospital.id,detailId:e.row.detailId,year:e.row.year,purchase:e.row.purchase,complete:e.row.complete},a.dialogVisible=!0):a.$message.error("系统异常"):null!=e.message?a.$message.error(e.message):a.$message.error("系统异常"),s.close()})}},save:function(){var e=this;this.$refs.form.validate(function(t){if(!t)return!1;e.formData.complete=Number(e.formData.complete)+Number(e.formData.addComplete);var a=r.default.baseContext+"/supervise/supCountryPurchase/update",s=e.openLoading();e.$http_post(a,e.formData).then(function(t){1==t.state?(e.$message.success("添加成功"),e.onQuery(),e.dialogVisible=!1):e.$alert(t.message),s.close()})})},del:function(e){var t=this;this.$alert("确定删除【"+e.year+"年 "+e.hospitalName+"】的约定采购量数据吗","确认信息",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,type:"warning"}).then(function(){var a=t.openLoading();t.$http_post(r.default.baseContext+"/supervise/supCountryPurchase/delete/"+e.id,null).then(function(e){1==e.state?(t.$message.success("删除成功"),t.onQuery(),a.close()):(a.close(),t.$message.error(e.message))})}).catch(function(e){console.log(e)})},handleCurrentChange:function(e){},onSearch:function(e){"reset"==e?(this.params.catalogName="",this.params.year=(new Date).getFullYear()+"",this.onQuery()):""!=this.params.catalogName||""!=this.params.year?(this.params.page=1,this.onQuery()):this.$message.warning("请输入查询条件")},onPageClick:function(e){this.params.page=e,this.onQuery()},onQuery:function(){var e=this,t=this.params,a=this.openLoading(),s=r.default.baseContext+"/supervise/supCountryPurchase/list";this.$http_post(s,t).then(function(t){if(1==t.state){var r=t.rows;e.dataList=r,e.params.records=t.records,a.close()}else a.close(),e.$alert(t.message)})},init:function(){var e=this,t=r.default.baseContext+"/supervise/supDrugDetail/all";this.$http_post(t,{country:"1"}).then(function(t){1==t.state?e.catalogNameList=t.row:e.$alert(t.message)});var a=r.default.baseContext+"/supervise/supHospital/all";this.$http_post(a,{}).then(function(t){1==t.state?e.hospitalNameList=t.row:e.$alert(t.message)});this.$store.getters.curUser;this.hospital={id:"1"},this.formData.hospitalId=this.hospital.id}},mounted:function(){var e=this;this.init(),this.onQuery(),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null}}},XLTF:function(e,t,a){"use strict";a.r(t);var r=a("TCJ6"),s=a.n(r);for(var l in r)["default"].indexOf(l)<0&&function(e){a.d(t,e,function(){return r[e]})}(l);t.default=s.a},o8Gh:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"},[t("span",[e._v("通用名")]),t("el-input",{attrs:{placeholder:"请输入通用名"},model:{value:e.params.catalogName,callback:function(t){e.$set(e.params,"catalogName",t)},expression:"params.catalogName"}}),t("span",[e._v("年份")]),t("el-date-picker",{attrs:{"value-format":"yyyy",type:"year",placeholder:"选择年"},model:{value:e.params.year,callback:function(t){e.$set(e.params,"year",t)},expression:"params.year"}}),t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1)]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.dataList,"highlight-current-row":"",height:e.tableHeight},on:{"current-change":e.handleCurrentChange}},[t("el-table-column",{attrs:{prop:"catalogName",label:"通用名"}}),t("el-table-column",{attrs:{prop:"specs",label:"规格"}}),t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构"}}),t("el-table-column",{attrs:{prop:"purchase",label:"约定采购量"}}),t("el-table-column",{attrs:{prop:"complete",label:"已完成采购量"}}),t("el-table-column",{attrs:{prop:"rate",formatter:e.rateFormat,label:"完成率"}}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.add("edit",a.row)}}},[e._v("添加")])]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)]),t("el-dialog",{attrs:{title:"添加已完成采购量",visible:e.dialogVisible,width:"45%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",{staticClass:"dialog-content"},[t("el-form",{ref:"form",staticClass:"item-form",attrs:{model:e.formData,"label-width":"90px",rules:e.rules}},[t("el-row",{attrs:{type:"flex"}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"通用名",prop:"hospitalId"}},[t("el-select",{attrs:{disabled:!0,filterable:"",placeholder:"请选择通用名"},model:{value:e.formData.detailId,callback:function(t){e.$set(e.formData,"detailId",t)},expression:"formData.detailId"}},e._l(e.catalogNameList,function(a){return t("el-option",{key:a.id,attrs:{label:a.catalogName,value:a.id}},[t("span",{staticStyle:{float:"left"}},[e._v(e._s(a.catalogName))]),t("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(a.specs))])])}),1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"医疗机构",prop:"hospitalId"}},[t("el-select",{attrs:{disabled:!0,filterable:"",placeholder:"请选择医疗机构"},model:{value:e.formData.hospitalId,callback:function(t){e.$set(e.formData,"hospitalId",t)},expression:"formData.hospitalId"}},e._l(e.hospitalNameList,function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}),1)],1)],1)],1),t("el-row",{attrs:{type:"flex"}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"年份",prop:"year"}},[t("el-date-picker",{attrs:{disabled:!0,type:"year","value-format":"yyyy",placeholder:"选择年"},model:{value:e.formData.year,callback:function(t){e.$set(e.formData,"year",t)},expression:"formData.year"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"约定采购量",prop:"purchase"}},[t("el-input",{attrs:{disabled:!0,type:"number",placeholder:"请设置约定采购量"},model:{value:e.formData.purchase,callback:function(t){e.$set(e.formData,"purchase",t)},expression:"formData.purchase"}})],1)],1)],1),t("el-row",{attrs:{type:"flex"}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{"label-width":"110px",label:"已完成采购量",prop:"complete"}},[t("el-input",{attrs:{disabled:!0,type:"number",placeholder:"请设置已完成采购量"},model:{value:e.formData.complete,callback:function(t){e.$set(e.formData,"complete",t)},expression:"formData.complete"}})],1)],1)],1),t("el-row",{attrs:{type:"flex"}},[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{"label-width":"140px",label:"新增已完成采购量",prop:"addComplete"}},[t("el-input",{attrs:{type:"number",placeholder:"请设置已完成采购量"},model:{value:e.formData.addComplete,callback:function(t){e.$set(e.formData,"addComplete",t)},expression:"formData.addComplete"}})],1)],1)],1)],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确 定")])],1)])],1)},t.staticRenderFns=[]},upkB:function(e,t,a){}}]);