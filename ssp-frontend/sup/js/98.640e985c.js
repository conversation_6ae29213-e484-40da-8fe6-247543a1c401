(window.webpackJsonp=window.webpackJsonp||[]).push([[98],{"390g":function(e,t,a){"use strict";a("hr6O")},"3UAL":function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var s=l(a("Q9c5")),o=l(a("DWNM")),i=l(a("XRYr"));function l(e){return e&&e.__esModule?e:{default:e}}t.default={name:"stockIn-list",mixins:[o.default],data:function(){return{adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1,rLoading:{},fileList:[],headers:{},uploadData:{},importDialogVisible:!1,warningData:[],tableHeight:100,dialogVisible:!1,dataList:[],hospitalList:[],params:{orderCode:"",catalogName:"",page:1,limit:10,records:0,deliveryName:"",stockInCode:"",hospitalName:"",stockInTime:"",source:""},dateChangeValue:0}},props:{},computed:{uploadUrl:function(){return s.default.baseContext+"/file/upload"}},watch:{},methods:{setDate:function(){var e=(new Date).format("yyyy-MM-dd"),t=new Date,a=new Date(t.getTime()-2592e6).format("yyyy-MM-dd");this.params.stockInTime=[a,e]},initRole:function(){var e=this.$store.getters.curUser.roleCode;-1!=e.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=e.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=e.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=e.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)},getHospitalList:function(){var e=this,t=s.default.baseContext+"/supervise/supHospital/all";this.$http_post(t,{}).then(function(t){1==t.state?e.hospitalList=t.row:e.$alert(t.message)})},cancel:function(){this.importDialogVisible=!1},downloadTemplate:function(){var e=s.default.baseContext+"/file/downloadStockInImportTemplate",t=document.createElement("a");t.href=e,document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(e)},submitUpload:function(){var e=this;this.rLoading=this.openLoading("导入中,请耐心等待...",{timeout:1e5});var t=this.uploadData.docId;this.$http_post(s.default.baseContext+"/supervise/supStockIn/importCountryStockIn?docId="+t,{},null,{timeout:1e5}).then(function(t){1==t.state?(e.onQuery(),e.rLoading.close(),e.importDialogVisible=!1,e.$alert("导入成功，请前往【入库单批次日志】查看导入结果")):(e.onQuery(),e.rLoading.close(),e.importDialogVisible=!1,e.$message.error(t.message))})},onSuccess:function(e,t,a){if("1"!=e.state)return this.$refs.upload.clearFiles(),this.$alert(e.message),!1;t.docId=e.row.id,this.uploadData={docId:e.row.id,name:e.row.name}},onError:function(e,t,a){this.rLoading.close(),console.log(e),this.$message.error("上传失败,请重试")},handlePreview:function(e){var t=this.downloadUrl+"?docId="+encodeURI(encodeURI(e.docId))+"&fileName="+e.name;console.log(e);var a=document.createElement("a");a.href=t,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(t)},handleExceed:function(e,t){this.$message.warning("一次只能上传一个Excel文件")},beforeUpload:function(e){if(e.size/1024/1024>20)return this.$message.error("上传文件过大，请分开上传，单个文件最大为20M。"),!1;var t=e.name;return"xlsx"==t.substring(t.length-4)||"xls"==t.substring(t.length-3)||(this.$message.error("上传文件格式只能为【.xlsx】或【.xls】"),!1)},beforeRemove:function(e,t){return!this.beforeUpload(e)||this.$confirm("确定移除 "+e.name+"？")},handleRemove:function(e,t){return this.uploadData={},console.log(e.docId,e,t),!0},stockInImport:function(){this.importDialogVisible=!0},getWaringType:function(e){for(var t=0;t<this.warningData.length;t++)if(this.warningData[t].value==e)return this.warningData[t].label},getWaring:function(e){for(var t=[],a=e.split(","),s=0;s<a.length;s++){var o=this.getWaringType(a[s]);o&&t.push({name:o})}return t},getDictItem:function(e){var t=this,a=this.openLoading();this.$http_post(s.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(s){var o=s.rows;1==s.state?("WARNING"==e&&(t.warningData=o),a.close()):(a.close(),t.$message.error(s.message))})},showDetail:function(e){this.$router.push({name:"stockInDetail",query:{stockInCode:e.stockInCode}})},onSearch:function(e){"reset"==e?(this.params.catalogName="",this.params.stockInCode="",this.params.hospitalName="",this.params.deliveryName="",this.params.orderCode="",this.dateChangeValue=0,this.onQuery()):""!=this.params.hospitalName||""!=this.params.deliveryName||""!=this.params.code||""!=this.params.catalogName||""!=this.params.orderCode?(this.params.page=1,this.dateChangeValue=1,this.onQuery()):this.$message.warning("请输入查询条件")},onPageClick:function(e){this.params.page=e,this.onQuery()},onQuery:function(){var e=this;0!=this.dateChangeValue&&""!=this.dateChangeValue||this.setDate();var t=this.params,a=this.openLoading(),o=s.default.baseContext+"/supervise/supStockInItem/getStockInItemList";this.$http_post(o,t).then(function(t){1==t.state?(e.dataList=t.rows,e.params.records=t.records,a.close()):(a.close(),e.$alert(t.message))})}},mounted:function(){var e=this,t=i.default.doCloundRequest(s.default.app_key,s.default.app_security,"");this.headers["x-aep-appkey"]=t["x-aep-appkey"],this.headers["x-aep-signature"]=t["x-aep-signature"],this.headers["x-aep-timestamp"]=t["x-aep-timestamp"],this.headers["x-aep-nonce"]=t["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,this.initRole(),this.onQuery(),this.getHospitalList(),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e),a=t.getFullYear()+"-",s=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",o=t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ";t.getHours(),t.getHours(),t.getMinutes(),t.getMinutes(),t.getSeconds(),t.getSeconds();return a+s+o}}}},OqAW:function(e,t,a){"use strict";a.r(t);var s=a("nXYP"),o=a("qMkA");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,function(){return o[e]})}(i);a("390g");var l=a("gp09"),n=Object(l.a)(o.default,s.render,s.staticRenderFns,!1,null,"4d96d3d0",null);t.default=n.exports},hr6O:function(e,t,a){},nXYP:function(e,t,a){"use strict";var s=a("yQdI");a.o(s,"render")&&a.d(t,"render",function(){return s.render}),a.o(s,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return s.staticRenderFns})},qMkA:function(e,t,a){"use strict";a.r(t);var s=a("3UAL"),o=a.n(s);for(var i in s)["default"].indexOf(i)<0&&function(e){a.d(t,e,function(){return s[e]})}(i);t.default=o.a},yQdI:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.staticRenderFns=t.render=void 0;var s=function(e){return e&&e.__esModule?e:{default:e}}(a("/umX"));t.render=function(){var e,t=this,a=t._self._c;return a("div",{ref:"tableH",staticClass:"content"},[a("div",{staticClass:"search-header flex-row"},[a("div",{staticClass:"left"},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("入库单号")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入库单号"},model:{value:t.params.stockInCode,callback:function(e){t.$set(t.params,"stockInCode",e)},expression:"params.stockInCode"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("订单号")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入订单号"},model:{value:t.params.orderCode,callback:function(e){t.$set(t.params,"orderCode",e)},expression:"params.orderCode"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("通用名")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入药品通用名"},model:{value:t.params.catalogName,callback:function(e){t.$set(t.params,"catalogName",e)},expression:"params.catalogName"}})],1)])]),this.adminRole||this.medicalAdmin||this.socialAdmin?a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("医疗机构")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:(e={size:"small",clearable:""},(0,s.default)(e,"clearable",""),(0,s.default)(e,"filterable",""),(0,s.default)(e,"placeholder","请选择医疗机构"),e),model:{value:t.params.hospitalName,callback:function(e){t.$set(t.params,"hospitalName",e)},expression:"params.hospitalName"}},t._l(t.hospitalList,function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}),1)],1)])]):t._e(),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("配送企业")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入配送企业名称"},model:{value:t.params.deliveryName,callback:function(e){t.$set(t.params,"deliveryName",e)},expression:"params.deliveryName"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("入库时间")]),a("div",{staticClass:"searchInput"},[a("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":t.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:t.params.stockInTime,callback:function(e){t.$set(t.params,"stockInTime",e)},expression:"params.stockInTime"}})],1)])])],1)],1),a("div",{staticClass:"right"},[a("div",{staticStyle:{"margin-bottom":"5px"}},[a("el-button",{attrs:{size:"small",type:"warning",icon:"el-icon-download"},on:{click:t.stockInImport}},[t._v("导入入库单")])],1),a("div",[a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),a("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1)])]),a("el-row",{staticClass:"con"},[a("div",{staticClass:"con-left"},[a("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.dataList,"highlight-current-row":"",height:t.tableHeight,border:""}},[a("el-table-column",{attrs:{prop:"stockInCode",label:"入库单号",width:"200","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"200","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"deliveryName",label:"配送企业",width:"200","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"200","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"unitPrice",label:"单价（元）",width:"100"}}),a("el-table-column",{attrs:{prop:"num",label:"入库数量"}}),a("el-table-column",{attrs:{prop:"amount",label:"金额(元)"}}),a("el-table-column",{attrs:{prop:"orderCode",label:"订单编号",width:"200","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"stockInTime",label:"入库时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("formatTime")(e.row.stockInTime)))])]}}])}),a("el-table-column",{attrs:{prop:"stockStatus",label:"入库状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.stockStatus&&"0"!=e.row.stockStatus?t._e():a("div",[a("el-tag",{attrs:{type:"danger"}},[t._v("未入库")])],1),"1"==e.row.stockStatus?a("div",[a("el-tag",{attrs:{type:"success"}},[t._v("已入库")])],1):t._e(),"2"==e.row.stockStatus?a("div",[a("el-tag",{attrs:{type:"warning"}},[t._v("部分入库")])],1):t._e()]}}])}),a("el-table-column",{attrs:{label:"操作",align:"right",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.showDetail(e.row)}}},[t._v("查看详情")])]}}])})],1),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)]),a("el-dialog",{staticClass:"upload-box",attrs:{title:"药品入库明细单导入",visible:t.importDialogVisible,"append-to-body":"",width:"40%"},on:{"update:visible":function(e){t.importDialogVisible=e}}},[a("el-form",{ref:"importDataForm",attrs:{"label-position":"left",model:t.uploadData}},[a("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:(0,s.default)({drag:"",limit:1,headers:t.headers,action:t.uploadUrl,"on-preview":t.handlePreview,"on-remove":t.handleRemove,"on-exceed":t.handleExceed,"before-upload":t.beforeUpload,"before-remove":t.beforeRemove,"on-success":t.onSuccess,"on-error":t.onError,multiple:"",accept:".xls,.xlsx","file-list":t.fileList},"multiple",!1)},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[t._v("将文件拖到此处，或"),a("em",[t._v("点击上传")])]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("只能上传【 .xlsx / .xls 】文件")])])],1),a("span",{staticStyle:{color:"red"}},[t._v("提示：请下载 Excel 模板，按照格式进行导入！")]),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitUpload("")}}},[t._v("确 定")]),a("el-button",{attrs:{type:"warning"},on:{click:function(e){return t.downloadTemplate("")}}},[t._v("下载模板")]),a("el-button",{on:{click:t.cancel}},[t._v("取消")])],1)],1)],1)},t.staticRenderFns=[]}}]);