(window.webpackJsonp=window.webpackJsonp||[]).push([[3],{"4/L8":function(t,e,a){},"57Ej":function(t,e,a){"use strict";a("4/L8")},KPYa:function(t,e,a){"use strict";a.r(e);var o=a("oriS"),s=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return o[t]})}(r);e.default=s.a},OQvZ:function(t,e,a){},PsM9:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("el-dialog",{staticClass:"cms-dialog-padding",attrs:{title:"选择药品",visible:t.show,width:"70%"},on:{close:t.close}},[e("div",{staticClass:"dialog-content"},[e("el-form",{attrs:{model:t.relationParams,size:"small","label-width":"100px"}},[e("el-row",[e("el-col",{attrs:{span:10}},[e("el-form-item",{attrs:{label:"药品名称",width:"500"}},[e("el-input",{attrs:{placeholder:"请输入药品通用名"},model:{value:t.relationParams.name,callback:function(e){t.$set(t.relationParams,"name",e)},expression:"relationParams.name"}})],1)],1),e("el-col",{attrs:{span:8}},[e("el-form-item",{staticClass:"programme-btn"},[e("el-button",{attrs:{icon:"el-icon-delete"},on:{click:t.reset}},[t._v("重置")]),e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.search}},[t._v("查询")])],1)],1)],1)],1),e("el-table",{ref:"multipleTable",staticClass:"cms-table-hide",staticStyle:{width:"100%"},attrs:{data:t.relationList,"header-cell-style":{background:"#f5f7fa"},"max-height":"400","row-key":t.getRowKeys},on:{"selection-change":t.handleSelectionChange}},[e("el-table-column",{attrs:{type:"selection",width:"45","reserve-selection":!0}}),e("el-table-column",{attrs:{label:"药品通用名","show-overflow-tooltip":"",width:"200"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(a.row.catalogName))])]}}])}),e("el-table-column",{attrs:{prop:"dosageForm",width:"150",label:"剂型","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"specs",width:"100","show-tooltip-when-overflow":"",label:"规格"}}),e("el-table-column",{attrs:{prop:"unit",width:"80",label:"单位"}}),e("el-table-column",{attrs:{prop:"packingSpecs","show-tooltip-when-overflow":"",label:"包装规格"}}),e("el-table-column",{attrs:{prop:"packing",width:"100","show-tooltip-when-overflow":"",label:"包装材质"}}),e("el-table-column",{attrs:{prop:"attribute",width:"80","show-tooltip-when-overflow":"",label:"基药属性"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",["0"==a.row.attribute?e("div",{attrs:{type:"success"}},[t._v("空")]):t._e(),"1"==a.row.attribute?e("div",{attrs:{type:"success"}},[t._v("国基")]):t._e(),"2"==a.row.attribute?e("div",{attrs:{type:"success"}},[t._v("省基")]):t._e()])]}}])}),e("el-table-column",{attrs:{prop:"priceArray",label:"价格(元)",align:"left","min-width":"300"},scopedSlots:t._u([{key:"default",fn:function(a){return t._l(a.row.priceArray,function(a,o){return e("span",[e("el-tag",{staticStyle:{width:"200px","margin-bottom":"3px"},attrs:{type:"info"}},[t._v(t._s(t.getDictItemName(a.source))+" ： "+t._s(a.price))]),e("br")],1)})}}])}),e("el-table-column",{attrs:{prop:"standardCode",width:"180","show-tooltip-when-overflow":"",label:"药品本位码"}}),e("el-table-column",{attrs:{prop:"drugCompanyName","min-width":"200px",label:"生产企业","show-tooltip-when-overflow":""}})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{"current-page":t.relationParams.page,"page-size":t.relationParams.limit,layout:"total,prev, pager, next, jumper",total:t.relationParams.total},on:{"current-change":t.handleCurrentChange,"update:currentPage":function(e){return t.$set(t.relationParams,"page",e)},"update:current-page":function(e){return t.$set(t.relationParams,"page",e)}}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[1==t.flag?[e("el-button",{attrs:{type:"primary",plain:""},on:{click:t.changeDrugId}},[t._v("确定")]),e("el-button",{on:{click:t.close}},[t._v("取消")])]:t._e(),0==t.flag?[e("el-button",{attrs:{type:"primary"},on:{click:t.relation}},[t._v("关联")]),e("el-button",{on:{click:t.close}},[t._v("取消")])]:t._e()],2)])},e.staticRenderFns=[]},"ZoV/":function(t,e,a){"use strict";a.r(e);var o=a("t30D"),s=a("KPYa");for(var r in s)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return s[t]})}(r);a("hJT7");var l=a("gp09"),i=Object(l.a)(s.default,o.render,o.staticRenderFns,!1,null,"44e20946",null);e.default=i.exports},dqtc:function(t,e,a){"use strict";a.r(e);var o=a("qZkx"),s=a("iLnE");for(var r in s)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return s[t]})}(r);a("57Ej");var l=a("gp09"),i=Object(l.a)(s.default,o.render,o.staticRenderFns,!1,null,"cb37df9e",null);e.default=i.exports},hJT7:function(t,e,a){"use strict";a("OQvZ")},iLnE:function(t,e,a){"use strict";a.r(e);var o=a("yfn7"),s=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return o[t]})}(r);e.default=s.a},oriS:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var o=r(a("Q9c5")),s=r(a("DWNM"));function r(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[s.default],name:"hospitalUser-list",data:function(){return{flag:1,relationList:[],drugSourceList:[],relationParams:{page:1,limit:10,total:0,name:"",flag:1,batchCode:""},select:[],activeIndex:"1"}},props:{show:{type:Boolean,default:!1},batchCode:{type:String,require:!1},drugList:{type:Array,require:!1}},watch:{show:function(t){if(t){this.relationParams.batchCode=this.batchCode;var e=this.relationParams;this.onQuery(e),this.getDictItem("SOURCE")}}},methods:{getDictItem:function(t){var e=this,a=this.openLoading();this.$http_post(o.default.baseContext+"/supervise/supDictItem/getItemList",{code:t}).then(function(o){var s=o.rows;1==o.state?("SOURCE"==t&&(e.drugSourceList=s),a.close()):(a.close(),e.$message.error(o.message))})},getDictItemName:function(t){for(var e=0;e<this.drugSourceList.length;e++)if(this.drugSourceList[e].value==t)return this.drugSourceList[e].label},reset:function(){this.relationParams.flag=this.flag,this.relationParams.hospitalId=this.hospitalId,this.relationParams.name="",this.onQuery(this.relationParams)},formatter:function(t,e){var a=new Date(t.creationTime),o=a.getFullYear(),s=a.getMonth()+1,r=a.getDate();return o+"-"+(s<10?"0"+s:s)+"-"+(r<10?"0"+r:r)},onQuery:function(t){var e=this;this.$http_post(o.default.baseContext+"/supervise/drugbatch/list",t,!0).then(function(t){if(e.relationList=[],1!=t.state)return!1;var a=t.rows;e.$set(e,"relationList",a),e.relationParams.total=t.records;var o=e;e.$nextTick(function(){if(!(e.select.length>0))for(var t=0;t<o.drugList.length;t++)for(var s=0;s<a.length;s++)a[s].id==o.drugList[t]&&o.chageCheckbox(a[s])})})},chageCheckbox:function(t){console.log("---row",t),this.$refs.multipleTable.toggleRowSelection(t)},search:function(){this.reload()},reload:function(){this.relationParams.flag=this.flag,this.relationParams.hospitalId=this.hospitalId,this.onQuery(this.relationParams)},handleSelectionChange:function(t){this.select=t},getRowKeys:function(t){return t.id},relation:function(){for(var t=this,e="",a=0;a<this.select.length;a++)a==this.select.length-1?e+=this.select[a].id:e+=this.select[a].id+",";if(""!=e){var s=o.default.baseContext+"/supervise/drugbatch/syncOrderItemCountry";this.$http_post(s,{batchCode:this.batchCode,detailIds:e},!0).then(function(e){1==e.state?(t.$message.success("关联成功"),t.reload()):t.$message.error(e.message)})}else this.$message.error("请选择一个要关联药品！")},changeDrugId:function(){for(var t=this,e="",a=0;a<this.select.length;a++)a==this.select.length-1?e+=this.select[a].id:e+=this.select[a].id+",";""!=e?(this.$emit("changeDrugListId",e),this.$nextTick(function(){t.close()})):this.$message.error("请至少选择一个要解除关联的药品！")},handleCurrentChange:function(t){this.relationParams.page=t,this.relationParams.hospitalId=this.hospitalId,this.onQuery(this.relationParams)},relationItem:function(t){"0"==t&&(this.flag=1),"1"==t&&(this.flag=0),this.reload()},close:function(){this.$emit("close")}}}},phry:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0;var o=function(t){return t&&t.__esModule?t:{default:t}}(a("/umX"));e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left flex-row"},[this.adminRole||this.medicalAdmin||this.socialAdmin?e("el-select",{attrs:{size:"small",placeholder:"请选择区划"},model:{value:t.params.regionId,callback:function(e){t.$set(t.params,"regionId",e)},expression:"params.regionId"}},t._l(t.regionList,function(t){return e("el-option",{key:t.code,attrs:{label:t.name,value:t.code}})}),1):t._e(),this.adminRole||this.medicalAdmin||this.socialAdmin||this.areaRegionAdmin?e("el-select",{attrs:{clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:t.params.hospitalName,callback:function(e){t.$set(t.params,"hospitalName",e)},expression:"params.hospitalName"}},t._l(t.hospitalList,function(t){return e("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})}),1):t._e(),e("el-input",{attrs:{placeholder:"请输入药品通用名"},model:{value:t.params.catalogName,callback:function(e){t.$set(t.params,"catalogName",e)},expression:"params.catalogName"}}),e("el-select",{model:{value:t.params.batch,callback:function(e){t.$set(t.params,"batch",e)},expression:"params.batch"}},[e("el-option",{attrs:{label:"--请选择批次--",value:""}}),t._l(t.batchList,function(t){return e("el-option",{key:t.code,attrs:{label:t.batchName,value:t.code}})})],2),e("el-select",{model:{value:t.params.taskStatus,callback:function(e){t.$set(t.params,"taskStatus",e)},expression:"params.taskStatus"}},[e("el-option",{attrs:{label:"--请选择任务状态--",value:""}}),e("el-option",{attrs:{label:"紧急",value:"-1"}}),e("el-option",{attrs:{label:"正常",value:"0"}})],1),e("el-select",{model:{value:t.params.countryTaskStatus,callback:function(e){t.$set(t.params,"countryTaskStatus",e)},expression:"params.countryTaskStatus"}},[e("el-option",{attrs:{label:"--请选择完成情况--",value:""}}),e("el-option",{attrs:{label:"未完成（入库）",value:"0"}}),e("el-option",{attrs:{label:"已完成（入库）",value:"1"}}),e("el-option",{attrs:{label:"未完成（配送）",value:"3"}}),e("el-option",{attrs:{label:"已完成（配送）",value:"4"}})],1),e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1),e("el-button",{attrs:{type:"success",icon:"el-icon-upload"},on:{click:function(e){return t.purchaseExportExcel("")}}},[t._v("Excel 导出")]),this.adminRole||this.medicalAdmin?e("el-button",{attrs:{type:"warning",icon:"el-icon-download"},on:{click:function(e){return t.purchaseImport("")}}},[t._v("导入任务量")]):t._e(),this.adminRole||this.medicalAdmin?e("el-button",{attrs:{type:"primary",icon:"el-icon-s-tools"},on:{click:function(e){return t.add("add")}}},[t._v("设置任务量")]):t._e()],1),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.dataList,"highlight-current-row":"",height:t.tableHeight,border:""},on:{"current-change":t.handleCurrentChange}},[e("el-table-column",{attrs:{label:"批次",width:"180","show-tooltip-when-overflow":""},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t.formatBatch(a.row.batch)))])]}}])}),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"150px","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"catalogName",label:"通用名","min-width":"150px","show-tooltip-when-overflow":"",align:"left"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("a",{staticStyle:{color:"blue"},on:{click:function(e){return t.catalogInfo(a.row)}}},[t._v(t._s(a.row.catalogName))])]}}])}),e("el-table-column",{attrs:{prop:"purchase",label:"任务量（片/支/粒）",width:"140px"}}),e("el-table-column",{attrs:{prop:"totalPurchase",label:"累计采购量（片/支/粒）",width:"170px"}}),e("el-table-column",{attrs:{prop:"totalDeliveryPurchase",label:"累计配送量（片/支/粒）",width:"170px"}}),e("el-table-column",{attrs:{prop:"deliveryRate",formatter:t.deliveryRateFormat,label:"完成率（配送）",width:"100px"}}),e("el-table-column",{attrs:{prop:"totalStockPurchase",label:"累计入库量（片/支/粒）",width:"170px"}}),e("el-table-column",{attrs:{prop:"rate",formatter:t.rateFormat,label:"完成率（入库）",width:"100px"}}),e("el-table-column",{attrs:{prop:"taskStatus",width:"80px",label:"任务状态"},scopedSlots:t._u([{key:"default",fn:function(a){return["-1"==a.row.taskStatus?e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("紧急")])],1):t._e(),"0"==a.row.taskStatus?e("div",[e("el-tag",{attrs:{type:"primary"}},[t._v("正常")])],1):t._e()]}}])}),e("el-table-column",{attrs:{label:"任务时间段",width:"200"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(a.row.taskStartTime.substr(0,10))+" 至 "+t._s(a.row.taskEndTime.substr(0,10)))])]}}])}),this.adminRole||this.medicalAdmin?e("el-table-column",{attrs:{label:"操作",align:"right",width:"150"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{disabled:"-1"!=t.getStatus(a.row),type:"text",size:"small"},on:{click:function(e){return t.sendOfficial(a.row)}}},[t._v("公文通知")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.add("edit",a.row)}}},[t._v("编辑")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.del(a.row)}}},[t._v("删除")])]}}],null,!1,**********)}):t._e()],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)]),e("el-dialog",{attrs:{title:"制定医疗机构约定采购量",visible:t.dialogVisible,width:"45%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("div",{staticClass:"dialog-content"},[e("el-form",{ref:"form",staticClass:"item-form",attrs:{model:t.formData,"label-width":"120px",rules:t.rules}},[e("div",{staticClass:"fromBox"},[e("el-row",{attrs:{type:"flex"}},[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"任务单号",prop:"taskNum"}},[e("el-input",{attrs:{type:"text",placeholder:"请输入任务单号"},model:{value:t.formData.taskNum,callback:function(e){t.$set(t.formData,"taskNum",e)},expression:"formData.taskNum"}})],1)],1)],1),e("el-row",{attrs:{type:"flex"}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"批次",prop:"batch"}},[e("el-select",{attrs:{filterable:"",placeholder:"请选择批次"},on:{change:t.batchChange},model:{value:t.formData.batch,callback:function(e){t.$set(t.formData,"batch",e)},expression:"formData.batch"}},t._l(t.batchList,function(t){return e("el-option",{key:t.code,attrs:{label:t.batchName,value:t.code}})}),1)],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"约定采购量",prop:"purchase"}},[e("el-input",{attrs:{type:"number",placeholder:"请设置约定采购量"},model:{value:t.formData.purchase,callback:function(e){t.$set(t.formData,"purchase",e)},expression:"formData.purchase"}})],1)],1)],1),e("el-row",{attrs:{type:"flex"}},[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"请选择药品",prop:"drugIdList"}},[e("span",{staticStyle:{display:"block"}},[t._v(t._s(t.showNameList))]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:t.showDrugDialog}},[t._v("选择药品")])],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"药品通用名",prop:"catalogName"}},[e("el-input",{attrs:{placeholder:"请输入药品通用名"},model:{value:t.formData.catalogName,callback:function(e){t.$set(t.formData,"catalogName",e)},expression:"formData.catalogName"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"药品规格",prop:"specs"}},[e("el-input",{attrs:{placeholder:"请输入药品规格"},model:{value:t.formData.specs,callback:function(e){t.$set(t.formData,"specs",e)},expression:"formData.specs"}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"药品剂型",prop:"dosageForm"}},[e("el-input",{attrs:{placeholder:"请输入药品剂型"},model:{value:t.formData.dosageForm,callback:function(e){t.$set(t.formData,"dosageForm",e)},expression:"formData.dosageForm"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"医疗机构",prop:"hospitalId"}},[e("el-select",{attrs:{clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:t.formData.hospitalId,callback:function(e){t.$set(t.formData,"hospitalId",e)},expression:"formData.hospitalId"}},t._l(t.hospitalNameList,function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}),1)],1)],1)],1),e("el-row",{attrs:{type:"flex"}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"开始时间",prop:"taskStartTime"}},[e("el-date-picker",{attrs:{disabled:!0,type:"date",format:"yyyy-MM-dd",placeholder:"选择开始时间"},model:{value:t.formData.taskStartTime,callback:function(e){t.$set(t.formData,"taskStartTime",e)},expression:"formData.taskStartTime"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"结束时间",prop:"taskEndTime"}},[e("el-date-picker",{attrs:{disabled:!0,type:"date",placeholder:"选择日期",format:"yyyy-MM-dd"},model:{value:t.formData.taskEndTime,callback:function(e){t.$set(t.formData,"taskEndTime",e)},expression:"formData.taskEndTime"}})],1)],1)],1)],1)])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("确 定")])],1)]),e("relationItem",{attrs:{batchCode:t.batchCode,show:t.drugDialog,drugList:t.formData.drugIdList},on:{close:t.closeRealtionItem,changeDrugListId:t.changeDrugListId}}),e("el-dialog",{staticClass:"upload-box",attrs:{title:"集采药品任务量导入",visible:t.importDialogVisible,"append-to-body":"",width:"40%"},on:{"update:visible":function(e){t.importDialogVisible=e}}},[e("el-form",{ref:"importDataForm",attrs:{"label-position":"left",model:t.uploadData}},[e("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:(0,o.default)({drag:"",limit:1,headers:t.headers,action:t.uploadUrl,"on-preview":t.handlePreview,"on-remove":t.handleRemove,"on-exceed":t.handleExceed,"before-upload":t.beforeUpload,"before-remove":t.beforeRemove,"on-success":t.onSuccess,"on-error":t.onError,multiple:"",accept:".xls,.xlsx","file-list":t.fileList},"multiple",!1)},[e("i",{staticClass:"el-icon-upload"}),e("div",{staticClass:"el-upload__text"},[t._v("将文件拖到此处，或"),e("em",[t._v("点击上传")])]),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("只能上传【 .xlsx / .xls 】文件")])])],1),e("span",{staticStyle:{color:"red"}},[t._v("提示：请下载 Excel 模板或上传深圳市点击交易平台任务量明细的 Excel，按照格式进行导入！")]),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitUpload("")}}},[t._v("确 定")]),e("el-button",{attrs:{type:"warning"},on:{click:function(e){return t.downloadTemplate("")}}},[t._v("下载模板")]),e("el-button",{on:{click:function(e){t.importDialogVisible=!1}}},[t._v("取消")])],1)],1),e("el-dialog",{staticClass:"upload-box",attrs:{title:"4+7药品执行量导入",visible:t.importSpeedDialogVisible,"append-to-body":"",width:"40%"},on:{"update:visible":function(e){t.importSpeedDialogVisible=e}}},[e("el-form",{ref:"importSpeedDataForm",attrs:{"label-position":"left",model:t.uploadSpeedData}},[e("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:(0,o.default)({drag:"",limit:1,headers:t.headers,action:t.uploadUrl,"on-preview":t.handlePreview,"on-remove":t.handleRemove,"on-exceed":t.handleExceed,"before-upload":t.beforeUpload,"before-remove":t.beforeRemove,"on-success":t.onSuccess,"on-error":t.onError,multiple:"",accept:".xls,.xlsx","file-list":t.fileList},"multiple",!1)},[e("i",{staticClass:"el-icon-upload"}),e("div",{staticClass:"el-upload__text"},[t._v("将文件拖到此处，或"),e("em",[t._v("点击上传")])]),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("只能上传【 .xlsx / .xls 】文件")])])],1),e("span",{staticStyle:{color:"red"}},[t._v("提示：请下载 Excel 模板或上传深圳市点击交易平台任务量明细的 Excel，按照格式进行导入！")]),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitUpload("speed")}}},[t._v("确 定")]),e("el-button",{attrs:{type:"warning"},on:{click:function(e){return t.downloadTemplate("speed")}}},[t._v("下载模板")]),e("el-button",{on:{click:function(e){t.importSpeedDialogVisible=!1}}},[t._v("取消")])],1)],1),e("el-dialog",{attrs:{title:t.title,visible:t.dialogVisibleCatalogInfo,width:"45%"},on:{"update:visible":function(e){t.dialogVisibleCatalogInfo=e}}},[e("div",{staticClass:"dialog-content"},[e("el-form",{ref:"form",staticClass:"item-form",attrs:{"label-width":"120px",rules:t.rules}},[e("div",{staticClass:"fromBox"},[e("el-row",{attrs:{type:"flex"}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"本位码",prop:"standardCode"}},[e("el-input",{attrs:{type:"text"},model:{value:t.catalogInfoData.standardCode,callback:function(e){t.$set(t.catalogInfoData,"standardCode",e)},expression:"catalogInfoData.standardCode"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"国家医保码",prop:"medicalInsuranceCode"}},[e("el-input",{attrs:{type:"text"},model:{value:t.catalogInfoData.medicalInsuranceCode,callback:function(e){t.$set(t.catalogInfoData,"medicalInsuranceCode",e)},expression:"catalogInfoData.medicalInsuranceCode"}})],1)],1)],1),e("el-row",{attrs:{type:"flex"}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"规格",prop:"specs"}},[e("el-input",{attrs:{type:"text"},model:{value:t.catalogInfoData.specs,callback:function(e){t.$set(t.catalogInfoData,"specs",e)},expression:"catalogInfoData.specs"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"剂型",prop:"dosageForm"}},[e("el-input",{attrs:{type:"text"},model:{value:t.catalogInfoData.dosageForm,callback:function(e){t.$set(t.catalogInfoData,"dosageForm",e)},expression:"catalogInfoData.dosageForm"}})],1)],1)],1),e("el-row",{attrs:{type:"flex"}},[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"生产企业",prop:"companyName"}},[e("el-input",{attrs:{type:"text"},model:{value:t.catalogInfoData.companyName,callback:function(e){t.$set(t.catalogInfoData,"companyName",e)},expression:"catalogInfoData.companyName"}})],1)],1)],1),e("el-row",{attrs:{type:"flex"}},[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"备注",prop:"companyName"}},[e("el-input",{attrs:{type:"text"},model:{value:t.catalogInfoData.remarks,callback:function(e){t.$set(t.catalogInfoData,"remarks",e)},expression:"catalogInfoData.remarks"}})],1)],1)],1)],1)])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogVisibleCatalogInfo=!1}}},[t._v("取 消")])],1)])],1)},e.staticRenderFns=[]},qZkx:function(t,e,a){"use strict";var o=a("phry");a.o(o,"render")&&a.d(e,"render",function(){return o.render}),a.o(o,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return o.staticRenderFns})},rGKd:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var o=r(a("Q9c5")),s=r(a("ERIh"));function r(t){return t&&t.__esModule?t:{default:t}}var l=o.default.baseContext+"/supervise/supDrugBatch/getBatchList";var i={getBatchList:function(t){s.default.$http_api("GET",l,{},{jsonContentType:!1,responseType:null,addBaseUrl:!0}).then(function(e){1==e.state?function(t){return null!=t&&void 0!==t&&"function"==typeof t}(t)&&t(e):console.warn("查询集采批次失败",e.message)}).catch(function(t){console.warn("查询集采批次失败",t.message)})}};e.default=i},t30D:function(t,e,a){"use strict";var o=a("PsM9");a.o(o,"render")&&a.d(e,"render",function(){return o.render}),a.o(o,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return o.staticRenderFns})},yfn7:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var o=n(a("Q9c5")),s=n(a("DWNM")),r=n(a("XRYr")),l=n(a("rGKd")),i=n(a("ZoV/"));function n(t){return t&&t.__esModule?t:{default:t}}e.default={name:"countryPurchase",mixins:[s.default],components:{relationItem:i.default},data:function(){return{regionList:[{code:"",name:"全部"},{code:"440703",name:"蓬江区"},{code:"440704",name:"江海区"},{code:"440705",name:"新会区"},{code:"440781",name:"台山市"},{code:"440783",name:"开平市"},{code:"440784",name:"鹤山市"},{code:"440785",name:"恩平市"}],setPurchaseDialog:!1,batchList:[],officialShow:!1,importDialogVisible:!1,drugDialog:!1,batchCode:"",importSpeedDialogVisible:!1,headers:{},dataList:[],hospitalList:[],adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1,areaRegionAdmin:!1,params:{taskStatus:"",countryTaskStatus:"",batch:"",page:1,limit:10,records:0,catalogName:"",hospitalName:"",taskNum:"",regionId:""},formData:{drugIdList:[],id:"",taskNum:"",drugId:"",hospitalId:"",taskStartTime:"",taskEndTime:"",purchase:"0",complete:"0",batch:"",dosageForm:"",catalogName:"",specs:""},uploadSpeedData:{taskNum:""},dialogVisible:!1,showNameList:"",tableHeight:100,rules:{drugId:[{required:!0,message:"请选择药品",trigger:"blur"}],hospitalId:[{required:!0,message:"请选择医疗机构",trigger:"blur"}],taskStartTime:[{required:!0,message:"请先设置批次开始时间",trigger:"blur"}],taskEndTime:[{required:!0,message:"请先设置批次结束时间",trigger:"blur"}],purchase:[{required:!0,message:"请输入约定采购量",trigger:"blur"}],batch:[{required:!0,message:"请选择批次",trigger:"blur"}],taskNum:[{required:!0,message:"请输入任务单号",trigger:"blur"}]},catalogNameList:[],hospitalNameList:[],taskNumList:[],spanArr:[],position:"",fileList:[],uploadData:{},title:"",dialogVisibleCatalogInfo:!1,catalogInfoData:{standardCode:"",medicalInsuranceCode:"",specs:"",dosageForm:"",companyName:"",regionId:"",remarks:""}}},props:{},computed:{uploadUrl:function(){return o.default.baseContext+"/file/upload"}},watch:{},methods:{closeRealtionItem:function(){this.drugDialog=!1},changeDrugListId:function(t){console.log(t),this.formData.drugIdList=t.split(","),this.changeDrugId(),this.showNameList="";for(var e=0;e<this.catalogNameList.length;e++)for(var a=0;a<this.formData.drugIdList.length;a++)this.formData.drugIdList[a]==this.catalogNameList[e].id&&(this.showNameList+=""==this.showNameList?this.catalogNameList[e].catalogName:","+this.catalogNameList[e].catalogName)},showDrugDialog:function(){null!=this.formData.batch&&""!=this.formData.batch&&void 0!=this.formData.batch?(this.drugDialog=!0,this.batchCode=this.formData.batch):this.$message.error("请先选择批次!")},changeDrugId:function(){for(var t=this.formData.drugIdList,e=this.formData,a="",o=0;o<t.length;o++)a+=""==a?t[o]:","+t[o];e.drugId=a;var s=this.catalogNameList;if(1==t.length)for(var r=0;r<s.length;r++){var l=s[r];if(t[0]==l.id){e.dosageForm=l.dosageForm,e.catalogName=l.catalogName,e.specs=l.specs;break}}this.formData=e},formatDate:function(t){var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()+1<10?"0"+e.getDate():e.getDate())},batchChange:function(t){for(var e=this,a=0;a<this.batchList.length;a++)this.batchList[a].code==t&&(this.formData.taskStartTime=this.formatDate(this.batchList[a].taskStartTime),this.formData.taskEndTime=this.formatDate(this.batchList[a].taskEndTime),this.formData.drugIdList=[],this.formData.drugId="");var s={batchCode:t,page:1,limit:500,flag:"1"};this.$http_post(o.default.baseContext+"/supervise/drugbatch/list",s,!0).then(function(t){e.catalogNameList=t.rows})},formatBatch:function(t){if(void 0==t||""==t)return"";for(var e=0;e<this.batchList.length;e++)if(this.batchList[e].code==t)return this.batchList[e].batchName},initRole:function(){var t=this.$store.getters.curUser.roleCode;-1!=t.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=t.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=t.indexOf("DELIVERY_ADMIN")?this.areaRegionAdmin=!0:-1!=t.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=t.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)},getHospitalList:function(){var t=this,e=o.default.baseContext+"/supervise/supHospital/all";this.$http_post(e,{}).then(function(e){1==e.state?t.hospitalList=e.row:t.$alert(e.message)})},purchaseExportExcel:function(){var t=this;this.$confirm("确定导出集采完成情况数据吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var e=t.$store.getters.curUser.id,a=t.$store.getters.curUser.roleCode;e||t.$message.error("用户未登录");var s=o.default.baseContext+"/supervise/supCountryPurchase/exportPurchase?hospitalName="+t.params.hospitalName+"&batch="+t.params.batch+"&taskStatus="+t.params.taskStatus+"&userId="+e+"&roleValue="+a+"&catalogName="+t.params.catalogName+"&countryTaskStatus="+t.params.countryTaskStatus+"&regionId="+t.params.regionId;t.$get_blob(s).then(function(e){if("application/json"==e.type){var a=new FileReader;return a.addEventListener("loadend",function(e){t.$message.error(JSON.parse(e.target.result).message)}),void a.readAsText(e)}var o=URL.createObjectURL(e),s=document.createElement("a");s.href=o,s.target="_blank",s.download="集采完成情况数据.xls",s.click()}).catch(function(t){console.log(t)})}).catch(function(t){console.log(t)})},sendOfficial:function(t){var e=this;this.$alert("确定发送公文通知吗","确认信息",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,type:"warning"}).then(function(){var a=t.totalPurchase/t.purchase;a=Number(100*a).toFixed(2)+"%";var s=e.getDiffDate(t.taskStartTime)/365;s=Number(100*s).toFixed(2)+"%",t.completeRate=a,t.statusRate=s;var r=e.openLoading();e.$http_post(o.default.baseContext+"/supervise/supOfficial/sendTemplate/country_purchase",t,!0).then(function(t){1==t.state?e.$message.success("发送成功"):e.$message.error("系统异常"),r.close()})}).catch(function(t){console.log(t)})},getStatus:function(t){var e="0",a=this.getDiffDate(t.taskStartTime)/365,o=t.totalPurchase/t.purchase;return Number(a)==Number(o)&&(e="0"),Number(a)>Number(o)&&(e="-1"),Number(a)<Number(o)&&(e="1"),e},getDiffDate:function(t){var e=new Date(t),a=new Date;return e=new Date(e.getFullYear(),e.getMonth(),e.getDate()),((a=new Date(a.getFullYear(),a.getMonth(),a.getDate())).getTime()-e.getTime())/864e5},onSuccess:function(t,e,a){if(console.log(t),"1"!=t.state)return this.$message.error(t.message),this.$refs.upload.clearFiles(),!1;e.docId=t.row.id,this.uploadData={docId:t.row.id,name:t.row.name}},onError:function(t,e,a){this.rLoading.close(),console.log(t),this.$message.error("上传失败,请重试")},handlePreview:function(t){var e=this.downloadUrl+"?docId="+encodeURI(encodeURI(t.docId))+"&fileName="+t.name;console.log(t);var a=document.createElement("a");a.href=e,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(e)},handleExceed:function(t,e){this.$message.warning("一次只能上传一个Excel文件")},beforeUpload:function(t){if(t.size/1024/1024>20)return this.$message.error("上传文件过大，请分开上传，单个文件最大为20M。"),!1;var e=t.name;return"xlsx"==e.substring(e.length-4)||"xls"==e.substring(e.length-3)||(this.$message.error("上传文件格式只能为【.xlsx】或【.xls】"),!1)},beforeRemove:function(t,e){return!this.beforeUpload(t)||this.$confirm("确定移除 "+t.name+"？")},handleRemove:function(t,e){return this.uploadData={},console.log(t.docId,t,e),!0},objectSpanMethod:function(t){t.row,t.column;var e=t.rowIndex,a=t.columnIndex;if(0===a||1===a){var o=this.spanArr[e];return{rowspan:o,colspan:o>0?1:0}}},rateFormat:function(t){var e=t.totalStockPurchase/t.purchase;return isNaN(e)||""===e?e="--":(e=Number(100*e).toFixed(2),e+="%"),e},deliveryRateFormat:function(t){var e=t.totalDeliveryPurchase/t.purchase;return isNaN(e)||""===e?e="--":(e=Number(100*e).toFixed(2),e+="%"),e},purchaseImport:function(t){this.uploadData={},this.fileList=[],"speed"==t?this.importSpeedDialogVisible=!0:this.importDialogVisible=!0},submitUpload:function(t){var e=this,a=this.openLoading("导入中"),s=this.uploadData.docId;"speed"==t?(this.importSpeedDialogVisible=!1,this.$refs.importSpeedDataForm.validate(function(t){if(!t)return!1;var r=o.default.baseContext+"/supervise/supCountryPurchase/importPurchaseSpeed?docId="+s;e.$http_post(r,{}).then(function(t){a.close(),1==t.state?(e.onQuery(),e.$alert("导入成功，请前往【任务量批次日志】查看导入结果")):e.$message.error(t.message)})})):(this.importDialogVisible=!1,this.$http_post(o.default.baseContext+"/supervise/supCountryPurchase/importPurchase?docId="+s,{}).then(function(t){a.close(),1==t.state?(e.onQuery(),e.$alert("导入成功，请前往【执行量批次日志】查看导入结果")):e.$message.error(t.message)}))},downloadTemplate:function(t){var e=o.default.baseContext+"/file/downloadPurchaseImportTemplate";"speed"==t&&(e=o.default.baseContext+"/file/downloadPurchaseSpeedImportTemplate");var a=document.createElement("a");a.href=e,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(e)},catalogInfo:function(t){this.title=t.catalogName,this.catalogInfoData.standardCode="",this.catalogInfoData.medicalInsuranceCode="",this.catalogInfoData.specs="",this.catalogInfoData.dosageForm="",this.catalogInfoData.companyName="",this.catalogInfoData.standardCode=t.standardCode,this.catalogInfoData.medicalInsuranceCode=t.medicalInsuranceCode,this.catalogInfoData.specs=t.specs,this.catalogInfoData.dosageForm=t.dosageForm,this.catalogInfoData.companyName=t.companyName,this.catalogInfoData.remarks=t.remarks,this.dialogVisibleCatalogInfo=!0},add:function(t,e){var a=this;if("add"==t&&(this.formData={id:"",taskNum:"",drugIdList:[],drugId:"",hospitalId:"",taskStartTime:"",taskEndTime:"",purchase:"0",complete:"0",batch:"",dosageForm:"",catalogName:"",specs:""},this.dialogVisible=!0),"edit"==t){var s=this.openLoading();this.$http_post(o.default.baseContext+"/supervise/supCountryPurchase/info/"+e.id,{}).then(function(t){1==t.state?null!=t.row?(a.batchChange(t.row.batch),a.formData={id:t.row.id,drugId:t.row.drugId,drugIdList:t.row.drugId.split(","),hospitalId:t.row.hospitalId,taskStartTime:t.row.taskStartTime,taskEndTime:t.row.taskEndTime,purchase:t.row.purchase,complete:t.row.complete,batch:t.row.batch,taskNum:t.row.taskNum,dosageForm:t.row.dosageForm,catalogName:t.row.catalogName,specs:t.row.specs},a.dialogVisible=!0):a.$message.error("系统异常"):null!=t.message?a.$message.error(t.message):a.$message.error("系统异常"),s.close()})}},save:function(){var t=this;this.$refs.form.validate(function(e){if(!e)return!1;var a="";a=null!=t.formData.id&&""!=t.formData.id?o.default.baseContext+"/supervise/supCountryPurchase/update":o.default.baseContext+"/supervise/supCountryPurchase/save";var s=t.openLoading();t.$http_post(a,t.formData).then(function(e){1==e.state?(-1!=a.indexOf("update")?t.$message.success("修改成功"):t.$message.success("添加成功"),t.onQuery(),t.dialogVisible=!1):t.$alert(e.message),s.close()})})},del:function(t){var e=this;this.$alert("确定删除【"+t.taskNum+"年 "+t.hospitalName+"】的约定采购量数据吗","确认信息",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,type:"warning"}).then(function(){var a=e.openLoading();e.$http_post(o.default.baseContext+"/supervise/supCountryPurchase/delete/"+t.id,null).then(function(t){1==t.state?(e.$message.success("删除成功"),e.onQuery(),a.close()):(a.close(),e.$message.error(t.message))})}).catch(function(t){console.log(t)})},handleCurrentChange:function(t){},onSearch:function(t){"reset"==t?(this.params.countryTaskStatus="",this.params.taskStatus="",this.params.batch="",this.params.catalogName="",this.params.hospitalName="",this.params.taskNum="",this.params.regionId="",this.onQuery()):(this.params.page=1,this.onQuery())},onPageClick:function(t){this.params.page=t,this.onQuery()},onQuery:function(){var t=this,e=this.params,a=this.openLoading(),s=o.default.baseContext+"/supervise/supCountryPurchase/list";this.$http_post(s,e).then(function(e){if(1==e.state){var o=e.rows;t.dataList=o,t.params.records=e.records,t.rowspan(),a.close()}else a.close(),t.$alert(e.message)})},rowspan:function(){var t=this;this.spanArr=[],this.position=0,this.dataList.forEach(function(e,a){0===a?(t.spanArr.push(1),t.position=0,e.sequence=a+1):t.dataList[a].catalogName===t.dataList[a-1].catalogName&&t.dataList[a].specs===t.dataList[a-1].specs?(t.spanArr[t.position]+=1,t.spanArr.push(0),t.dataList[a].sequence=t.dataList[a-1].sequence):(t.spanArr.push(1),t.position=a,t.dataList[a].sequence=t.dataList[a-1].sequence+1)})},setBatchList:function(t){for(var e=t.rows,a=0;a<e.length;a++)this.batchList.push(e[a])},init:function(){var t=this,e=o.default.baseContext+"/supervise/supHospital/all";this.$http_post(e,{}).then(function(e){1==e.state?t.hospitalNameList=e.row:t.$alert(e.message)});var a=o.default.baseContext+"/supervise/supCountryPurchase/taskNumAll";this.$http_post(a,{}).then(function(e){1==e.state?t.taskNumList=e.row:t.$alert(e.message)})}},mounted:function(){var t=this,e=r.default.doCloundRequest(o.default.app_key,o.default.app_security,"");this.headers["x-aep-appkey"]=e["x-aep-appkey"],this.headers["x-aep-signature"]=e["x-aep-signature"],this.headers["x-aep-timestamp"]=e["x-aep-timestamp"],this.headers["x-aep-nonce"]=e["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,l.default.getBatchList(this.setBatchList),this.init(),this.initRole(),this.onQuery(),this.getHospitalList(),this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null}}}}]);