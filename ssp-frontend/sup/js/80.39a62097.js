(window.webpackJsonp=window.webpackJsonp||[]).push([[80],{"7JOz":function(t,e,a){},Ke8t:function(t,e,a){"use strict";var r=a("xDgc");a.o(r,"render")&&a.d(e,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return r.staticRenderFns})},a8cT:function(t,e,a){"use strict";a.r(e);var r=a("Ke8t"),n=a("ooka");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,function(){return n[t]})}(o);a("qbtU");var i=a("gp09"),l=Object(i.a)(n.default,r.render,r.staticRenderFns,!1,null,"c8027c14",null);e.default=l.exports},iim0:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var r=i(a("cjK8")),n=i(a("Q9c5")),o=i(a("DWNM"));function i(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[o.default],name:"recommendDetail",data:function(){return{activeName:"first",curTab:"0",dialogVisible:!1,orderData:{data:{},orderItem:[],hospital:{}},listLoading:!1,tableHeight:100}},components:{itemDetail:r.default},filters:{formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}},watch:{},mounted:function(){this.$route.query.orderId&&this.getOrderDetail()},methods:{goBack:function(){this.$router.go(-1)},handleClick:function(t,e){this.curTab=t.index},getOrderDetail:function(){var t=this,e=this.openLoading("查询中"),a=n.default.baseContext+"/supervise/supRecommendOrder/show/"+this.$route.query.orderId;this.$http_post(a,{}).then(function(a){1==a.state?(t.orderData=a.row,e.close()):(e.close(),t.$alert(a.message))})}}}},ooka:function(t,e,a){"use strict";a.r(e);var r=a("iim0"),n=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,function(){return r[t]})}(o);e.default=n.a},qbtU:function(t,e,a){"use strict";a("7JOz")},xDgc:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left flex-row"}),e("el-button",{attrs:{type:"info",icon:"el-icon-arrow-left"},on:{click:t.goBack}},[t._v("返回\n    ")])],1),e("el-tabs",{model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[e("el-tab-pane",{attrs:{label:"订单信息",name:"first"}})],1),e("div",{staticClass:"form"},[e("el-form",{ref:"dataForm",staticClass:"item-form",attrs:{id:"orderData2",model:t.orderData,"label-position":"left","label-width":"110px"}},[e("div",{staticClass:"fromBox"},[e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"订单号"}},[e("el-input",{attrs:{value:t.orderData.data.orderNum}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"医疗机构"}},[e("el-input",{attrs:{value:t.orderData.data.hospitalName}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"创建时间"}},[e("el-input",{attrs:{value:t._f("formatTime")(t.orderData.data.creationTime)}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"创建人"}},[e("el-input",{attrs:{value:t.orderData.data.creatorName}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"预警状态"}},[e("el-input",{attrs:{value:t.orderData.data.warning}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"合计"}},[e("el-input",{attrs:{value:t.orderData.data.totalPrice+"元"}})],1)],1)],1)],1)])],1),e("div",{staticClass:"memberTab"},[e("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.curTab,callback:function(e){t.curTab=e},expression:"curTab"}},[e("el-tab-pane",{attrs:{label:"订单明细信息"}},[e("item-detail",{ref:"orderItem",attrs:{name:"0",orderItem:t.orderData.orderItem,operateHide:!0,recommend:!0}})],1)],1)],1)],1)},e.staticRenderFns=[]}}]);