<template>
    <!--医院耗材采购统计-->
    <div class="content" ref="tableH">
        <div class="search-header flex-row">
            <div class="left">
                <el-row :gutter="10">
                    <el-col :span="6" :sm="12" :md="8" :lg="6">
                        <div class="searchct">
                            <div class="searchLabel">医疗机构</div>
                            <div class="searchInput">
                                <el-select size="small" v-model="itemParams.hospitalId" clearable filterable
                                    placeholder="请选择医疗机构">
                                    <el-option v-for="item in hospitals" :key="item.id" :label="item.name"
                                        :value="item.id">
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="6" :sm="12" :md="8" :lg="6">
                        <div class="searchct">
                            <div class="searchLabel">配送时间</div>
                            <div class="searchInput">
                                <el-date-picker size="small" unlink-panels value-format="yyyy-MM-dd"
                                    v-model="itemParams.submitTime" type="daterange" :picker-options="pickerOptions"
                                    range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" align="left">
                                </el-date-picker>
                            </div>
                        </div>
                    </el-col>
                    <!-- <el-col :span="6" :sm="12" :md="8" :lg="6">
                        <div class="searchct">
                            <div class="searchLabel">采购平台</div>
                            <div class="searchInput">
                                <el-select size="small" v-model="itemParams.platform" placeholder="全部">
                                    <el-option key="" label="全部" value=""></el-option>
                                    <el-option v-for="item in platforms" :key="item.id" :label="item.name"
                                        :value="item.id">
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                    </el-col> -->
                    <el-col :span="6" :sm="12" :md="8" :lg="6">
                        <div class="searchct">
                            <div class="searchLabel">区划</div>
                            <div class="searchInput">
                                <el-select size="small" clearable v-model="itemParams.regionId" placeholder="请选择区划"
                                    @change="changeRegionName">
                                    <el-option v-for="item in regionList" :key="item.code" :label="item.name"
                                        :value="item.code">
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </div>
            <div class="right">
                <div class="button-group">
                    <el-button size="small" type="primary" icon="el-icon-search" @click="initTableData()">查询</el-button>
                    <el-button size="small" type="success" icon="el-icon-search" @click="exportList()">导出</el-button>
                    <el-button size="small" type="primary" plain icon="el-icon-refresh-left"
                        @click="handleReset">重置</el-button>
                </div>
            </div>
        </div>

        <!-- 主表格区 -->
        <div class="hospital-table">
            <div class="table-title">耗材配送统计列表</div>
            <el-table :data="tableData" border 
          :height="tableHeight"  v-loading="loading"
            >
            <el-table-column prop="regionName" label="区划" align="center" min-width="180">
                       
                    </el-table-column>
                <el-table-column prop="hospitalName" label="医疗机构" align="center" min-width="200">
                </el-table-column>
   

                <el-table-column prop="totalPackingAmount" sortable label="配送最小单位总数量" align="center" min-width="200">
                    <template slot-scope="scope">
                        <span>{{ formatAmount(scope.row.totalPackingAmount) }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="totalPrice" sortable label="配送总金额(元)" align="center" min-width="180">
                    <template slot-scope="scope">
                        <span>{{ formatAmount(scope.row.totalPrice) }}</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="deliveryRawPercentage" label="配送金额占比" align="center" min-width="150">
                </el-table-column> -->
               
                <el-table-column label="操作" align="center" width="120">
                    <template slot-scope="scope">
                        <el-button type="primary" size="mini" @click="showDetails(scope.row)">详情</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页组件 -->
            <div class="pagination-container">
                <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="pagination.currentPage" :page-sizes="pagination.pageSizes"
                    :page-size="pagination.pageSize" :layout="pagination.layout" :total="pagination.total">
                </el-pagination>
            </div>
        </div>



        <!-- 详情模态框 -->
        <el-dialog :title="currentHospital + ' - 配送统计详情'" :visible.sync="dialogVisible" width="80%"
            :before-close="handleClose">
            <div class="dialog-content">
                <div class="dialog-header">
                    <div class="left">
                        <i class="el-icon-data-analysis"></i>
                        <span>配送分类统计</span>
                    </div>
                </div>
           
                <el-table :data="detailData" border :cell-style="{ 'text-align': 'center' }"
                    :header-cell-style="{ 'text-align': 'center', 'background-color': '#f5f7fa' }" stripe
                    highlight-current-row>
                    <el-table-column prop="type" label="配送类型" align="center" min-width="180">
                        <template slot-scope="scope">
                            <el-tag :type="getTagType(scope.row.type)">{{ scope.row.type }}</el-tag>
                        </template>
                    </el-table-column>

                    <el-table-column prop="packingAmount" label="配送最小单位总数量" align="center" min-width="180">
                        <template slot-scope="scope">
                            <span class="amount">{{ formatAmount(scope.row.packingAmount) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="price" label="配送总金额(元)" align="center" min-width="150">
                        <template slot-scope="scope">
                            <span class="amount">{{ formatAmount(scope.row.price) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="percentage" label="配送金额占比" align="center" min-width="180">
                        <template slot-scope="scope">
                            <div class="percentage-wrapper">
                                <el-progress :percentage="(scope.row).deliveryRawPercentage"
                                    :color="getProgressColor(scope.row.type, (scope.row).deliveryRawPercentage)"
                                    :show-text="false"></el-progress>
                                <span class="percentage-text">{{ (scope.row).deliveryRawPercentage }}</span>
                            </div>
                        </template>
                    </el-table-column>
                
                </el-table>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="dialogVisible = false">确定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import base from '@_src/core/base';
import Config from "@_src/api/app.config";
export default {
    mixins: [base],
    name: "material_list",
    data() {
        return {
            loading: false,
            itemParams: {
                hospitalId: '',
                type:1,
                platform: '',
                drugType: '',
            
                attribute: "",
                source: "",
                submitTime: [new Date((new Date().getTime() - 3600 * 1000 * 24 * 30)).format('yyyy-MM-dd'), new Date().format('yyyy-MM-dd')],
                hospitalName: "",
                currentPage: 1,    // 当前页码
                pageSize: 10,   
            },
            pickerOptions: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            tableHeight: 500,
            dialogVisible: false,
            currentHospital: '',
            detailData: [],
            // 医院列表
            hospitals: [
                { id: 1, name: '鄂山西工医院（普通合伙）' },
                { id: 2, name: '江门市中心医院' },
                { id: 3, name: '江门市五邑中医院' },
                { id: 4, name: '江门市人民医院' },
                { id: 5, name: '江门市妇幼保健院' },
                { id: 6, name: '江门市第二人民医院' },
                { id: 7, name: '江门市第三人民医院' },
                { id: 8, name: '江门市结核病防治所' },
                { id: 9, name: '江门市蓬江区杜阮卫生院' },
                { id: 10, name: '江门市江海区人民医院' },
            ],
            // 采购平台
            platforms: [
                { id: 1, name: '深圳市药品交易平台' },
                { id: 2, name: '广东省药品交易平台' },
                { id: 3, name: '广州市药品交易平台' }
            ],
            // 分页设置
            pagination: {
                currentPage: 1,    // 当前页码
                pageSize: 10,      // 每页条数
                total: 0,          // 总条数
                pageSizes: [10, 20, 50, 100], // 每页条数选项
                layout: 'total, sizes, prev, pager, next, jumper' // 分页组件布局
            },
            // 所有表格数据（用于本地分页）
            allTableData: [],
            // 当前页表格数据
            tableData: [],
            areaRegoniName: '',
            regionList: [
                {
                    code: '',
                    name: '全部'
                },
                {
                    code: '440703',
                    name: '蓬江区'
                }, {
                    code: '440704',
                    name: '江海区'
                }, {
                    code: '440705',
                    name: '新会区'
                }, {
                    code: '440781',
                    name: '台山市'
                }, {
                    code: '440783',
                    name: '开平市'
                }, {
                    code: '440784',
                    name: '鹤山市'
                }, {
                    code: '440785',
                    name: '恩平市'
                }
            ],
        };
    },
    watch: {
        // 监听表格数据变化，重新计算表格高度
        tableData: {
            handler() {
                this.$nextTick(() => {
                    this.calcTableHeight();
                });
            },
            deep: true
        },
        // 监听分页变化，重新计算表格高度
        'pagination.currentPage'() {
            this.$nextTick(() => {
                this.calcTableHeight();
            });
        },
        'pagination.pageSize'() {
            this.$nextTick(() => {
                this.calcTableHeight();
            });
        }
    },
    mounted() {
        // 初始化时计算表格高度
        this.$nextTick(() => {
            this.calcTableHeight();
            // 添加窗口大小变化监听，以便在调整窗口大小时重新计算表格高度
            window.addEventListener('resize', this.calcTableHeight);
            
            // 定时检查表格高度，确保正确显示（解决某些异步加载问题）
            this.heightCheckInterval = setInterval(() => {
                this.calcTableHeight();
            }, 1000);
            
            // 5秒后清除定时器
            setTimeout(() => {
                if (this.heightCheckInterval) {
                    clearInterval(this.heightCheckInterval);
                    this.heightCheckInterval = null;
                }
            }, 5000);
        });
        
        this.getHospitalList();
       
        // 初始化表格数据
        this.initTableData();
    },
    updated() {
        // 在组件更新后也触发高度计算
        this.$nextTick(() => {
            this.calcTableHeight();
        });
    },
    beforeDestroy() {
        // 组件销毁前移除事件监听
        window.removeEventListener('resize', this.calcTableHeight);
        
        // 清除定时器
        if (this.heightCheckInterval) {
            clearInterval(this.heightCheckInterval);
            this.heightCheckInterval = null;
        }
    },
    methods: {


        changeRegionName(val) {
            for (var i = 0; i < this.regionList.length; i++) {
                if (val == this.regionList[i].code) {
                    this.areaRegoniName = this.regionList[i].name == '全部' ? '全市合计' : this.regionList[i].name
                    //   this.onQuery();
                    break;
                }
            }

        },
        //
        getHospitalList() {
            var url = Config.baseContext + "/supervise/supHospital/all";
            this.$http_post(url, {}).then(ret => {
                if (ret.state == 1) {
                    this.hospitals = ret.row;
                } else {
                    this.$alert(ret.message)
                }
            });
        },

        // 初始化表格数据
        initTableData() {
            this.loading = true
            var url = Config.baseContext + '/supervise/statistics/getMaterialSourceAmountByHospital';

            this.$http_post(url, this.itemParams).then(ret => {
           
                if (ret.state == 1) {
                    if (ret.rows != null) {
                        this.loading = false
                        // console.log(ret)
                        // this.$set(this,"amountBarData",ret.rows);
                        this.pagination.total = ret.records
                        this.tableData = ret.rows
                        
                        // 数据加载完成后，重新计算表格高度
                        this.$nextTick(() => {
                            this.calcTableHeight();
                        });
                    }
                } else {
                    this.loading = false
                    if (ret.message != null) {
                        this.$message.error(ret.message);
                    } else {
                        this.$message.error("系统异常");
                    }
                }
            }).catch(err => {
                console.error('获取数据失败:', err);
                this.$message.error("获取数据失败");
                this.loading = false
            });
        },

    // 导出
    exportList() {
            // this.params.country = this.$route.query.country;
            this.$confirm('确定导出耗材配送统计数据吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let userId = this.$store.getters.curUser.id;
                if (!userId) {
                    this.$message.error("用户未登录");
                }
                this.loading = true
                var url = Config.baseContext + "/supervise/statistics/exportMaterialSourceAmountByHospital"
                this.$post_blob(url,  this.itemParams).then(blob => {
                    if (blob.type == "application/json") {
                        var reader = new FileReader()
                        reader.addEventListener('loadend', (e) => {
                            this.$message.error(JSON.parse(e.target.result).message);
                        });
                        reader.readAsText(blob);
                        return;
                    }
                    let objectUrl = URL.createObjectURL(blob)
                    var a = document.createElement('a');
                    a.href = objectUrl;
                    a.target = "_blank";
                    let time = this.itemParams.submitTime[0] +'-' +this.itemParams.submitTime[1] 
                    //设置文件名称
                    a.download = "耗材配送统计" + time +'.xls';
                    a.click();
                    this.loading = false;
                }).catch(err => {
                    console.log(err);
                });
            }).catch(err => {
                console.log(err);
            });
        },


        // 页码变化处理
        handleCurrentChange(currentPage) {
          
            this.itemParams.currentPage = currentPage;
                
            this.pagination.currentPage = currentPage;
            this.initTableData();
        },

        // 每页条数变化处理
        handleSizeChange(pageSize) {
         
           this.pagination.pageSize = pageSize;
            this.itemParams.pageSize = pageSize;
        
          
            this.initTableData();
        },

        // 计算表格高度
        calcTableHeight() {
            // 获取页面高度减去其他元素的高度，为表格留出更大空间
            try {
                if (!this.$refs.tableH) return;
                
                // 获取容器高度
                const containerHeight = this.$refs.tableH.clientHeight;
                
                // 获取搜索区域和分页区域的高度
                const searchHeader = this.$refs.tableH.querySelector('.search-header');
                const searchHeight = searchHeader ? searchHeader.offsetHeight : 0;
                
                const tableTitle = this.$refs.tableH.querySelector('.table-title');
                const tableTitleHeight = tableTitle ? tableTitle.offsetHeight : 0;
                
                const pagination = this.$refs.tableH.querySelector('.pagination-container');
                const paginationHeight = pagination ? pagination.offsetHeight : 70;
                
                // 计算其他元素的总高度，包括间距和内边距
                const otherElementsHeight = searchHeight + tableTitleHeight + paginationHeight + 60; // 60px for margins and paddings
                
                // 计算表格可用高度
                const availableHeight = containerHeight - otherElementsHeight;
                
                // 设置最小高度为300px，避免表格过小
                const newHeight = Math.max(availableHeight, 300);
                
                // 只有当高度发生变化时才更新，避免不必要的重绘
                if (this.tableHeight !== newHeight) {
                    this.tableHeight = newHeight;
                }
            } catch (error) {
                console.error('计算表格高度时出错:', error);
                this.tableHeight = 500; // 出错时使用默认高度
            }
        },

        // 显示详情模态框
        showDetails(row) {
            this.currentHospital = row.hospitalName;
            this.detailData = row.materialSourceAmountVoList;
            this.dialogVisible = true;
        },

        // 关闭模态框
        handleClose() {
            this.dialogVisible = false;
        },

        // 格式化金额，添加千位分隔符
        formatAmount(amount) {
            if(amount == null){
                return 0;
            }
            
            return amount.toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        },

        // 获取子表格数据
        getSubTableData(hospitalName) {


            // 对于其他医院，生成一些随机的测试数据
            const total = this.tableData.find(item => item.name === hospitalName);
            if (!total) return [];

            // 分配比例 - 75%国家集采，20%非国家集采，5%线下采购
            const nationalCount = Math.round(total.drugCount * 0.75);
            const nonNationalCount = Math.round(total.drugCount * 0.2);
            const offlineCount = total.drugCount - nationalCount - nonNationalCount;

            const nationalAmount = Math.round(total.totalAmount * 0.75 * 100) / 100;
            const nonNationalAmount = Math.round(total.totalAmount * 0.2 * 100) / 100;
            const offlineAmount = Math.round((total.totalAmount - nationalAmount - nonNationalAmount) * 100) / 100;

            return [
                {
                    type: '国家集采',
                    drugCount: nationalCount,
                    totalCount: Math.round(total.totalCount * 0.75),
                    totalUnitCount: Math.round(total.totalUnitCount * 0.75),
                    totalAmount: nationalAmount,
                    rawPercentage: '75.00%',
                    percentage: 75
                },
                {
                    type: '非国家集采',
                    drugCount: nonNationalCount,
                    totalCount: Math.round(total.totalCount * 0.2),
                    totalUnitCount: Math.round(total.totalUnitCount * 0.2),
                    totalAmount: nonNationalAmount,
                    rawPercentage: '20.00%',
                    percentage: 20
                },
                {
                    type: '线下采购',
                    drugCount: offlineCount,
                    totalCount: Math.round(total.totalCount * 0.05),
                    totalUnitCount: Math.round(total.totalUnitCount * 0.05),
                    totalAmount: offlineAmount,
                    rawPercentage: '5.00%',
                    percentage: 5
                }
            ];
        },

        // 格式化百分比数据
        formatPercentage(row) {
            let percentage = 100;
            let rawPercentage = '100%';

            if (row.rawPercentage) {
                // 解析 rawPercentage 中的数值
                percentage = parseFloat(row.rawPercentage.replace('%', ''));
                rawPercentage = row.rawPercentage;
            } else if (row.percentage) {
                percentage = parseFloat(row.percentage);
                rawPercentage = percentage + '%';
            }

            return {
                percentage: percentage,
                rawPercentage: rawPercentage
            };
        },

        // 获取标签类型
        getTagType(type) {
            // 根据采购类型返回不同的标签类型
            switch (type) {
                case '国家集采':
                    return 'success';
                case '非国家集采':
                    return 'info';
                case '线下采购':
                    return 'warning';
                default:
                    return '';
            }
        },

        // 获取进度条颜色
        getProgressColor(type, percentage) {
            // 根据进度值设置颜色
            if (percentage !== undefined) {
                if (percentage < 30) {
                    return '#F56C6C'; // 红色
                } else if (percentage < 60) {
                    return '#E6A23C'; // 橙色
                } else if (percentage < 80) {
                    return '#909399'; // 灰色
                } else {
                    return '#67C23A'; // 绿色
                }
            }

            // 如果没有进度值，则根据类型设置颜色
            if (!type) {
                return '#409EFF'; // 主表格默认使用蓝色
            }

            switch (type) {
                case '国家集采':
                    return '#67C23A';
                case '非国家集采':
                    return '#909399';
                case '线下采购':
                    return '#E6A23C';
                default:
                    return '#409EFF'; // 默认蓝色
            }
        },

        // 重置处理
        handleReset() {
            // 重置查询条件
            this.itemParams = {
                hospitalId: '',
                submitTime: [new Date((new Date().getTime() - 3600 * 1000 * 24 * 30)).format('yyyy-MM-dd'), new Date().format('yyyy-MM-dd')],
                platform: '',
                drugType: '',
                regionId:'',
                pageSize:10,
                currentPage:1,
                type:1
            };

            // 重置分页状态
            this.pagination.currentPage = 1;
            this.initTableData();
        }
    }
}
</script>

<style scoped lang="less">
.content {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    padding: 10px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.breadcrumb-container {
    padding-bottom: 15px;
    margin-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
    width: 100%;

    .page-title {
        font-size: 22px;
        font-weight: bold;
        color: #303133;
        margin-top: 12px;
        position: relative;
        padding-left: 12px;

        &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background-color: #409EFF;
            border-radius: 2px;
        }
    }
}

.search-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    padding: 10px;
    border-bottom: 1px solid #ebeef5;
    width: 100%;
    background-color: #f9f9f9;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    .left {
        flex: 1;
    }

    .right {
        display: flex;
        align-items: flex-end;
        height: 100%;
        margin-left: 20px;
        padding-bottom: 10px;

        .button-group {
            display: flex;
            gap: 10px;
        }
    }
}

.searchct {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    height: 32px;
    flex-wrap: wrap;

    .searchLabel {
        min-width: 70px;
        width: auto;
        text-align: right;
        margin-right: 10px;
        color: #606266;
        font-weight: normal;
        white-space: nowrap;
    }

    .searchInput {
        flex: 1;
        min-width: 160px;
        
        /deep/ .el-input__inner,
        /deep/ .el-range-editor.el-input__inner,
        /deep/ .el-select {
            width: 100%;
        }

        /deep/ .el-date-editor--daterange.el-input__inner {
            width: 100%;
            .el-range-separator {
                padding: 0 2px;
                line-height: 24px;
            }
            .el-range-input {
                width: 38%;
            }
        }
    }
}

@media screen and (max-width: 1200px) {
    /deep/ .el-date-editor.el-range-editor {
        .el-range-input {
            width: 36%;
            font-size: 12px;
        }
        .el-range-separator {
            padding: 0 2px;
        }
    }
    
    .searchct {
        height: auto;
        min-height: 32px;
    }
}

@media screen and (max-width: 768px) {
    .searchct {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 12px;
        
        .searchLabel {
            width: 100%;
            text-align: left;
            margin-bottom: 4px;
        }
        
        .searchInput {
            width: 100%;
        }
    }
}

.hospital-table {
    margin-bottom: 5px;
    transition: all 0.3s ease;
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 450px;

    .table-title {
        font-size: 18px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 8px;
        position: relative;
        padding-left: 12px;
        
        &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 18px;
            background-color: #409EFF;
            border-radius: 2px;
        }
    }

    .table-container {
        width: 100% !important;
        flex: 1;
    }

    .inner-table {
        width: 100% !important;
    }

    // 表格内部的展开图标样式
    /deep/ .el-table__expand-icon {
        font-size: 16px;
        transition: transform 0.3s ease;

        .el-icon {
            color: #409EFF;
        }

        &.el-table__expand-icon--expanded {
            transform: rotate(90deg);
        }
    }

    // 设置表格行的样式
    /deep/ .el-table__row {
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
            background-color: #f0f9ff !important;
        }
    }

    // 表格展开行内容区域样式
    /deep/ .el-table__expanded-cell {
        background-color: #f5f7fa;
        padding: 20px !important;

        // 子表格样式
        .el-table {
            margin-bottom: 0;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            width: 100% !important;
            margin: 0 auto;
        }
    }
}

/deep/ .el-table {
    min-height: 400px;
}

/deep/ .el-table th {
    font-weight: bold;
    background-color: #f5f7fa !important;
}

/deep/ .el-table--border {
    border-radius: 4px;
    overflow: hidden;
}

/deep/ .el-table__header {
    th {
        background-color: #eef1f6 !important;
        color: #606266;
    }
}

// 高亮选中行
/deep/ .el-table__row.current-row {
    background-color: #e6f0fc !important;
}

// 美化按钮
/deep/ .el-button {
    transition: all 0.3s;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
}

/deep/ .el-button+.el-button {
    margin-left: 10px;
}

/deep/ .el-select {
    width: 100%;
}

/deep/ .el-date-editor.el-input,
/deep/ .el-date-editor.el-input__inner {
    width: 100%;
}

/deep/ .el-input__icon {
    line-height: 32px;
}

/deep/ .el-input__inner {
    height: 32px;
    line-height: 32px;
}

/deep/ .el-range-editor.el-input__inner {
    padding: 3px 10px;
    /* 小屏幕下优化日期选择器内部元素间距 */
    .el-range__icon, .el-range__close-icon {
        line-height: 24px;
    }
}

/deep/ .el-range-separator {
    line-height: 24px;
    padding: 0 2px;
}

/* 针对日期选择器的特殊优化 */
@media screen and (max-width: 1366px) {
    /deep/ .el-range-editor--small.el-input__inner {
        height: 32px;
        
        .el-range-input {
            font-size: 12px; /* 减小字体大小 */
        }
        
        .el-range-separator {
            padding: 0 2px;
            font-size: 12px;
        }
    }
    
    /deep/ .el-date-editor .el-range__icon {
        margin-right: 0;
    }
}

// 添加空数据时的美化
/deep/ .el-table__empty-block {
    min-height: 80px;

    .el-table__empty-text {
        line-height: 60px;
        color: #909399;
    }
}

// 详情按钮样式
/deep/ .el-table .cell .el-button--mini {
    padding: 7px 10px;
    font-weight: normal;
}

// 模态框样式
/deep/ .el-dialog {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

    .el-dialog__header {
        background-color: #f5f7fa;
        padding: 15px 20px;
        border-bottom: 1px solid #e4e7ed;

        .el-dialog__title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
        }

        .el-dialog__headerbtn {
            top: 16px;

            .el-dialog__close {
                font-size: 18px;

                &:hover {
                    color: #409EFF;
                }
            }
        }
    }

    .el-dialog__body {
        padding: 0;
    }

    .el-dialog__footer {
        border-top: 1px solid #e4e7ed;
        padding: 15px 20px;
    }
}

// 增加表格最后一列的按钮列宽度适应性
/deep/ .el-table .el-table__row td:last-child .cell {
    display: flex;
    justify-content: center;
}

.dialog-content {
    padding: 20px;
}

.dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .left {
        display: flex;
        align-items: center;
    }

    .el-icon-data-analysis {
        font-size: 20px;
        margin-right: 10px;
        color: #409EFF;
    }

    span {
        font-size: 16px;
        font-weight: bold;
    }
}

.percentage-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;

    .el-progress {
        margin-right: 10px;
        flex: 1;

        /deep/ .el-progress-bar__outer {
            height: 8px;
            border-radius: 4px;
            background-color: #ebeef5;
        }

        /deep/ .el-progress-bar__inner {
            border-radius: 4px;
            transition: width 0.6s ease;
        }
    }

    .percentage-text {
        min-width: 50px;
        text-align: right;
        font-weight: bold;
        color: #606266;
        font-size: 14px;
    }
}

.amount {
    color: #F56C6C;
    font-weight: bold;
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
    padding: 5px 0;
}

/deep/ .el-table td, /deep/ .el-table th {
    padding: 8px 0;
}

/deep/ .el-table .el-table__row {
    height: 42px;
}
</style>