(window.webpackJsonp=window.webpackJsonp||[]).push([[32],{"5L02":function(e,t,r){"use strict";r.r(t);var a=r("jX2M"),o=r.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){r.d(t,e,function(){return a[e]})}(n);t.default=o.a},"5Rlz":function(e,t,r){"use strict";r("vIW0")},"7a1e":function(e,t,r){"use strict";r.r(t);var a=r("AHSE"),o=r("KDzZ");for(var n in o)["default"].indexOf(n)<0&&function(e){r.d(t,e,function(){return o[e]})}(n);r("5Rlz");var i=r("gp09"),s=Object(i.a)(o.default,a.render,a.staticRenderFns,!1,null,"206521ee",null);t.default=s.exports},AHSE:function(e,t,r){"use strict";var a=r("T0y/");r.o(a,"render")&&r.d(t,"render",function(){return a.render}),r.o(a,"staticRenderFns")&&r.d(t,"staticRenderFns",function(){return a.staticRenderFns})},Dapj:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{staticClass:"content"},[t("div",{staticClass:"on-operating"},[t("el-button",{attrs:{type:"info",icon:"el-icon-arrow-left"},on:{click:function(t){return e.$router.push({path:"payVoucher",query:{country:e.$route.query.country}})}}},[e._v("返回")])],1),t("el-tabs",{model:{value:e.orderActive,callback:function(t){e.orderActive=t},expression:"orderActive"}},[t("el-tab-pane",{attrs:{label:"订单信息",name:"first"}})],1),t("div",{staticClass:"form"},[t("el-form",{ref:"dataForm",staticClass:"item-form",attrs:{id:"orderData2",model:e.orderData,"label-position":"left","label-width":"110px"}},[t("div",{staticClass:"fromBox"},[t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"订单号"}},[t("el-input",{attrs:{value:e.orderData.data.orderNum}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"医疗机构"}},[t("el-input",{attrs:{value:e.orderData.data.hospitalName}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"采购时间"}},[t("el-input",{attrs:{value:e._f("formatTime")(e.orderData.data.submitTime)}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"提交人"}},[t("el-input",{attrs:{value:e.orderData.data.userName}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"支付状态"}},["0"==e.orderData.data.payStatus?t("el-input",{attrs:{value:"未支付"}}):e._e(),"1"==e.orderData.data.payStatus?t("el-input",{attrs:{value:"已支付"}}):e._e(),"2"==e.orderData.data.payStatus?t("el-input",{attrs:{value:"部分支付"}}):e._e()],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"订单状态"}},["0"==e.orderData.data.status?t("el-input",{attrs:{value:"暂存"}}):e._e(),"1"==e.orderData.data.status?t("el-input",{attrs:{value:"已提交"}}):e._e(),"2"==e.orderData.data.status?t("el-input",{attrs:{value:"已完成"}}):e._e()],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"预警状态"}},[t("el-input",{attrs:{value:e.orderData.data.warning}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"结束时间"}},[t("el-input",{attrs:{value:e._f("formatTime")(e.orderData.data.endTime)}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"合计"}},[t("el-input",{attrs:{value:e.orderData.data.totalPrice+"元"}})],1)],1)],1)],1)])],1),t("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[t("el-tab-pane",{attrs:{label:"订单项信息"}},[t("item-detail",{ref:"tab0",attrs:{name:"0",orderItem:e.orderData.orderItem,operateHide:!0}})],1),t("el-tab-pane",{attrs:{label:"支付凭证信息",name:"1"}},[t("pay-voucher",{ref:"payVoucher",attrs:{name:"1",orderPays:e.orderData.orderPays,payStatus:e.payStatus},on:{editOrderPay:e.editOrderPay,deleteOrderPay:e.deleteOrderPay}})],1)],1),t("el-dialog",{attrs:{title:this.title,visible:e.dialogVisible,width:"60%"},on:{close:e.close}},[t("div",{staticClass:"dialog-content"},[t("el-form",{ref:"orderPay",staticClass:"item-form",attrs:{id:"orderPay",model:e.orderPay,"label-width":"90px"}},[t("div",{staticClass:"fromBox"},[t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"支付金额",prop:"payPrice",rules:e.rules.payPrice}},[t("el-input",{staticStyle:{width:"200px"},attrs:{type:"number","inline-message":"true",placeholder:"请输入实际支付价格"},model:{value:e.orderPay.payPrice,callback:function(t){e.$set(e.orderPay,"payPrice",e._n(t))},expression:"orderPay.payPrice"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"支付时间",prop:"payTime",rules:e.rules.payTime}},[t("el-date-picker",{attrs:{type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss",size:"small",placeholder:"请选择支付时间"},model:{value:e.orderPay.payTime,callback:function(t){e.$set(e.orderPay,"payTime",t)},expression:"orderPay.payTime"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"凭证上传"}},[t("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{headers:e.headers,action:e.uploadUrl,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"before-upload":e.beforeUpload,"before-remove":e.beforeRemove,"on-success":e.onSuccess,"on-error":e.onError,multiple:"",accept:".gif,.jpg,.jpeg,.bmp,.png,.doc,.docx,.txt,.xlsx,.xls,.pdf","file-list":e.fileList}},[t("el-button",{attrs:{size:"small"}},[e._v("点击上传")])],1)],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"备注",prop:"remark"}},[t("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.orderPay.remark,callback:function(t){e.$set(e.orderPay,"remark",t)},expression:"orderPay.remark"}})],1)],1)],1)],1)])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:e.saveOrderPay}},[e._v("确定")]),t("el-button",{on:{click:e.close}},[e._v("取消")])],1)])],1)},t.staticRenderFns=[]},H3mY:function(e,t,r){"use strict";var a=r("Dapj");r.o(a,"render")&&r.d(t,"render",function(){return a.render}),r.o(a,"staticRenderFns")&&r.d(t,"staticRenderFns",function(){return a.staticRenderFns})},KDzZ:function(e,t,r){"use strict";r.r(t);var a=r("LcKc"),o=r.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){r.d(t,e,function(){return a[e]})}(n);t.default=o.a},LcKc:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var a=n(r("Q9c5")),o=n(r("DWNM"));function n(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[o.default],name:"payVoucher-list",data:function(){return{downloadUrl:a.default.baseContext+"/file/download"}},props:{orderPays:{type:Array,default:function(){}},payStatus:{type:String,default:""}},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ")+(t.getHours()<10?"0"+t.getHours()+":":t.getHours()+":")+(t.getMinutes()<10?"0"+t.getMinutes()+":":t.getMinutes()+":")+(t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds())}},mounted:function(){},methods:{downloadFile:function(e,t){var r=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,a=document.createElement("a");a.href=r,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(r)},deleteOrderPay:function(e){this.$emit("deleteOrderPay",e)},editOrderPay:function(e){this.$emit("editOrderPay",e)}}}},MD6x:function(e,t,r){"use strict";r.r(t);var a=r("H3mY"),o=r("5L02");for(var n in o)["default"].indexOf(n)<0&&function(e){r.d(t,e,function(){return o[e]})}(n);r("flwu");var i=r("gp09"),s=Object(i.a)(o.default,a.render,a.staticRenderFns,!1,null,"4de30b7e",null);t.default=s.exports},"T0y/":function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"item-item"},[t("div",{staticClass:"flex-row-default"},[t("div",{staticClass:"box-card box-right"},[t("div",{staticClass:"text item"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:!1,expression:"false"}],staticClass:"data_table",staticStyle:{width:"100%"},attrs:{"header-cell-style":{background:"#f5f7fa"},data:e.orderPays,border:"","highlight-current-row":""}},[t("el-table-column",{attrs:{label:"序号",width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(t.$index+1)+"\n                        ")]}}])}),t("el-table-column",{attrs:{label:"支付金额(元)",align:"center",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(t.row.payPrice)+"\n                        ")]}}])}),t("el-table-column",{attrs:{width:"180px",label:"支付时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(e._f("formatTime")(t.row.payTime))+"\n                        ")]}}])}),t("el-table-column",{attrs:{width:"300px",label:"支付凭证",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return e._l(JSON.parse(r.row.docInfo),function(r,a){return t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.downloadFile(r.docId,r.name)}}},[e._v("\n                                "+e._s(r.name)+"\n                            ")])})}}])}),t("el-table-column",{attrs:{label:"备注",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(t.row.remark)+"\n                        ")]}}])}),t("el-table-column",{attrs:{width:"180px",label:"数据上传时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(e._f("formatTime")(t.row.creationTime))+"\n                        ")]}}])}),"itemPayVoucher"==e.$route.name&&"1"!=e.payStatus?t("el-table-column",{attrs:{label:"操作",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.editOrderPay(r.row)}}},[e._v("编辑")]),t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.deleteOrderPay(r.row)}}},[e._v("删除")])]}}],null,!1,2948716754)}):e._e()],1)],1)])])])},t.staticRenderFns=[]},flwu:function(e,t,r){"use strict";r("ltnw")},jX2M:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var a,o=c(r("/umX")),n=c(r("omC7")),i=c(r("XRYr")),s=c(r("7a1e")),l=c(r("cjK8")),d=c(r("Q9c5")),u=c(r("DWNM"));function c(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[u.default],name:"itemPayVoucher",data:function(){return{payStatus:"1",headers:{},rules:{payPrice:[{validator:function(e,t,r){0==/^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/.test(t)?r(new Error("请输入大于零的金额")):r()},trigger:"change"},{validator:function(e,t,r){0==/^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/.test(t)?r(new Error("请输入大于零的金额")):r()},trigger:"blur"},{type:"number",required:!0,message:"总金额不能为空",trigger:"blur"}]},orderPay:{orderId:"",payPrice:0,payTime:"",docIdList:[],remark:"",docInfo:""},orderPaysData:[],title:"上传支付凭证",downloadUrl:d.default.baseContext+"/file/download",dialogVisible:!1,fileList:[],sonRefresh:!0,orderData:{data:{},orderItem:[],hospital:{},orderPays:[]},orderActive:"first",activeName:"0",listLoading:!1,tableHeight:100}},computed:{uploadUrl:function(){return d.default.baseContext+"/file/upload"}},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ")+(t.getHours()<10?"0"+t.getHours()+":":t.getHours()+":")+(t.getMinutes()<10?"0"+t.getMinutes()+":":t.getMinutes()+":")+(t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds())}},watch:{},mounted:function(){var e=i.default.doCloundRequest(d.default.app_key,d.default.app_security,"");this.headers["x-aep-appkey"]=e["x-aep-appkey"],this.headers["x-aep-signature"]=e["x-aep-signature"],this.headers["x-aep-timestamp"]=e["x-aep-timestamp"],this.headers["x-aep-nonce"]=e["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,void 0!=this.$route.query.orderId&&this.getOrderDetail()},components:{itemDetail:l.default,payVoucher:s.default},methods:(a={close:function(){this.dialogVisible=!1},onSuccess:function(e,t,r){if(console.log(e),"1"!=e.state)return this.$message.error(e.message),this.$refs.upload.clearFiles(),!1;t.docId=e.row.id;var a={docId:e.row.id,name:e.row.name};this.orderPay.docIdList.push(a)},onError:function(e,t,r){this.rLoading.close(),console.log(e),this.$message.error("上传失败,请重试")},handlePreview:function(e){var t=this.downloadUrl+"?docId="+encodeURI(encodeURI(e.docId))+"&fileName="+e.name;console.log(e);var r=document.createElement("a");r.href=t,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(t)},beforeUpload:function(e){return!(e.size/1024/1024>10)||(this.$message.error("上传文件过大，请分开上传，单个文件最大为10M。"),!1)},beforeRemove:function(e,t){return!this.beforeUpload(e)||this.$confirm("确定移除 "+e.name+"？")},handleRemove:function(e,t){var r=this;this.orderPay.docIdList.some(function(t,a){if(t.docId==e.docId)return r.orderPay.docIdList.splice(a,1),!0}),console.log(e.docId,e,t)},saveOrderPay:function(){var e=this;if(!this.orderPay.docIdList||0==this.orderPay.docIdList.length)return this.$message.error("请上传支付凭证。"),!1;this.$refs.orderPay.validate(function(t){if(t){e.orderPay.docInfo=(0,n.default)(e.orderPay.docIdList);var r=e.openLoading("提交中...");e.$http_post(d.default.baseContext+"/supervise/supOrderPay/save",e.orderPay,!0).then(function(t){1==t.state?(e.$message.success("提交成功"),e.queryOrderPays(),e.orderPay={id:"",orderId:e.$route.query.orderId,payPrice:0,payTime:"",remark:"",docInfo:"",docIdList:[]},e.fileList=[],e.dialogVisible=!1,r.close()):(e.$message.error(t.message),r.close())})}})},deleteOrderPay:function(e){var t=this;this.$confirm("确定要删除该记录吗").then(function(){var r=t.openLoading("");t.$http_post(d.default.baseContext+"/supervise/supOrderPay/delete/"+e.id,{orderId:e.orderId}).then(function(e){1==e.state?(t.$message.success("删除成功"),t.queryOrderPays(),r.close()):(t.$alert(e.message),r.close())})})},queryOrderPays:function(){var e=this;this.$http_post(d.default.baseContext+"/supervise/supOrderPay/all",{orderId:this.$route.query.orderId}).then(function(t){1==t.state?e.orderData.orderPays=t.row:(e.$alert(t.message),rLoading.close())})},editOrderPay:function(e){var t=this;this.orderPay={id:e.id,orderId:this.$route.query.orderId,payPrice:e.payPrice,payTime:e.payTime,remark:e.remark,docInfo:e.docInfo,docIdList:JSON.parse(e.docInfo)},this.fileList=JSON.parse(e.docInfo),this.title="编辑凭证",this.$nextTick(function(){t.dialogVisible=!0})},downloadFile:function(e,t){var r=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,a=document.createElement("a");a.href=r,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(r)}},(0,o.default)(a,"queryOrderPays",function(){var e=this,t=this.openLoading("");this.$http_post(d.default.baseContext+"/supervise/supOrderPay/all",{orderId:this.$route.query.orderId}).then(function(r){1==r.state?e.orderData.orderPays=r.row:(e.$alert(r.message),t.close())})}),(0,o.default)(a,"handleClick",function(e,t){this.activeName=e.index}),(0,o.default)(a,"uploadVoucher",function(){var e=this;this.activeName="1",this.orderPay={id:"",orderId:this.orderData.data.id,payPrice:this.orderData.data.totalPrice,payTime:"",remark:"",docInfo:"",docIdList:[]},this.title="上传凭证",this.fileList=[],this.$nextTick(function(){e.dialogVisible=!0})}),(0,o.default)(a,"getOrderDetail",function(){var e=this,t=this.openLoading(""),r=d.default.baseContext+"/supervise/supOrder/show/"+this.$route.query.orderId;this.$http_post(r,{}).then(function(r){t.close(),1==r.state&&(e.$set(e,"orderData",r.row),e.payStatus=r.row.data.payStatus,e.orderPay={id:"",orderId:r.row.data.id,payPrice:r.row.data.totalPrice,payTime:"",remark:"",docInfo:"",docIdList:[]})})}),a)}},ltnw:function(e,t,r){},vIW0:function(e,t,r){}}]);