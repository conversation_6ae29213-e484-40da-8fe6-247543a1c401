(window.webpackJsonp=window.webpackJsonp||[]).push([[33,83],{"+eyf":function(e,t,o){},"0JLP":function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"}),t("el-button",{attrs:{type:"info",icon:"el-icon-arrow-left"},on:{click:function(t){return e.$router.push({path:"stockVoucher",query:{country:e.$route.query.country}})}}},[e._v("返回\n        ")])],1),t("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[t("el-tab-pane",{attrs:{label:"订单信息",name:"first"}})],1),t("div",{staticClass:"form"},[t("el-form",{ref:"dataForm",staticClass:"item-form",attrs:{id:"orderData2",model:e.orderData,"label-position":"left","label-width":"110px"}},[t("div",{staticClass:"fromBox"},[t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"订单号"}},[t("el-input",{attrs:{value:e.orderData.data.orderNum}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"医疗机构"}},[t("el-input",{attrs:{value:e.orderData.data.hospitalName}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"采购时间"}},[t("el-input",{attrs:{value:e._f("formatTime")(e.orderData.data.submitTime)}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"提交人"}},[t("el-input",{attrs:{value:e.orderData.data.userName}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"支付状态"}},["0"==e.orderData.data.payStatus?t("el-input",{attrs:{value:"未支付"}}):e._e(),"1"==e.orderData.data.payStatus?t("el-input",{attrs:{value:"已支付"}}):e._e(),"2"==e.orderData.data.payStatus?t("el-input",{attrs:{value:"部分支付"}}):e._e()],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"订单状态"}},["0"==e.orderData.data.status?t("el-input",{attrs:{value:"暂存"}}):e._e(),"1"==e.orderData.data.status?t("el-input",{attrs:{value:"已提交"}}):e._e(),"2"==e.orderData.data.status?t("el-input",{attrs:{value:"已完成"}}):e._e()],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"预警状态"}},[t("el-input",{attrs:{value:e.orderData.data.warning}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"结束时间"}},[t("el-input",{attrs:{value:e._f("formatTime")(e.orderData.data.endTime)}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"合计"}},[t("el-input",{attrs:{value:e.orderData.data.totalPrice+"元"}})],1)],1)],1)],1)])],1),t("div",{staticClass:"flex-row",staticStyle:{"justify-content":"space-between"}},[t("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[t("el-tab-pane",{attrs:{label:"订单项信息",name:"first"}},[t("div",{ref:"tableH",staticClass:"element-item"},[t("div",{staticClass:"box-card box-right",attrs:{shadow:"never"}},[t("div",{staticClass:"text item"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticClass:"data_table",staticStyle:{width:"100%"},attrs:{"header-cell-style":{background:"#f5f7fa"},data:e.orderData.orderItem,"row-key":"id",lazy:"",border:"","highlight-current-row":""}},[e._e(),t("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                        "+e._s(e.getDictItemName(t.row.source))+"\n                                    ")]}}])}),t("el-table-column",{attrs:{prop:"busiCode",label:"药品编码",width:"100"}}),t("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150"}}),t("el-table-column",{attrs:{prop:"country",label:"国家集中集采",width:"115"},scopedSlots:e._u([{key:"default",fn:function(o){return["-1"==o.row.source?t("span",[e._v("是")]):t("span",[e._v("否")])]}}])}),t("el-table-column",{attrs:{prop:"category",label:"类别",width:"80"},scopedSlots:e._u([{key:"default",fn:function(o){return["1"==o.row.category?t("span",[e._v("中药\n                                ")]):e._e(),"2"==o.row.category?t("span",[e._v("西药\n                                ")]):e._e(),"1"!=o.row.category&&"2"!=o.row.category?t("span",[e._v("\n                                    其它\n                                ")]):e._e()]}}])}),t("el-table-column",{attrs:{prop:"specs",label:"规格",width:"80"}}),t("el-table-column",{attrs:{prop:"packingSpecs",label:"包装规格",width:"80"}}),t("el-table-column",{attrs:{prop:"dosageForm",label:"剂型",width:"80"}}),t("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"130"}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"最小单位价",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                        "+e._s(e.getPrice(t.row.packingSpecs,t.row.unitPrice)+"元")+"\n                                    ")]}}])}),t("el-table-column",{attrs:{prop:"unitPrice",label:"商品价",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                        "+e._s(t.row.unitPrice+"元")+"\n                                    ")]}}])}),t("el-table-column",{attrs:{prop:"amount",label:"采购数量",width:"100"}}),t("el-table-column",{attrs:{prop:"itemPrice",label:"合计",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                        "+e._s(t.row.itemPrice+"元")+"\n                                    ")]}}])}),t("el-table-column",{attrs:{prop:"systemContrast",label:"系统推荐",width:"135"},scopedSlots:e._u([{key:"default",fn:function(o){return["1"==o.row.systemContrast?t("span",[e._v("是")]):e._e(),"0"==o.row.systemContrast?t("span",[e._v("否(原因："+e._s(o.row.reason)+")")]):e._e()]}}])}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){}}])})],1)],1)])])])],1)],1),t("el-dialog",{attrs:{title:this.title,visible:e.dialogVisible,width:"60%"},on:{close:e.close}},[t("div",{staticClass:"dialog-content"},[t("el-form",{ref:"orderStock",staticClass:"item-form",attrs:{id:"orderStock",model:e.orderStock,"label-width":"90px"}},[t("div",{staticClass:"fromBox"},[t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"入库数量",prop:"itemStock"}},[t("el-input",{model:{value:e.orderStock.itemStock,callback:function(t){e.$set(e.orderStock,"itemStock",e._n(t))},expression:"orderStock.itemStock"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"入库时间",prop:"stockTime"}},[t("el-date-picker",{attrs:{type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss",size:"small",placeholder:"请选择入库时间"},model:{value:e.orderStock.stockTime,callback:function(t){e.$set(e.orderStock,"stockTime",t)},expression:"orderStock.stockTime"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"凭证上传"}},[t("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{headers:e.headers,action:e.uploadUrl,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"before-upload":e.beforeUpload,"before-remove":e.beforeRemove,"on-success":e.onSuccess,"on-error":e.onError,multiple:"",accept:".gif,.jpg,.jpeg,.bmp,.png,.doc,.docx,.txt,.xlsx,.xls,.pdf","file-list":e.fileList}},[t("el-button",{attrs:{size:"small"}},[e._v("点击上传")])],1)],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"备注",prop:"remark"}},[t("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.orderStock.remark,callback:function(t){e.$set(e.orderStock,"remark",t)},expression:"orderStock.remark"}})],1)],1)],1)],1)])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:e.saveOrderStock}},[e._v("确定")]),t("el-button",{on:{click:e.close}},[e._v("取消")])],1)]),t("stockVoucher",{ref:"asyncDialog",tag:"component",attrs:{orderItemId:e.orderItemId,amount:e.amount,sourceId:e.sourceId},on:{close:e.close}})],1)},t.staticRenderFns=[]},8713:function(e,t,o){"use strict";o("e3HG")},AvpF:function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0});var r=l(o("/umX")),a=l(o("omC7")),s=l(o("XRYr")),i=l(o("Q9c5")),n=l(o("DWNM"));function l(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[n.default],name:"stockVoucher-list",data:function(){return{stockStatus:"",title:"",orderStock:{id:"",orderId:"",stockTime:"",orderItemId:"",itemStock:0,docIdList:[],remark:"",docInfo:"",sourceId:""},headers:{},rules:{itemStock:[{validator:function(e,t,o){0==/(^[1-9]\d*$)/.test(t)?o(new Error("请输入大于零的数")):o()},trigger:"change"},{validator:function(e,t,o){0==/(^[1-9]\d*$)/.test(t)?o(new Error("请输入大于零的数")):o()},trigger:"blur"},{type:"number",required:!0,message:"入库数不能为空",trigger:"blur"}]},show:!1,dialogVisible:!1,downloadUrl:i.default.baseContext+"/file/download",orderStocks:[],params:{page:1,limit:10,records:0,orderItemId:""},fileList:[]}},computed:{uploadUrl:function(){return i.default.baseContext+"/file/upload"}},props:{orderItemId:{type:String,default:""},amount:{type:Number,default:""},sourceId:{type:String,default:""}},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ")+(t.getHours()<10?"0"+t.getHours()+":":t.getHours()+":")+(t.getMinutes()<10?"0"+t.getMinutes()+":":t.getMinutes()+":")+(t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds())}},watch:{orderItemId:function(e){e&&this.queryOrderStocks(),console.log(this.$route.name)}},mounted:function(){var e=s.default.doCloundRequest(i.default.app_key,i.default.app_security,"");this.headers["x-aep-appkey"]=e["x-aep-appkey"],this.headers["x-aep-signature"]=e["x-aep-signature"],this.headers["x-aep-timestamp"]=e["x-aep-timestamp"],this.headers["x-aep-nonce"]=e["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,this.orderItemId&&this.queryOrderStocks()},methods:(0,r.default)({onSuccess:function(e,t,o){if(console.log(e),"1"!=e.state)return this.$message.error(e.message),this.$refs.upload.clearFiles(),!1;t.docId=e.row.id;var r={docId:e.row.id,name:e.row.name};this.orderStock.docIdList.push(r)},onError:function(e,t,o){this.rLoading.close(),console.log(e),this.$message.error("上传失败,请重试")},handlePreview:function(e){var t=this.downloadUrl+"?docId="+encodeURI(encodeURI(e.docId))+"&fileName="+e.name;console.log(e);var o=document.createElement("a");o.href=t,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(t)},beforeUpload:function(e){return!(e.size/1024/1024>10)||(this.$message.error("上传文件过大，请分开上传，单个文件最大为10M。"),!1)},beforeRemove:function(e,t){return!this.beforeUpload(e)||this.$confirm("确定移除 "+e.name+"？")},handleRemove:function(e,t){var o=this;this.orderStock.docIdList.some(function(t,r){if(t.docId==e.docId)return o.orderStock.docIdList.splice(r,1),!0}),console.log(e.docId,e,t)},saveOrderStock:function(){var e=this;if(!this.orderStock.docIdList||0==this.orderStock.docIdList.length)return this.$message.error("请上传入库凭证。"),!1;this.$refs.orderStock.validate(function(t){if(t){e.orderStock.docInfo=(0,a.default)(e.orderStock.docIdList);var o=e.openLoading("提交中...");e.$http_post(i.default.baseContext+"/supervise/supOrderStock/save",e.orderStock,!0).then(function(t){1==t.state?(e.$message.success("提交成功"),e.queryOrderStocks(),e.show=!1,o.close()):(e.$message.error(t.message),o.close())})}})},deleteOrderStock:function(e){var t=this;this.$confirm("确定要删除该记录吗").then(function(){var o=t.openLoading("");t.$http_post(i.default.baseContext+"/supervise/supOrderStock/delete/"+e.id,{orderId:t.$route.query.orderId,sourceId:t.sourceId}).then(function(e){1==e.state?(t.$message.success("删除成功"),t.queryOrderStocks(),o.close()):(t.$alert(e.message),o.close())})})},editOrderStock:function(e){var t=this;console.log(e),this.orderStock={id:e.id,sourceId:this.sourceId,orderId:this.$route.query.orderId,itemStock:e.itemStock,stockTime:e.stockTime,orderItemId:e.orderItemId,remark:e.remark,docInfo:e.docInfo,docIdList:JSON.parse(e.docInfo)},this.fileList=JSON.parse(e.docInfo),this.title="编辑入库凭证",this.$nextTick(function(){t.show=!0})},downloadFile:function(e,t){var o=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,r=document.createElement("a");r.href=o,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(o)},close:function(){this.show=!1},onPageClick:function(e){this.params.page=e,this.queryOrderStocks()},uploadVoucher:function(){var e=this,t=this.$route.query.orderId;this.orderStock={id:"",stockTime:"",sourceId:this.sourceId,orderId:t,itemStock:this.amount,orderItemId:this.orderItemId,remark:"",docInfo:"",docIdList:[]},this.title="上传入库凭证",this.fileList=[],this.$nextTick(function(){e.show=!0})},queryOrderStocks:function(){var e=this;this.params.orderId=this.$route.query.orderId,this.params.orderItemId=this.orderItemId;var t=this.openLoading("");this.$http_post(i.default.baseContext+"/supervise/supOrderStock/all",this.params).then(function(o){t.close(),1==o.state?(e.stockStatus=o.message,e.orderStocks=o.rows,e.params.records=o.records):e.$alert(o.message)})}},"downloadFile",function(e,t){var o=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,r=document.createElement("a");r.href=o,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(o)})}},C8bI:function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0});var r,a=c(o("/umX")),s=c(o("omC7")),i=c(o("tNMj")),n=c(o("Q9c5")),l=c(o("DWNM")),d=c(o("XRYr"));function c(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[l.default],name:"itemStockVoucher",data:function(){return{drugSourceList:[],sourceId:"",amount:1,orderItemId:"",title:"上传入库凭证",downloadUrl:n.default.baseContext+"/file/download",dialogVisible:!1,fileList:[],orderData:{data:{},orderItem:[],hospital:{},orderStocks:[]},orderStock:{id:"",orderId:"",stockTime:"",orderItemId:"",itemStock:0,docIdList:[],remark:"",docInfo:""},headers:{},rules:{itemStock:[{validator:function(e,t,o){0==/(^[1-9]\d*$)/.test(t)?o(new Error("请输入大于零的数")):o()},trigger:"change"},{validator:function(e,t,o){0==/(^[1-9]\d*$)/.test(t)?o(new Error("请输入大于零的数")):o()},trigger:"blur"},{type:"number",required:!0,message:"入库数不能为空",trigger:"blur"}]},activeName:"first",listLoading:!1,tableHeight:100}},components:{stockVoucher:i.default},computed:{uploadUrl:function(){return n.default.baseContext+"/file/upload"}},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ")+(t.getHours()<10?"0"+t.getHours()+":":t.getHours()+":")+(t.getMinutes()<10?"0"+t.getMinutes()+":":t.getMinutes()+":")+(t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds())}},watch:{},mounted:function(){var e=d.default.doCloundRequest(n.default.app_key,n.default.app_security,"");this.headers["x-aep-appkey"]=e["x-aep-appkey"],this.headers["x-aep-signature"]=e["x-aep-signature"],this.headers["x-aep-timestamp"]=e["x-aep-timestamp"],this.headers["x-aep-nonce"]=e["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,void 0!=this.$route.query.orderId&&(this.getDictItem("SOURCE"),this.getOrderDetail())},methods:(r={getPrice:function(e,t){if(e){var o=e.match(/\d+/g);return t?(t/o.join("")).toFixed(2):""}return""},getDictItemName:function(e){for(var t=0;t<this.drugSourceList.length;t++)if(this.drugSourceList[t].value==e)return this.drugSourceList[t].label},getDictItem:function(e){var t=this,o=this.openLoading();this.$http_post(n.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(r){var a=r.rows;1==r.state?("SOURCE"==e&&(t.drugSourceList=a),o.close()):(o.close(),t.$message.error(r.message))})},close:function(){this.$refs.asyncDialog.dialogVisible=!1},editVoucher:function(e){this.amount=e.amount,this.orderItemId=e.id,this.sourceId=e.sourceId,this.$refs.asyncDialog.dialogVisible=!0},uploadVoucher:function(e){var t=this;this.orderStock={id:"",stockTime:"",orderId:this.$route.query.orderId,itemStock:e.amount,orderItemId:e.id,remark:"",docInfo:"",docIdList:[]},this.title="上传入库凭证",this.fileList=[],this.$nextTick(function(){t.dialogVisible=!0})},getOrderDetail:function(){var e=this,t=this.openLoading("查询中"),o=n.default.baseContext+"/supervise/supOrder/show/"+this.$route.query.orderId;this.$http_post(o,{}).then(function(o){1==o.state?(e.orderData=o.row,t.close()):(t.close(),e.$alert(o.message))})}},(0,a.default)(r,"close",function(){this.dialogVisible=!1}),(0,a.default)(r,"onSuccess",function(e,t,o){if("1"!=e.state)return this.$message.error(e.message),this.$refs.upload.clearFiles(),!1;t.docId=e.row.id;var r={docId:e.row.id,name:e.row.name};this.orderStock.docIdList.push(r)}),(0,a.default)(r,"onError",function(e,t,o){this.rLoading.close(),console.log(e),this.$message.error("上传失败,请重试")}),(0,a.default)(r,"handlePreview",function(e){var t=this.downloadUrl+"?docId="+encodeURI(encodeURI(e.docId))+"&fileName="+e.name;console.log(e);var o=document.createElement("a");o.href=t,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(t)}),(0,a.default)(r,"beforeUpload",function(e){return!(e.size/1024/1024>10)||(this.$message.error("上传文件过大，请分开上传，单个文件最大为10M。"),!1)}),(0,a.default)(r,"beforeRemove",function(e,t){return!this.beforeUpload(e)||this.$confirm("确定移除 "+e.name+"？")}),(0,a.default)(r,"handleRemove",function(e,t){var o=this;this.orderStock.docIdList.some(function(t,r){if(t.docId==e.docId)return o.orderStock.docIdList.splice(r,1),!0}),console.log(e.docId,e,t)}),(0,a.default)(r,"saveOrderStock",function(){var e=this;if(!this.orderStock.docIdList||0==this.orderStock.docIdList.length)return this.$message.error("请上传入库凭证。"),!1;this.$refs.orderStock.validate(function(t){if(t){e.orderStock.docInfo=(0,s.default)(e.orderStock.docIdList);var o=e.openLoading("提交中...");e.$http_post(n.default.baseContext+"/supervise/supOrderStock/save",e.orderStock,!0).then(function(t){1==t.state?(e.$message.success("提交成功"),e.dialogVisible=!1,o.close()):(e.$message.error(t.message),o.close())})}})}),(0,a.default)(r,"deleteOrderStock",function(e){var t=this;this.$confirm("确定要删除该记录吗").then(function(){var o=t.openLoading("");t.$http_post(n.default.baseContext+"/supervise/supOrderStock/delete/"+e.id,{}).then(function(e){1==e.state?(t.$message.success("删除成功"),t.queryOrderStocks(),o.close()):(t.$alert(e.message),o.close())})})}),(0,a.default)(r,"editOrderStock",function(e){var t=this;this.orderStock={id:e.id,orderId:this.$route.query.orderId,itemStock:e.itemStock,stockTime:e.stockTime,orderItemId:e.orderItemId,remark:e.remark,docInfo:e.docInfo,docIdList:JSON.parse(e.docInfo)},this.fileList=JSON.parse(e.docInfo),this.title="编辑入库凭证",this.$nextTick(function(){t.dialogVisible=!0})}),(0,a.default)(r,"downloadFile",function(e,t){var o=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,r=document.createElement("a");r.href=o,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(o)}),r)}},KCwm:function(e,t,o){"use strict";var r=o("0JLP");o.o(r,"render")&&o.d(t,"render",function(){return r.render}),o.o(r,"staticRenderFns")&&o.d(t,"staticRenderFns",function(){return r.staticRenderFns})},XyuW:function(e,t,o){"use strict";o.r(t);var r=o("KCwm"),a=o("jAJ4");for(var s in a)["default"].indexOf(s)<0&&function(e){o.d(t,e,function(){return a[e]})}(s);o("ro9p");var i=o("gp09"),n=Object(i.a)(a.default,r.render,r.staticRenderFns,!1,null,"7c5c7b68",null);t.default=n.exports},chxd:function(e,t,o){"use strict";o.r(t);var r=o("AvpF"),a=o.n(r);for(var s in r)["default"].indexOf(s)<0&&function(e){o.d(t,e,function(){return r[e]})}(s);t.default=a.a},cyWD:function(e,t,o){"use strict";var r=o("zY1e");o.o(r,"render")&&o.d(t,"render",function(){return r.render}),o.o(r,"staticRenderFns")&&o.d(t,"staticRenderFns",function(){return r.staticRenderFns})},e3HG:function(e,t,o){},jAJ4:function(e,t,o){"use strict";o.r(t);var r=o("C8bI"),a=o.n(r);for(var s in r)["default"].indexOf(s)<0&&function(e){o.d(t,e,function(){return r[e]})}(s);t.default=a.a},ro9p:function(e,t,o){"use strict";o("+eyf")},tNMj:function(e,t,o){"use strict";o.r(t);var r=o("cyWD"),a=o("chxd");for(var s in a)["default"].indexOf(s)<0&&function(e){o.d(t,e,function(){return a[e]})}(s);o("8713");var i=o("gp09"),n=Object(i.a)(a.default,r.render,r.staticRenderFns,!1,null,"dec4c0de",null);t.default=n.exports},zY1e:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",[t("el-dialog",{attrs:{title:"入库凭证信息",visible:e.dialogVisible,top:"10vh",width:"70%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",{staticClass:"dialog-content"},[t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:!1,expression:"false"}],staticClass:"data_table",staticStyle:{width:"100%"},attrs:{"header-cell-style":{background:"#f5f7fa"},data:e.orderStocks,"highlight-current-row":""}},[t("el-table-column",{attrs:{label:"序号",width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(t.$index+1)+"\n                            ")]}}])}),t("el-table-column",{attrs:{label:"入库数量",align:"center",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(t.row.itemStock)+"\n                            ")]}}])}),t("el-table-column",{attrs:{width:"180px",label:"入库时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(e._f("formatTime")(t.row.stockTime))+"\n                            ")]}}])}),t("el-table-column",{attrs:{width:"300px",label:"入库凭证",align:"center"},scopedSlots:e._u([{key:"default",fn:function(o){return e._l(JSON.parse(o.row.docInfo),function(o,r){return t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.downloadFile(o.docId,o.name)}}},[e._v("\n                                    "+e._s(o.name)+"\n                                ")])})}}])}),t("el-table-column",{attrs:{label:"备注",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(t.row.remark)+"\n                            ")]}}])}),t("el-table-column",{attrs:{width:"180px",label:"数据上传时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(e._f("formatTime")(t.row.creationTime))+"\n                            ")]}}])}),"itemStockVoucher"==e.$route.name?t("el-table-column",{attrs:{label:"操作",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(o){return[t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.editOrderStock(o.row)}}},[e._v("编辑")]),t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.deleteOrderStock(o.row)}}},[e._v("删除")])]}}],null,!1,209884114)}):e._e()],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("关闭")])],1)]),t("el-dialog",{attrs:{title:this.title,visible:e.show,width:"60%"},on:{close:e.close}},[t("div",{staticClass:"dialog-content"},[t("el-form",{ref:"orderStock",staticClass:"item-form",attrs:{id:"orderStock",model:e.orderStock,"label-width":"90px"}},[t("div",{staticClass:"fromBox"},[t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"入库数量",prop:"itemStock"}},[t("el-input",{model:{value:e.orderStock.itemStock,callback:function(t){e.$set(e.orderStock,"itemStock",e._n(t))},expression:"orderStock.itemStock"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"入库时间",prop:"stockTime"}},[t("el-date-picker",{attrs:{type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss",size:"small",placeholder:"请选择入库时间"},model:{value:e.orderStock.stockTime,callback:function(t){e.$set(e.orderStock,"stockTime",t)},expression:"orderStock.stockTime"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"凭证上传"}},[t("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{headers:e.headers,action:e.uploadUrl,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"before-upload":e.beforeUpload,"before-remove":e.beforeRemove,"on-success":e.onSuccess,"on-error":e.onError,multiple:"",accept:".gif,.jpg,.jpeg,.bmp,.png,.doc,.docx,.txt,.xlsx,.xls,.pdf","file-list":e.fileList}},[t("el-button",{attrs:{size:"small"}},[e._v("点击上传")])],1)],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"备注",prop:"remark"}},[t("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.orderStock.remark,callback:function(t){e.$set(e.orderStock,"remark",t)},expression:"orderStock.remark"}})],1)],1)],1)],1)])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.close}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.saveOrderStock}},[e._v("确定")])],1)])],1)},t.staticRenderFns=[]}}]);