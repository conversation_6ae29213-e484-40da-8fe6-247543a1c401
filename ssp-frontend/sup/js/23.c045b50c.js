(window.webpackJsonp=window.webpackJsonp||[]).push([[23,68],{"/8m2":function(e,t,r){"use strict";r.r(t);var a=r("DPpS"),n=r("r5yx");for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,function(){return n[e]})}(i);r("tWmC");var o=r("gp09"),l=Object(o.a)(n.default,a.render,a.staticRenderFns,!1,null,"270a6cc4",null);t.default=l.exports},"/bDl":function(e,t,r){},"0llo":function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var a=o(r("yDCl")),n=o(r("Q9c5")),i=o(r("DWNM"));function o(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[i.default],name:"delivery-detail",data:function(){return{activeName:"first",curTab:"0",dialogVisible:!1,deliveryData:{data:{},deliveryItem:[]},listLoading:!1,tableHeight:100}},components:{deliveryItemList:a.default},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ")+(t.getHours()<10?"0"+t.getHours()+":":t.getHours()+":")+(t.getMinutes()<10?"0"+t.getMinutes()+":":t.getMinutes()+":")+(t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds())}},watch:{},mounted:function(){this.$route.query.deliveryCode&&this.getDeliveryDetail()},methods:{goBack:function(){this.$router.go(-1)},handleClick:function(e,t){this.curTab=e.index},getDeliveryDetail:function(){var e=this,t=this.openLoading("查询中"),r=n.default.baseContext+"/supervise/supDelivery/show/"+this.$route.query.deliveryCode;this.$http_post(r,{}).then(function(r){1==r.state?(e.deliveryData=r.row,t.close()):(t.close(),e.$alert(r.message))})}}}},"5sBe":function(e,t,r){"use strict";var a=r("OcOx");r.o(a,"render")&&r.d(t,"render",function(){return a.render}),r.o(a,"staticRenderFns")&&r.d(t,"staticRenderFns",function(){return a.staticRenderFns})},BUMC:function(e,t,r){},CFHU:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var a=i(r("Q9c5")),n=i(r("DWNM"));function i(e){return e&&e.__esModule?e:{default:e}}t.default={name:"invoiceItem-list",mixins:[n.default],data:function(){return{invoiceItemDataList:[],invoiceParams:{deliveryItemId:"",orderItemId:"",page:1,limit:10,records:0},previewUrl:a.default.baseContext+"/file",downloadUrl:a.default.baseContext+"/file/download",tableHeight:350}},props:{deliveryItemId:{type:String,default:"#"},orderItemId:{type:String,default:"#"}},watch:{deliveryItemId:function(e){e&&this.onInvoiceQuery()},orderItemId:function(e){e&&this.onInvoiceQuery()}},mounted:function(){this.onInvoiceQuery()},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e),r=t.getFullYear()+"-",a=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",n=t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ";t.getHours(),t.getHours(),t.getMinutes(),t.getMinutes(),t.getSeconds(),t.getSeconds();return r+a+n}},methods:{isImageOrPdf:function(e){var t=e.lastIndexOf("."),r=e.substr(t+1);return-1!==["png","jpg","jpeg","bmp","gif","webp","psd","svg","tiff","pdf"].indexOf(r.toLowerCase())},onOnlineView:function(e,t){window.open(this.previewUrl+"/previewByDocId?docId="+encodeURI(encodeURI(e))+"&fileName="+t)},downloadFile:function(e,t){var r=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,a=document.createElement("a");a.href=r,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(r)},onInvoicePageClick:function(e){this.invoiceParams.page=e,this.onInvoiceQuery()},onInvoiceQuery:function(){var e=this;this.invoiceParams.deliveryItemId=this.deliveryItemId,this.invoiceParams.orderItemId=this.orderItemId;var t=this.invoiceParams,r=this.openLoading(),n=a.default.baseContext+"/supervise/supInvoiceItem/getInvoiceItem";this.$http_post(n,t).then(function(t){1==t.state?(e.invoiceItemDataList=t.rows,e.invoiceParams.records=t.records,r.close()):(r.close(),e.$alert(t.message))})}}}},CmiC:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"}),t("el-button",{attrs:{type:"info",icon:"el-icon-arrow-left"},on:{click:e.goBack}},[e._v("返回\n        ")])],1),t("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[t("el-tab-pane",{attrs:{label:"配送单信息",name:"first"}})],1),t("div",{staticClass:"form"},[t("el-form",{ref:"dataForm",staticClass:"item-form",attrs:{id:"deliveryData2",model:e.deliveryData,"label-position":"left","label-width":"110px"}},[t("div",{staticClass:"fromBox"},[t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"配送单号"}},[t("el-input",{attrs:{value:e.deliveryData.data.code}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"医疗机构"}},[t("el-input",{attrs:{value:e.deliveryData.data.hospitalName}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"医院联系人"}},[t("el-input",{attrs:{value:e.deliveryData.data.hospitalPerson}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"医院联系电话"}},[t("el-input",{attrs:{value:e.deliveryData.data.hospitalPhone}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"配送企业"}},[t("el-input",{attrs:{value:e.deliveryData.data.deliveryName}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"配送联系人"}},[t("el-input",{attrs:{value:e.deliveryData.data.deliveryPerson}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"配送时间"}},[t("el-input",{attrs:{value:e._f("formatTime")(e.deliveryData.data.deliveryTime)}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"配送联系电话"}},[t("el-input",{attrs:{value:e.deliveryData.data.deliveryPhone}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"配送地址"}},[t("el-input",{attrs:{value:e.deliveryData.data.deliveryAddress}})],1)],1)],1)],1)])],1),t("div",{staticClass:"memberTab"},[t("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.curTab,callback:function(t){e.curTab=t},expression:"curTab"}},[t("el-tab-pane",{attrs:{label:"配送明细信息"}},[t("delivery-item-list",{ref:"deliveryItem",attrs:{name:"0",deliveryCode:this.$route.query.deliveryCode}})],1)],1)],1)],1)},t.staticRenderFns=[]},DPpS:function(e,t,r){"use strict";var a=r("CmiC");r.o(a,"render")&&r.d(t,"render",function(){return a.render}),r.o(a,"staticRenderFns")&&r.d(t,"staticRenderFns",function(){return a.staticRenderFns})},FYSU:function(e,t,r){},INKj:function(e,t,r){"use strict";r.r(t);var a=r("vVNS"),n=r.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,function(){return a[e]})}(i);t.default=n.a},"LOm/":function(e,t,r){"use strict";var a=r("UtLg");r.o(a,"render")&&r.d(t,"render",function(){return a.render}),r.o(a,"staticRenderFns")&&r.d(t,"staticRenderFns",function(){return a.staticRenderFns})},MaVJ:function(e,t,r){"use strict";r("FYSU")},OcOx:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"}),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.invoiceItemDataList,"highlight-current-row":"",height:e.tableHeight}},[t("el-table-column",{attrs:{prop:"itemNo",label:"发票号",width:"100px"}}),t("el-table-column",{attrs:{prop:"invoiceItemNo",label:"发票明细号",width:"150px"}}),t("el-table-column",{attrs:{prop:"num",label:"数量"}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"单价"}}),t("el-table-column",{attrs:{prop:"taxesAmount",label:"金额"}}),t("el-table-column",{attrs:{prop:"orderCode",label:"订单编号",width:"120px"}}),t("el-table-column",{attrs:{prop:"invoiceDate",label:"开票时间",width:"120px","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s(e._f("formatTime")(r.row.invoiceDate)))])]}}])}),t("el-table-column",{attrs:{prop:"payStatus",label:"回款状态"},scopedSlots:e._u([{key:"default",fn:function(r){return["1"==r.row.payStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已回款")])],1):e._e(),"2"==r.row.payStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分回款")])],1):e._e(),"0"!=r.row.payStatus&&r.row.payStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未回款")])],1)]}}])}),"0"!=e.$route.query.payStatus?t("el-table-column",{attrs:{prop:"docInfo",label:"凭证信息",width:"250px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",{staticClass:"po-column el-button-group"},e._l(JSON.parse(r.row.docInfo||"[]"),function(r,a){return t("el-popover",{staticStyle:{"margin-left":"10px"},attrs:{placement:"left",title:"更多操作",width:e.isImageOrPdf(r.name)?150:80,trigger:"hover"}},[t("el-row",{staticStyle:{"margin-bottom":"5px"}},[e.isImageOrPdf(r.name)?t("el-col",{attrs:{span:12}},[t("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.onOnlineView(r.docId,r.name)}}},[e._v("预览")])],1):e._e(),t("el-col",{attrs:{span:12}},[t("el-button",{attrs:{type:"warning",size:"small"},on:{click:function(t){return e.downloadFile(r.docId,r.name)}}},[e._v("下载")])],1)],1),t("el-button",{attrs:{slot:"reference",type:"text",size:"small"},slot:"reference"},[e._v(" "+e._s(r.name))])],1)}),1)]}}],null,!1,3285525396)}):e._e(),t("el-table-column",{attrs:{prop:"remark",label:"备注","show-overflow-tooltip":""}})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.invoiceParams.records,"page-size":e.invoiceParams.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onInvoicePageClick}})],1)],1)])],1)},t.staticRenderFns=[]},UtLg:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"item-item"},[t("div",{staticClass:"flex-row-default"},[t("div",{staticClass:"box-card box-right"},[t("div",{staticClass:"text item"},[t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{border:"",data:e.deliveryItemDataList,"highlight-current-row":""}},[t("el-table-column",{attrs:{prop:"itemNo",label:"配送明细号",width:"180"}}),t("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"180"}}),t("el-table-column",{attrs:{prop:"approvalNumber",label:"批准文号",width:"150"}}),t("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"220"}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"单价(元)",width:"100"}}),t("el-table-column",{attrs:{prop:"num",label:"发货数量"}}),t("el-table-column",{attrs:{prop:"amount",label:"发货金额"}}),t("el-table-column",{attrs:{prop:"deliveryTime",label:"配送时间"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s(e._f("formatTime")(r.row.deliveryTime)))])]}}])}),t("el-table-column",{attrs:{prop:"warning",label:"预警状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.warning&&"1"!=r.row.warning&&"4"!=r.row.warning?e._e():t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("正常")])],1),r.row.warning&&"1"!=r.row.warning&&"4"!=r.row.warning?t("div",[t("el-popover",{attrs:{placement:"top-start",width:"250",trigger:"hover"}},[e._l(e.getWaring(r.row.warning),function(r,a){return t("div",{key:a,staticClass:"text item"},[e._v("\n                                    "+e._s(a+1+"、"+r.name)+"\n                                  ")])}),t("el-tag",{attrs:{slot:"reference",type:"danger"},slot:"reference"},[e._v("查看预警")])],2)],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"country",label:"是否国家集采",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return["1"==r.row.country?t("div",[t("el-tag",{attrs:{type:"primary"}},[e._v("是")])],1):e._e(),"1"!=r.row.country?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("否")])],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"stockStatus",label:"入库状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.stockStatus&&"0"!=r.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未入库")])],1),"1"==r.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已入库")])],1):e._e(),"2"==r.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分入库")])],1):e._e()]}}])})],1)],1)])],1)])]),t("el-dialog",{attrs:{title:"发票信息",visible:e.dialogVisible,width:"70%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",{staticClass:"dialog-content"},[t("invoice-item",{ref:"deliveryItem",attrs:{name:"0",deliveryItemId:this.deliveryItemId}})],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("关闭")])],1)])],1)},t.staticRenderFns=[]},mxJk:function(e,t,r){"use strict";r("BUMC")},pU08:function(e,t,r){"use strict";r.r(t);var a=r("5sBe"),n=r("w18v");for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,function(){return n[e]})}(i);r("MaVJ");var o=r("gp09"),l=Object(o.a)(n.default,a.render,a.staticRenderFns,!1,null,"9a07a89c",null);t.default=l.exports},r5yx:function(e,t,r){"use strict";r.r(t);var a=r("0llo"),n=r.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,function(){return a[e]})}(i);t.default=n.a},tWmC:function(e,t,r){"use strict";r("/bDl")},vVNS:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var a=o(r("pU08")),n=o(r("DWNM")),i=o(r("Q9c5"));function o(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[n.default],name:"deliveryItemList",data:function(){return{warningData:[],deliveryItemId:"",dialogVisible:!1,deliveryItemDataList:[]}},props:{deliveryCode:{type:String,default:"#"},orderCode:{type:String,default:"#"},orderItemId:{type:String,default:"#"}},components:{invoiceItem:a.default},computed:{goPath:{get:function(){return"#"!=this.orderCode?"/order/orderDetail/invoice/invoiceItemList":"#"!=this.deliveryCode?"/delivery/deliveryDetail/invoice/invoiceItemList":"#"!=this.orderItemId?"/sup/delivery/deliveryItemList":void 0},set:function(e){}}},watch:{orderCode:function(e){e&&this.onDeliveryQuery()},deliveryCode:function(e){e&&this.onDeliveryQuery()},orderItemId:function(e){e&&this.onDeliveryQuery()}},mounted:function(){this.onDeliveryQuery(),this.getDictItem("WARNING")},methods:{getDictItem:function(e){var t=this,r=this.openLoading();this.$http_post(i.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(a){var n=a.rows;1==a.state?("SOURCE"==e&&(t.drugSourceList=n),"WARNING"==e&&(t.warningData=n),r.close()):(r.close(),t.$message.error(a.message))})},getWaringType:function(e){for(var t=0;t<this.warningData.length;t++)if(this.warningData[t].value==e)return this.warningData[t].label},getWaring:function(e){for(var t=[],r=e.split(","),a=0;a<r.length;a++){var n=this.getWaringType(r[a]);n&&t.push({name:n})}return t},showInvoice:function(e){this.deliveryItemId=e,this.dialogVisible=!0},onDeliveryQuery:function(){var e=this,t={orderCode:"#",orderItemId:"#"};"#"!=this.orderCode&&(t={orderCode:this.orderCode}),"#"!=this.orderItemId&&(t={orderItemId:this.orderItemId}),"#"!=this.deliveryCode&&(t={deliveryCode:this.deliveryCode});var r=this.openLoading(),a=i.default.baseContext+"/supervise/supDeliveryItem/getDeliveryItemByCode";this.$http_post(a,t).then(function(t){1==t.state?(e.deliveryItemDataList=t.rows,r.close()):(r.close(),e.$alert(t.message))})}},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e),r=t.getFullYear()+"-",a=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",n=t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ";t.getHours(),t.getHours(),t.getMinutes(),t.getMinutes(),t.getSeconds(),t.getSeconds();return r+a+n}}}},w18v:function(e,t,r){"use strict";r.r(t);var a=r("CFHU"),n=r.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,function(){return a[e]})}(i);t.default=n.a},yDCl:function(e,t,r){"use strict";r.r(t);var a=r("LOm/"),n=r("INKj");for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,function(){return n[e]})}(i);r("mxJk");var o=r("gp09"),l=Object(o.a)(n.default,a.render,a.staticRenderFns,!1,null,"1125e6fb",null);t.default=l.exports}}]);