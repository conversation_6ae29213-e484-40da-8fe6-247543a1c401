(window.webpackJsonp=window.webpackJsonp||[]).push([[21],{"+Mqa":function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=o(a("yDCl")),n=o(a("7a1e")),i=o(a("cjK8")),s=o(a("Q9c5")),l=o(a("DWNM"));a("Enfz");function o(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[l.default],name:"detail_purchase",data:function(){return{pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),a=new Date,r=new Date(a.getTime()-6048e5).format("yyyy-MM-dd");e.$emit("pick",[r,t])}},{text:"最近一个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),a=new Date,r=new Date(a.getTime()-2592e6).format("yyyy-MM-dd");e.$emit("pick",[r,t])}},{text:"最近三个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),a=new Date,r=new Date(a.getTime()-7776e6).format("yyyy-MM-dd");e.$emit("pick",[r,t])}}]},activeName:"first",curTab:"0",dialogVisible:!1,orderData:{data:{},orderItem:[],hospital:{}},listLoading:!1,tableHeight:100,test:"",tableData:[],loading:!1,params:{source:"",hospitalName:"",unitPrice:"",submitTime:[],detailId:"",page:0,limit:15},total:0,hospitalList:[],priceList:[],currentPage4:1}},components:{itemDetail:i.default,payVoucher:n.default,deliveryItemList:r.default},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ")+(t.getHours()<10?"0"+t.getHours()+":":t.getHours()+":")+(t.getMinutes()<10?"0"+t.getMinutes()+":":t.getMinutes()+":")+(t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds())}},watch:{},mounted:function(){var e=this;this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100},this.params.detailId=this.$route.query.drugDetailId,this.getInfoPurchase(),this.getHospitalList()},methods:{goBack:function(){this.$router.go(-1)},handleSizeChange:function(e){this.params.limit=e,this.getInfoPurchase()},onPageClick:function(e){this.params.page=e,this.getInfoPurchase()},getInfoPurchase:function(){var e=this,t=this.params,a=s.default.baseContext+"/supervise/supDrugDetail/info/purchase";this.loading=!0,this.$http_get(a,t).then(function(t){console.log(t),e.tableData=t.row.purchaseDrugDetailVos.rows,e.priceList=t.row.priceList,e.loading=!1,e.total=t.row.purchaseDrugDetailVos.records}).catch(function(t){e.loading=!1})},getHospitalList:function(){var e=this,t=s.default.baseContext+"/supervise/supHospital/all";this.$http_post(t,{}).then(function(t){1==t.state?e.hospitalList=t.row:e.$alert(t.message)})},reeset:function(){this.params.source="",this.params.hospitalName="",this.params.unitPrice="",this.params.submitTime=[],this.getInfoPurchase()}}}},"0boK":function(e,t,a){"use strict";var r=a("SRhA");a.o(r,"render")&&a.d(t,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return r.staticRenderFns})},"5Rlz":function(e,t,a){"use strict";a("vIW0")},"7a1e":function(e,t,a){"use strict";a.r(t);var r=a("AHSE"),n=a("KDzZ");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,function(){return n[e]})}(i);a("5Rlz");var s=a("gp09"),l=Object(s.a)(n.default,r.render,r.staticRenderFns,!1,null,"206521ee",null);t.default=l.exports},AHSE:function(e,t,a){"use strict";var r=a("T0y/");a.o(r,"render")&&a.d(t,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return r.staticRenderFns})},BUMC:function(e,t,a){},INKj:function(e,t,a){"use strict";a.r(t);var r=a("vVNS"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,function(){return r[e]})}(i);t.default=n.a},KDzZ:function(e,t,a){"use strict";a.r(t);var r=a("LcKc"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,function(){return r[e]})}(i);t.default=n.a},"LOm/":function(e,t,a){"use strict";var r=a("UtLg");a.o(r,"render")&&a.d(t,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return r.staticRenderFns})},LcKc:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=i(a("Q9c5")),n=i(a("DWNM"));function i(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[n.default],name:"payVoucher-list",data:function(){return{downloadUrl:r.default.baseContext+"/file/download"}},props:{orderPays:{type:Array,default:function(){}},payStatus:{type:String,default:""}},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ")+(t.getHours()<10?"0"+t.getHours()+":":t.getHours()+":")+(t.getMinutes()<10?"0"+t.getMinutes()+":":t.getMinutes()+":")+(t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds())}},mounted:function(){},methods:{downloadFile:function(e,t){var a=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,r=document.createElement("a");r.href=a,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(a)},deleteOrderPay:function(e){this.$emit("deleteOrderPay",e)},editOrderPay:function(e){this.$emit("editOrderPay",e)}}}},QsH9:function(e,t,a){"use strict";a("e76J")},SRhA:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left"},[t("el-row",{attrs:{gutter:10}},[t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("医疗机构")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:e.params.hospitalName,callback:function(t){e.$set(e.params,"hospitalName",t)},expression:"params.hospitalName"}},e._l(e.hospitalList,function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}),1)],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("采购平台")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",placeholder:"请选择平台"},model:{value:e.params.source,callback:function(t){e.$set(e.params,"source",t)},expression:"params.source"}},[t("el-option",{key:"",attrs:{label:"全部",value:""}}),t("el-option",{key:"1",attrs:{label:"深圳市药品交易平台",value:"1"}}),t("el-option",{key:"2",attrs:{label:"广东省药品交易平台",value:"2"}}),t("el-option",{key:"3",attrs:{label:"广州市药品交易平台",value:"3"}}),t("el-option",{key:"4",attrs:{label:"线下采购",value:"4"}})],1)],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("药品售价")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择药品售价"},model:{value:e.params.unitPrice,callback:function(t){e.$set(e.params,"unitPrice",t)},expression:"params.unitPrice"}},e._l(e.priceList,function(e){return t("el-option",{key:e,attrs:{label:e,value:e}})}),1)],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("采购时间")]),t("div",{staticClass:"searchInput"},[t("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:e.params.submitTime,callback:function(t){e.$set(e.params,"submitTime",t)},expression:"params.submitTime"}})],1)])])],1)],1),t("div",{staticClass:"right"},[t("div",{staticStyle:{"margin-bottom":"5px"}},[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.getInfoPurchase()}}},[e._v("查询")]),t("el-button",{attrs:{size:"small",type:"primary",plain:"",icon:"el-icon-refresh-left"},on:{click:function(t){return e.reeset()}}},[e._v("重置")]),t("el-button",{attrs:{type:"info"},on:{click:e.goBack}},[e._v("返回\n                ")])],1)])]),t("div",{staticClass:"memberTab"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,"cell-style":{"text-align":"center"},"header-cell-style":{"text-align":"center"},border:"",height:e.tableHeight}},[t("el-table-column",{attrs:{fixed:"",prop:"detailId",label:"药品编号"}}),t("el-table-column",{attrs:{prop:"orderNum",label:"订单编码"}}),t("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单编码"}}),t("el-table-column",{attrs:{prop:"catalogName",label:"药品名称"}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"药品售价"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{attrs:{type:"info"}},[e._v(e._s(a.row.unitPrice))])]}}])}),t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构"}}),t("el-table-column",{attrs:{prop:"source",label:"采购平台"},scopedSlots:e._u([{key:"default",fn:function(a){return[1==a.row.source?t("el-tag",{attrs:{type:"success"}},[e._v("深圳市药品电子交易平台")]):e._e(),2==a.row.source?t("el-tag",{attrs:{type:"warning"}},[e._v("广东省药品电子交易平台")]):e._e(),3==a.row.source?t("el-tag",[e._v("广州市药品电子交易平台")]):e._e(),4==a.row.source?t("el-tag",{attrs:{type:"info"}},[e._v("线下采购")]):e._e()]}}])}),t("el-table-column",{attrs:{prop:"deliveryName",label:"配送企业"}}),t("el-table-column",{attrs:{prop:"submitTime",label:"采购时间"}})],1)],1),t("div",{staticClass:"block"},[t("el-pagination",{staticStyle:{float:"right","margin-top":"15px"},attrs:{background:"","page-sizes":[15,30,50,100],"page-size":15,layout:"total, sizes, prev, pager, next, jumper",total:this.total},on:{"size-change":e.handleSizeChange,"current-change":e.onPageClick}})],1)])},t.staticRenderFns=[]},"T0y/":function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"item-item"},[t("div",{staticClass:"flex-row-default"},[t("div",{staticClass:"box-card box-right"},[t("div",{staticClass:"text item"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:!1,expression:"false"}],staticClass:"data_table",staticStyle:{width:"100%"},attrs:{"header-cell-style":{background:"#f5f7fa"},data:e.orderPays,border:"","highlight-current-row":""}},[t("el-table-column",{attrs:{label:"序号",width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(t.$index+1)+"\n                        ")]}}])}),t("el-table-column",{attrs:{label:"支付金额(元)",align:"center",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(t.row.payPrice)+"\n                        ")]}}])}),t("el-table-column",{attrs:{width:"180px",label:"支付时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(e._f("formatTime")(t.row.payTime))+"\n                        ")]}}])}),t("el-table-column",{attrs:{width:"300px",label:"支付凭证",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return e._l(JSON.parse(a.row.docInfo),function(a,r){return t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.downloadFile(a.docId,a.name)}}},[e._v("\n                                "+e._s(a.name)+"\n                            ")])})}}])}),t("el-table-column",{attrs:{label:"备注",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(t.row.remark)+"\n                        ")]}}])}),t("el-table-column",{attrs:{width:"180px",label:"数据上传时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(e._f("formatTime")(t.row.creationTime))+"\n                        ")]}}])}),"itemPayVoucher"==e.$route.name&&"1"!=e.payStatus?t("el-table-column",{attrs:{label:"操作",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.editOrderPay(a.row)}}},[e._v("编辑")]),t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.deleteOrderPay(a.row)}}},[e._v("删除")])]}}],null,!1,2948716754)}):e._e()],1)],1)])])])},t.staticRenderFns=[]},UtLg:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"item-item"},[t("div",{staticClass:"flex-row-default"},[t("div",{staticClass:"box-card box-right"},[t("div",{staticClass:"text item"},[t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{border:"",data:e.deliveryItemDataList,"highlight-current-row":""}},[t("el-table-column",{attrs:{prop:"itemNo",label:"配送明细号",width:"180"}}),t("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"180"}}),t("el-table-column",{attrs:{prop:"approvalNumber",label:"批准文号",width:"150"}}),t("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"220"}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"单价(元)",width:"100"}}),t("el-table-column",{attrs:{prop:"num",label:"发货数量"}}),t("el-table-column",{attrs:{prop:"amount",label:"发货金额"}}),t("el-table-column",{attrs:{prop:"deliveryTime",label:"配送时间"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e._f("formatTime")(a.row.deliveryTime)))])]}}])}),t("el-table-column",{attrs:{prop:"warning",label:"预警状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.warning&&"1"!=a.row.warning&&"4"!=a.row.warning?e._e():t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("正常")])],1),a.row.warning&&"1"!=a.row.warning&&"4"!=a.row.warning?t("div",[t("el-popover",{attrs:{placement:"top-start",width:"250",trigger:"hover"}},[e._l(e.getWaring(a.row.warning),function(a,r){return t("div",{key:r,staticClass:"text item"},[e._v("\n                                    "+e._s(r+1+"、"+a.name)+"\n                                  ")])}),t("el-tag",{attrs:{slot:"reference",type:"danger"},slot:"reference"},[e._v("查看预警")])],2)],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"country",label:"是否国家集采",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return["1"==a.row.country?t("div",[t("el-tag",{attrs:{type:"primary"}},[e._v("是")])],1):e._e(),"1"!=a.row.country?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("否")])],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"stockStatus",label:"入库状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.stockStatus&&"0"!=a.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未入库")])],1),"1"==a.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已入库")])],1):e._e(),"2"==a.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分入库")])],1):e._e()]}}])})],1)],1)])],1)])]),t("el-dialog",{attrs:{title:"发票信息",visible:e.dialogVisible,width:"70%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",{staticClass:"dialog-content"},[t("invoice-item",{ref:"deliveryItem",attrs:{name:"0",deliveryItemId:this.deliveryItemId}})],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("关闭")])],1)])],1)},t.staticRenderFns=[]},aRSu:function(e,t,a){"use strict";a.r(t);var r=a("0boK"),n=a("y8wp");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,function(){return n[e]})}(i);a("QsH9");var s=a("gp09"),l=Object(s.a)(n.default,r.render,r.staticRenderFns,!1,null,"9cc2627a",null);t.default=l.exports},e76J:function(e,t,a){},mxJk:function(e,t,a){"use strict";a("BUMC")},vIW0:function(e,t,a){},vVNS:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=s(a("pU08")),n=s(a("DWNM")),i=s(a("Q9c5"));function s(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[n.default],name:"deliveryItemList",data:function(){return{warningData:[],deliveryItemId:"",dialogVisible:!1,deliveryItemDataList:[]}},props:{deliveryCode:{type:String,default:"#"},orderCode:{type:String,default:"#"},orderItemId:{type:String,default:"#"}},components:{invoiceItem:r.default},computed:{goPath:{get:function(){return"#"!=this.orderCode?"/order/orderDetail/invoice/invoiceItemList":"#"!=this.deliveryCode?"/delivery/deliveryDetail/invoice/invoiceItemList":"#"!=this.orderItemId?"/sup/delivery/deliveryItemList":void 0},set:function(e){}}},watch:{orderCode:function(e){e&&this.onDeliveryQuery()},deliveryCode:function(e){e&&this.onDeliveryQuery()},orderItemId:function(e){e&&this.onDeliveryQuery()}},mounted:function(){this.onDeliveryQuery(),this.getDictItem("WARNING")},methods:{getDictItem:function(e){var t=this,a=this.openLoading();this.$http_post(i.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(r){var n=r.rows;1==r.state?("SOURCE"==e&&(t.drugSourceList=n),"WARNING"==e&&(t.warningData=n),a.close()):(a.close(),t.$message.error(r.message))})},getWaringType:function(e){for(var t=0;t<this.warningData.length;t++)if(this.warningData[t].value==e)return this.warningData[t].label},getWaring:function(e){for(var t=[],a=e.split(","),r=0;r<a.length;r++){var n=this.getWaringType(a[r]);n&&t.push({name:n})}return t},showInvoice:function(e){this.deliveryItemId=e,this.dialogVisible=!0},onDeliveryQuery:function(){var e=this,t={orderCode:"#",orderItemId:"#"};"#"!=this.orderCode&&(t={orderCode:this.orderCode}),"#"!=this.orderItemId&&(t={orderItemId:this.orderItemId}),"#"!=this.deliveryCode&&(t={deliveryCode:this.deliveryCode});var a=this.openLoading(),r=i.default.baseContext+"/supervise/supDeliveryItem/getDeliveryItemByCode";this.$http_post(r,t).then(function(t){1==t.state?(e.deliveryItemDataList=t.rows,a.close()):(a.close(),e.$alert(t.message))})}},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e),a=t.getFullYear()+"-",r=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",n=t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ";t.getHours(),t.getHours(),t.getMinutes(),t.getMinutes(),t.getSeconds(),t.getSeconds();return a+r+n}}}},y8wp:function(e,t,a){"use strict";a.r(t);var r=a("+Mqa"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,function(){return r[e]})}(i);t.default=n.a},yDCl:function(e,t,a){"use strict";a.r(t);var r=a("LOm/"),n=a("INKj");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,function(){return n[e]})}(i);a("mxJk");var s=a("gp09"),l=Object(s.a)(n.default,r.render,r.staticRenderFns,!1,null,"1125e6fb",null);t.default=l.exports}}]);