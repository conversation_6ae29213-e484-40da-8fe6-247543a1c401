(window.webpackJsonp=window.webpackJsonp||[]).push([[51],{"+KlW":function(e,t,a){"use strict";a.r(t);var s=a("SmA7"),l=a("NbCJ");for(var i in l)["default"].indexOf(i)<0&&function(e){a.d(t,e,function(){return l[e]})}(i);a("CS5Y");var r=a("gp09"),n=Object(r.a)(l.default,s.render,s.staticRenderFns,!1,null,"4dd7fa07",null);t.default=n.exports},CS5Y:function(e,t,a){"use strict";a("btXy")},NbCJ:function(e,t,a){"use strict";a.r(t);var s=a("xPbG"),l=a.n(s);for(var i in s)["default"].indexOf(i)<0&&function(e){a.d(t,e,function(){return s[e]})}(i);t.default=l.a},PgMq:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left"},[t("el-row",{attrs:{gutter:10}},[t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("发票号")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入发票号"},model:{value:e.params.code,callback:function(t){e.$set(e.params,"code",t)},expression:"params.code"}})],1)])]),this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("医疗机构")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:e.params.hospitalName,callback:function(t){e.$set(e.params,"hospitalName",t)},expression:"params.hospitalName"}},e._l(e.hospitalList,function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}),1)],1)])]):e._e(),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("配送单号")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入配送单号"},model:{value:e.params.deliveryCode,callback:function(t){e.$set(e.params,"deliveryCode",t)},expression:"params.deliveryCode"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("配送企业")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入配送企业名称"},model:{value:e.params.deliveryName,callback:function(t){e.$set(e.params,"deliveryName",t)},expression:"params.deliveryName"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("开票时间")]),t("div",{staticClass:"searchInput"},[t("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:e.params.invoiceTime,callback:function(t){e.$set(e.params,"invoiceTime",t)},expression:"params.invoiceTime"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("是否集采")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择集采状态"},model:{value:e.params.country,callback:function(t){e.$set(e.params,"country",t)},expression:"params.country"}},[t("el-option",{attrs:{label:"是",value:"1"}}),t("el-option",{attrs:{label:"否",value:"0"}})],1)],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("回款状态")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择回款状态"},model:{value:e.params.payStatus,callback:function(t){e.$set(e.params,"payStatus",t)},expression:"params.payStatus"}},[t("el-option",{attrs:{label:"未回款",value:"0"}}),t("el-option",{attrs:{label:"已回款",value:"1"}})],1)],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("回款方式")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择回款方式"},model:{value:e.params.payType,callback:function(t){e.$set(e.params,"payType",t)},expression:"params.payType"}},[t("el-option",{attrs:{label:"自动回款",value:"0"}}),t("el-option",{attrs:{label:"平台确认",value:"1"}})],1)],1)])])],1)],1),t("div",{staticClass:"right"},[t("div",[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1)])]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.dataList,"highlight-current-row":"",height:e.tableHeight,border:""}},[t("el-table-column",{attrs:{prop:"code",label:"发票代码"}}),t("el-table-column",{attrs:{prop:"itemNo",label:"发票号"}}),t("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单号"}}),t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构"}}),t("el-table-column",{attrs:{prop:"deliveryName",label:"配送企业"}}),t("el-table-column",{attrs:{prop:"status",label:"回款状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return["1"==a.row.payStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已回款")])],1):e._e(),"2"==a.row.payStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分回款")])],1):e._e(),"0"!=a.row.payStatus&&a.row.payStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未回款")])],1)]}}])}),t("el-table-column",{attrs:{prop:"invoiceDate",label:"开票时间"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e._f("formatTime")(a.row.invoiceDate)))])]}}])}),t("el-table-column",{attrs:{prop:"invoiceRemark",label:"备注"}}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"200"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showDetail(a.row)}}},[e._v("查看详情")])]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)])],1)},t.staticRenderFns=[]},SmA7:function(e,t,a){"use strict";var s=a("PgMq");a.o(s,"render")&&a.d(t,"render",function(){return s.render}),a.o(s,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return s.staticRenderFns})},btXy:function(e,t,a){},xPbG:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var s=i(a("Q9c5")),l=i(a("DWNM"));i(a("XRYr"));function i(e){return e&&e.__esModule?e:{default:e}}t.default={name:"invoice-list",mixins:[l.default],data:function(){return{adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1,rLoading:{},tableHeight:100,dataList:[],hospitalList:[],params:{page:1,limit:10,records:0,deliveryName:"",code:"",deliveryCode:"",hospitalName:"",invoiceTime:"",country:"",status:"",payType:""}}},props:{},computed:{uploadUrl:function(){return s.default.baseContext+"/file/upload"}},watch:{},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e),a=t.getFullYear()+"-",s=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",l=t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ";t.getHours(),t.getHours(),t.getMinutes(),t.getMinutes(),t.getSeconds(),t.getSeconds();return a+s+l}},methods:{initRole:function(){var e=this.$store.getters.curUser.roleCode;-1!=e.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=e.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=e.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=e.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)},showDetail:function(e){this.$router.push({name:"conMaterialInvoiceDetail",query:{invoiceCode:e.itemNo}})},onSearch:function(e){"reset"==e?(this.params.code="",this.params.hospitalName="",this.params.deliveryName="",this.params.deliveryCode="",this.params.invoiceTime="",this.params.page=1,this.onQuery()):(this.params.page=1,this.onQuery())},onPageClick:function(e){this.params.page=e,this.onQuery()},getHospitalList:function(){var e=this,t=s.default.baseContext+"/supervise/supHospital/all";this.$http_post(t,{}).then(function(t){1==t.state?e.hospitalList=t.row:e.$alert(t.message)})},onQuery:function(){var e=this,t=this.params,a=this.openLoading(),l=s.default.baseContext+"/supervise/conMaterialInvoiceItem/getConMaterialInvoiceItemList";this.$http_post(l,t).then(function(t){1==t.state?(e.dataList=t.rows,e.params.records=t.records,a.close()):(a.close(),e.$alert(t.message))})}},mounted:function(){var e=this;this.initRole(),this.onQuery(),this.getHospitalList(),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-150}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-150}},beforeDestroy:function(){window.onresize=null}}}}]);