<template>
	<div class="pageBar flex flex-v flex-align-center" v-if="pageShow">
		<i :class="upClass" @click="pageUp('up')"></i>
		<p><span>{{nowPage}}</span>/{{totalPage}}</p>
		<i :class="downClass" @click="pageDown('down')"></i>
	</div>
</template>

<script>
	export default {
	  data(){
      return {
        nowPage: 1
      }
    },
		props:{
      curPage:{
        type: Number,
        default: 1
      },
			totalPage:{
				type: Number,
				default: 1
			},
      mustShow:{
				type: Boolean,
				default: false
			},
		},
    watch:{
      curPage(newV,oldV){
        this.nowPage = newV;
      }
    },
    computed:{
      pageShow(){
        const totalPage = this.totalPage;
        if(totalPage <= 1){
          if(this.mustShow) {
            return true;
          } else {
            return false;
          }
        }else{
          return true;
        }
      },
		  upClass(){
		    const nowPage = this.nowPage;
		    if(nowPage == 1){
		      return ['iconfont','icon-shangjiantou'];
        }else{
          return ['iconfont','icon-shangjiantou','colorGreen'];
        }
      },
      downClass(){
        const nowPage = this.nowPage;
        const totalPage = this.totalPage
        if(nowPage == totalPage){
          return ['iconfont','icon-xiajiantou'];
        }else{
          return ['iconfont','icon-xiajiantou','colorGreen'];
        }
      }
    },
		methods:{
	    init(){
	      this.nowPage = 1;
      },
      pageUp(){
        var nowPage = this.nowPage;
        if(nowPage > 1){
          this.nowPage = nowPage - 1;
          this.$emit("up",this.nowPage);
          this.$emit("pageUp",this.nowPage);
        }
      },
      pageDown(){
        const nowPage = this.nowPage;
        const totalPage = this.totalPage;
        if(nowPage < totalPage){
          this.nowPage = nowPage + 1;
          this.$emit("down",this.nowPage);
          this.$emit("pageDown",this.nowPage);
        }
      }
		}
	}
</script>

<style scoped>
.pageBar i{
	display:inline-block;
	width: .88rem;
	height: .88rem;
	line-height: .88rem;
	/*background: rgba(102,102,102,0.24);*/
  background: #e0e0e0;
	border-radius:50%;
	font-size:.8rem;
	text-align: center;
	color:#FFF;
}
.pageBar p{
	text-align: center;
	font-size:.3rem;
	line-height:.76rem;
	color:#333333;
}
.pageBar span{
	font-size:.3rem;
	color:#2582EC;
}
.pageBar .colorGreen{
  background-color:#59a9ff !important;
 }
</style>
