(window.webpackJsonp=window.webpackJsonp||[]).push([[18],{"+M8W":function(t,e,a){"use strict";a.r(e);var n=a("O+Jk"),i=a("18UQ");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return i[t]})}(r);a("iUbp");var o=a("gp09"),s=Object(o.a)(i.default,n.render,n.staticRenderFns,!1,null,"5f3dc947",null);e.default=s.exports},"18UQ":function(t,e,a){"use strict";a.r(e);var n=a("jOMy"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return n[t]})}(r);e.default=i.a},"70IS":function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var n=r(a("DWNM")),i=r(a("Q9c5"));function r(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[n.default],name:"material_list",data:function(){return{loading:!1,itemParams:{hospitalId:"",type:1,platform:"",drugType:"",attribute:"",source:"",submitTime:[new Date((new Date).getTime()-2592e6).format("yyyy-MM-dd"),(new Date).format("yyyy-MM-dd")],hospitalName:"",currentPage:1,pageSize:10},pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-6048e5),t.$emit("pick",[a,e])}},{text:"最近一个月",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-2592e6),t.$emit("pick",[a,e])}},{text:"最近三个月",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-7776e6),t.$emit("pick",[a,e])}}]},tableHeight:500,dialogVisible:!1,currentHospital:"",detailData:[],hospitals:[{id:1,name:"鄂山西工医院（普通合伙）"},{id:2,name:"江门市中心医院"},{id:3,name:"江门市五邑中医院"},{id:4,name:"江门市人民医院"},{id:5,name:"江门市妇幼保健院"},{id:6,name:"江门市第二人民医院"},{id:7,name:"江门市第三人民医院"},{id:8,name:"江门市结核病防治所"},{id:9,name:"江门市蓬江区杜阮卫生院"},{id:10,name:"江门市江海区人民医院"}],platforms:[{id:1,name:"深圳市药品交易平台"},{id:2,name:"广东省药品交易平台"},{id:3,name:"广州市药品交易平台"}],pagination:{currentPage:1,pageSize:10,total:0,pageSizes:[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper"},allTableData:[],tableData:[],areaRegoniName:"",regionList:[{code:"",name:"全部"},{code:"440703",name:"蓬江区"},{code:"440704",name:"江海区"},{code:"440705",name:"新会区"},{code:"440781",name:"台山市"},{code:"440783",name:"开平市"},{code:"440784",name:"鹤山市"},{code:"440785",name:"恩平市"}]}},watch:{tableData:{handler:function(){var t=this;this.$nextTick(function(){t.calcTableHeight()})},deep:!0},"pagination.currentPage":function(){var t=this;this.$nextTick(function(){t.calcTableHeight()})},"pagination.pageSize":function(){var t=this;this.$nextTick(function(){t.calcTableHeight()})}},mounted:function(){var t=this;this.$nextTick(function(){t.calcTableHeight(),window.addEventListener("resize",t.calcTableHeight),t.heightCheckInterval=setInterval(function(){t.calcTableHeight()},1e3),setTimeout(function(){t.heightCheckInterval&&(clearInterval(t.heightCheckInterval),t.heightCheckInterval=null)},5e3)}),this.getHospitalList(),this.initTableData()},updated:function(){var t=this;this.$nextTick(function(){t.calcTableHeight()})},beforeDestroy:function(){window.removeEventListener("resize",this.calcTableHeight),this.heightCheckInterval&&(clearInterval(this.heightCheckInterval),this.heightCheckInterval=null)},methods:{changeRegionName:function(t){for(var e=0;e<this.regionList.length;e++)if(t==this.regionList[e].code){this.areaRegoniName="全部"==this.regionList[e].name?"全市合计":this.regionList[e].name;break}},getHospitalList:function(){var t=this,e=i.default.baseContext+"/supervise/supHospital/all";this.$http_post(e,{}).then(function(e){1==e.state?t.hospitals=e.row:t.$alert(e.message)})},initTableData:function(){var t=this;this.loading=!0;var e=i.default.baseContext+"/supervise/statistics/getMaterialSourceAmountByHospital";this.$http_post(e,this.itemParams).then(function(e){1==e.state?null!=e.rows&&(t.loading=!1,t.pagination.total=e.records,t.tableData=e.rows,t.$nextTick(function(){t.calcTableHeight()})):(t.loading=!1,null!=e.message?t.$message.error(e.message):t.$message.error("系统异常"))}).catch(function(e){console.error("获取数据失败:",e),t.$message.error("获取数据失败"),t.loading=!1})},exportList:function(){var t=this;this.$confirm("确定导出耗材配送统计数据吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.$store.getters.curUser.id||t.$message.error("用户未登录"),t.loading=!0;var e=i.default.baseContext+"/supervise/statistics/exportMaterialSourceAmountByHospital";t.$post_blob(e,t.itemParams).then(function(e){if("application/json"==e.type){var a=new FileReader;return a.addEventListener("loadend",function(e){t.$message.error(JSON.parse(e.target.result).message)}),void a.readAsText(e)}var n=URL.createObjectURL(e),i=document.createElement("a");i.href=n,i.target="_blank";var r=t.itemParams.submitTime[0]+"-"+t.itemParams.submitTime[1];i.download="耗材配送统计"+r+".xls",i.click(),t.loading=!1}).catch(function(t){console.log(t)})}).catch(function(t){console.log(t)})},handleCurrentChange:function(t){this.itemParams.currentPage=t,this.pagination.currentPage=t,this.initTableData()},handleSizeChange:function(t){this.pagination.pageSize=t,this.itemParams.pageSize=t,this.initTableData()},calcTableHeight:function(){try{if(!this.$refs.tableH)return;var t=this.$refs.tableH.clientHeight,e=this.$refs.tableH.querySelector(".search-header"),a=e?e.offsetHeight:0,n=this.$refs.tableH.querySelector(".table-title"),i=n?n.offsetHeight:0,r=this.$refs.tableH.querySelector(".pagination-container"),o=t-(a+i+(r?r.offsetHeight:70)+60),s=Math.max(o,300);this.tableHeight!==s&&(this.tableHeight=s)}catch(t){console.error("计算表格高度时出错:",t),this.tableHeight=500}},showDetails:function(t){this.currentHospital=t.hospitalName,this.detailData=t.materialSourceAmountVoList,this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1},formatAmount:function(t){return null==t?0:t.toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2})},getSubTableData:function(t){var e=this.tableData.find(function(e){return e.name===t});if(!e)return[];var a=Math.round(.75*e.drugCount),n=Math.round(.2*e.drugCount),i=e.drugCount-a-n,r=Math.round(.75*e.totalAmount*100)/100,o=Math.round(.2*e.totalAmount*100)/100,s=Math.round(100*(e.totalAmount-r-o))/100;return[{type:"国家集采",drugCount:a,totalCount:Math.round(.75*e.totalCount),totalUnitCount:Math.round(.75*e.totalUnitCount),totalAmount:r,rawPercentage:"75.00%",percentage:75},{type:"非国家集采",drugCount:n,totalCount:Math.round(.2*e.totalCount),totalUnitCount:Math.round(.2*e.totalUnitCount),totalAmount:o,rawPercentage:"20.00%",percentage:20},{type:"线下采购",drugCount:i,totalCount:Math.round(.05*e.totalCount),totalUnitCount:Math.round(.05*e.totalUnitCount),totalAmount:s,rawPercentage:"5.00%",percentage:5}]},formatPercentage:function(t){var e=100,a="100%";return t.rawPercentage?(e=parseFloat(t.rawPercentage.replace("%","")),a=t.rawPercentage):t.percentage&&(a=(e=parseFloat(t.percentage))+"%"),{percentage:e,rawPercentage:a}},getTagType:function(t){switch(t){case"国家集采":return"success";case"非国家集采":return"info";case"线下采购":return"warning";default:return""}},getProgressColor:function(t,e){if(void 0!==e)return e<30?"#F56C6C":e<60?"#E6A23C":e<80?"#909399":"#67C23A";if(!t)return"#409EFF";switch(t){case"国家集采":return"#67C23A";case"非国家集采":return"#909399";case"线下采购":return"#E6A23C";default:return"#409EFF"}},handleReset:function(){this.itemParams={hospitalId:"",submitTime:[new Date((new Date).getTime()-2592e6).format("yyyy-MM-dd"),(new Date).format("yyyy-MM-dd")],platform:"",drugType:"",regionId:"",pageSize:10,currentPage:1,type:1},this.pagination.currentPage=1,this.initTableData()}}}},"7XtU":function(t,e,a){"use strict";a.r(e);var n=a("jBh1"),i=a("cUyc");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return i[t]})}(r);a("lK0N");var o=a("gp09"),s=Object(o.a)(i.default,n.render,n.staticRenderFns,!1,null,"4799994e",null);e.default=s.exports},E2S0:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"tabs-container"},[e("el-tabs",{attrs:{type:"card"},on:{"tab-click":t.handleTabChange},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[e("el-tab-pane",{attrs:{label:"采购详情",name:"purchase"}},[e("material-list-component",{staticClass:"component-container",attrs:{hospitalId:t.params.hospitalId,submitTime:t.params.submitTime,platform:t.params.platform,regionId:t.itemParams.regionId}})],1),e("el-tab-pane",{attrs:{label:"配送详情",name:"delivery"}},[e("delivery-list-component",{staticClass:"component-container",attrs:{hospitalId:t.params.hospitalId,submitTime:t.params.submitTime,platform:t.params.platform,regionId:t.itemParams.regionId}})],1)],1)],1)])},e.staticRenderFns=[]},"E5+4":function(t,e,a){"use strict";a.r(e);var n=a("70IS"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return n[t]})}(r);e.default=i.a},Humh:function(t,e,a){"use strict";var n=a("Ml1E");a.o(n,"render")&&a.d(e,"render",function(){return n.render}),a.o(n,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return n.staticRenderFns})},JWP4:function(t,e,a){},Ml1E:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left"},[e("el-row",{attrs:{gutter:10}},[e("el-col",{attrs:{span:6,sm:12,md:8,lg:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("医疗机构")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:t.itemParams.hospitalId,callback:function(e){t.$set(t.itemParams,"hospitalId",e)},expression:"itemParams.hospitalId"}},t._l(t.hospitals,function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}),1)],1)])]),e("el-col",{attrs:{span:6,sm:12,md:8,lg:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("配送时间")]),e("div",{staticClass:"searchInput"},[e("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":t.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:t.itemParams.submitTime,callback:function(e){t.$set(t.itemParams,"submitTime",e)},expression:"itemParams.submitTime"}})],1)])]),e("el-col",{attrs:{span:6,sm:12,md:8,lg:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("区划")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择区划"},on:{change:t.changeRegionName},model:{value:t.itemParams.regionId,callback:function(e){t.$set(t.itemParams,"regionId",e)},expression:"itemParams.regionId"}},t._l(t.regionList,function(t){return e("el-option",{key:t.code,attrs:{label:t.name,value:t.code}})}),1)],1)])])],1)],1),e("div",{staticClass:"right"},[e("div",{staticClass:"button-group"},[e("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:function(e){return t.initTableData()}}},[t._v("查询")]),e("el-button",{attrs:{size:"small",type:"success",icon:"el-icon-search"},on:{click:function(e){return t.exportList()}}},[t._v("导出")]),e("el-button",{attrs:{size:"small",type:"primary",plain:"",icon:"el-icon-refresh-left"},on:{click:t.handleReset}},[t._v("重置")])],1)])]),e("div",{staticClass:"hospital-table"},[e("div",{staticClass:"table-title"},[t._v("耗材配送统计列表")]),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.tableData,border:"",height:t.tableHeight}},[e("el-table-column",{attrs:{prop:"regionName",label:"区划",align:"center","min-width":"180"}}),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",align:"center","min-width":"200"}}),e("el-table-column",{attrs:{prop:"totalPackingAmount",sortable:"",label:"配送最小单位总数量",align:"center","min-width":"200"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t.formatAmount(a.row.totalPackingAmount)))])]}}])}),e("el-table-column",{attrs:{prop:"totalPrice",sortable:"",label:"配送总金额(元)",align:"center","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t.formatAmount(a.row.totalPrice)))])]}}])}),e("el-table-column",{attrs:{label:"操作",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.showDetails(a.row)}}},[t._v("详情")])]}}])})],1),e("div",{staticClass:"pagination-container"},[e("el-pagination",{attrs:{background:"","current-page":t.pagination.currentPage,"page-sizes":t.pagination.pageSizes,"page-size":t.pagination.pageSize,layout:t.pagination.layout,total:t.pagination.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),e("el-dialog",{attrs:{title:t.currentHospital+" - 配送统计详情",visible:t.dialogVisible,width:"80%","before-close":t.handleClose},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("div",{staticClass:"dialog-content"},[e("div",{staticClass:"dialog-header"},[e("div",{staticClass:"left"},[e("i",{staticClass:"el-icon-data-analysis"}),e("span",[t._v("配送分类统计")])])]),e("el-table",{attrs:{data:t.detailData,border:"","cell-style":{"text-align":"center"},"header-cell-style":{"text-align":"center","background-color":"#f5f7fa"},stripe:"","highlight-current-row":""}},[e("el-table-column",{attrs:{prop:"type",label:"配送类型",align:"center","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:t.getTagType(a.row.type)}},[t._v(t._s(a.row.type))])]}}])}),e("el-table-column",{attrs:{prop:"packingAmount",label:"配送最小单位总数量",align:"center","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticClass:"amount"},[t._v(t._s(t.formatAmount(a.row.packingAmount)))])]}}])}),e("el-table-column",{attrs:{prop:"price",label:"配送总金额(元)",align:"center","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticClass:"amount"},[t._v(t._s(t.formatAmount(a.row.price)))])]}}])}),e("el-table-column",{attrs:{prop:"percentage",label:"配送金额占比",align:"center","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticClass:"percentage-wrapper"},[e("el-progress",{attrs:{percentage:a.row.deliveryRawPercentage,color:t.getProgressColor(a.row.type,a.row.deliveryRawPercentage),"show-text":!1}}),e("span",{staticClass:"percentage-text"},[t._v(t._s(a.row.deliveryRawPercentage))])],1)]}}])})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){t.dialogVisible=!1}}},[t._v("确定")])],1)])],1)},e.staticRenderFns=[]},"O+Jk":function(t,e,a){"use strict";var n=a("E2S0");a.o(n,"render")&&a.d(e,"render",function(){return n.render}),a.o(n,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return n.staticRenderFns})},Q9u3:function(t,e,a){"use strict";a("JWP4")},T536:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left"},[e("el-row",{attrs:{gutter:10}},[e("el-col",{attrs:{span:6,sm:12,md:8,lg:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("医疗机构")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:t.itemParams.hospitalId,callback:function(e){t.$set(t.itemParams,"hospitalId",e)},expression:"itemParams.hospitalId"}},t._l(t.hospitals,function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}),1)],1)])]),e("el-col",{attrs:{span:6,sm:12,md:8,lg:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("采购时间")]),e("div",{staticClass:"searchInput"},[e("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":t.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:t.itemParams.submitTime,callback:function(e){t.$set(t.itemParams,"submitTime",e)},expression:"itemParams.submitTime"}})],1)])]),e("el-col",{attrs:{span:6,sm:12,md:8,lg:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("区划")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择区划"},on:{change:t.changeRegionName},model:{value:t.itemParams.regionId,callback:function(e){t.$set(t.itemParams,"regionId",e)},expression:"itemParams.regionId"}},t._l(t.regionList,function(t){return e("el-option",{key:t.code,attrs:{label:t.name,value:t.code}})}),1)],1)])])],1)],1),e("div",{staticClass:"right"},[e("div",{staticClass:"button-group"},[e("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:function(e){return t.initTableData()}}},[t._v("查询")]),e("el-button",{attrs:{size:"small",type:"success",icon:"el-icon-search"},on:{click:function(e){return t.exportList()}}},[t._v("导出")]),e("el-button",{attrs:{size:"small",type:"primary",plain:"",icon:"el-icon-refresh-left"},on:{click:t.handleReset}},[t._v("重置")])],1)])]),e("div",{staticClass:"hospital-table"},[e("div",{staticClass:"table-title"},[t._v("耗材采购统计列表")]),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.tableData,border:"",height:t.tableHeight}},[e("el-table-column",{attrs:{prop:"regionName",label:"区划",align:"center","min-width":"180"}}),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",align:"center","min-width":"200"}}),e("el-table-column",{attrs:{prop:"totalPackingAmount",sortable:"",label:"采购最小单位总数量",align:"center","min-width":"200"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t.formatAmount(a.row.totalPackingAmount)))])]}}])}),e("el-table-column",{attrs:{prop:"totalPrice",sortable:"",label:"采购总金额(元)",align:"center","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t.formatAmount(a.row.totalPrice)))])]}}])}),e("el-table-column",{attrs:{label:"操作",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.showDetails(a.row)}}},[t._v("详情")])]}}])})],1),e("div",{staticClass:"pagination-container"},[e("el-pagination",{attrs:{background:"","current-page":t.pagination.currentPage,"page-sizes":t.pagination.pageSizes,"page-size":t.pagination.pageSize,layout:t.pagination.layout,total:t.pagination.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),e("el-dialog",{attrs:{title:t.currentHospital+" - 采购统计详情",visible:t.dialogVisible,width:"80%","before-close":t.handleClose},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("div",{staticClass:"dialog-content"},[e("div",{staticClass:"dialog-header"},[e("div",{staticClass:"left"},[e("i",{staticClass:"el-icon-data-analysis"}),e("span",[t._v("采购分类统计")])])]),e("el-table",{attrs:{data:t.detailData,border:"","cell-style":{"text-align":"center"},"header-cell-style":{"text-align":"center","background-color":"#f5f7fa"},stripe:"","highlight-current-row":""}},[e("el-table-column",{attrs:{prop:"type",label:"采购类型",align:"center","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:t.getTagType(a.row.type)}},[t._v(t._s(a.row.type))])]}}])}),e("el-table-column",{attrs:{prop:"packingAmount",label:"采购最小单位总数量",align:"center","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticClass:"amount"},[t._v(t._s(t.formatAmount(a.row.packingAmount)))])]}}])}),e("el-table-column",{attrs:{prop:"price",label:"采购总金额(元)",align:"center","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticClass:"amount"},[t._v(t._s(t.formatAmount(a.row.price)))])]}}])}),e("el-table-column",{attrs:{prop:"percentage",label:"采购金额占比",align:"center","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticClass:"percentage-wrapper"},[e("el-progress",{attrs:{percentage:a.row.deliveryRawPercentage,color:t.getProgressColor(a.row.type,a.row.deliveryRawPercentage),"show-text":!1}}),e("span",{staticClass:"percentage-text"},[t._v(t._s(a.row.deliveryRawPercentage))])],1)]}}])})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){t.dialogVisible=!1}}},[t._v("确定")])],1)])],1)},e.staticRenderFns=[]},"Wmr+":function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var n=r(a("DWNM")),i=r(a("Q9c5"));function r(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[n.default],name:"material_list",data:function(){return{loading:!1,itemParams:{type:0,hospitalId:"",platform:"",drugType:"",attribute:"",source:"",submitTime:[new Date((new Date).getTime()-2592e6).format("yyyy-MM-dd"),(new Date).format("yyyy-MM-dd")],hospitalName:"",currentPage:1,pageSize:10},pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-6048e5),t.$emit("pick",[a,e])}},{text:"最近一个月",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-2592e6),t.$emit("pick",[a,e])}},{text:"最近三个月",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-7776e6),t.$emit("pick",[a,e])}}]},tableHeight:500,dialogVisible:!1,currentHospital:"",detailData:[],hospitals:[{id:1,name:"鄂山西工医院（普通合伙）"},{id:2,name:"江门市中心医院"},{id:3,name:"江门市五邑中医院"},{id:4,name:"江门市人民医院"},{id:5,name:"江门市妇幼保健院"},{id:6,name:"江门市第二人民医院"},{id:7,name:"江门市第三人民医院"},{id:8,name:"江门市结核病防治所"},{id:9,name:"江门市蓬江区杜阮卫生院"},{id:10,name:"江门市江海区人民医院"}],platforms:[{id:1,name:"深圳市药品交易平台"},{id:2,name:"广东省药品交易平台"},{id:3,name:"广州市药品交易平台"}],pagination:{currentPage:1,pageSize:10,total:0,pageSizes:[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper"},allTableData:[],tableData:[],areaRegoniName:"",regionList:[{code:"",name:"全部"},{code:"440703",name:"蓬江区"},{code:"440704",name:"江海区"},{code:"440705",name:"新会区"},{code:"440781",name:"台山市"},{code:"440783",name:"开平市"},{code:"440784",name:"鹤山市"},{code:"440785",name:"恩平市"}]}},watch:{tableData:{handler:function(){var t=this;this.$nextTick(function(){t.calcTableHeight()})},deep:!0},"pagination.currentPage":function(){var t=this;this.$nextTick(function(){t.calcTableHeight()})},"pagination.pageSize":function(){var t=this;this.$nextTick(function(){t.calcTableHeight()})}},mounted:function(){var t=this;this.$nextTick(function(){t.calcTableHeight(),window.addEventListener("resize",t.calcTableHeight),t.heightCheckInterval=setInterval(function(){t.calcTableHeight()},1e3),setTimeout(function(){t.heightCheckInterval&&(clearInterval(t.heightCheckInterval),t.heightCheckInterval=null)},5e3)}),this.getHospitalList(),this.initTableData()},updated:function(){var t=this;this.$nextTick(function(){t.calcTableHeight()})},beforeDestroy:function(){window.removeEventListener("resize",this.calcTableHeight),this.heightCheckInterval&&(clearInterval(this.heightCheckInterval),this.heightCheckInterval=null)},methods:{changeRegionName:function(t){for(var e=0;e<this.regionList.length;e++)if(t==this.regionList[e].code){this.areaRegoniName="全部"==this.regionList[e].name?"全市合计":this.regionList[e].name;break}},getHospitalList:function(){var t=this,e=i.default.baseContext+"/supervise/supHospital/all";this.$http_post(e,{}).then(function(e){1==e.state?t.hospitals=e.row:t.$alert(e.message)})},initTableData:function(){var t=this;this.loading=!0;var e=i.default.baseContext+"/supervise/statistics/getMaterialSourceAmountByHospital";this.$http_post(e,this.itemParams).then(function(e){1==e.state?null!=e.rows&&(t.loading=!1,t.pagination.total=e.records,t.tableData=e.rows,t.$nextTick(function(){t.calcTableHeight()})):(t.loading=!1,null!=e.message?t.$message.error(e.message):t.$message.error("系统异常"))}).catch(function(e){console.error("获取数据失败:",e),t.$message.error("获取数据失败"),t.loading=!1})},exportList:function(){var t=this;this.$confirm("确定导出耗材采购统计数据吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.$store.getters.curUser.id||t.$message.error("用户未登录"),t.loading=!0;var e=i.default.baseContext+"/supervise/statistics/exportMaterialSourceAmountByHospital";t.$post_blob(e,t.itemParams).then(function(e){if("application/json"==e.type){var a=new FileReader;return a.addEventListener("loadend",function(e){t.$message.error(JSON.parse(e.target.result).message)}),void a.readAsText(e)}var n=URL.createObjectURL(e),i=document.createElement("a");i.href=n,i.target="_blank";var r=t.itemParams.submitTime[0]+"-"+t.itemParams.submitTime[1];i.download="耗材采购统计"+r+".xls",i.click(),t.loading=!1}).catch(function(t){console.log(t)})}).catch(function(t){console.log(t)})},handleCurrentChange:function(t){this.itemParams.currentPage=t,this.pagination.currentPage=t,this.initTableData()},handleSizeChange:function(t){this.pagination.pageSize=t,this.itemParams.pageSize=t,this.initTableData()},calcTableHeight:function(){try{if(!this.$refs.tableH)return;var t=this.$refs.tableH.clientHeight,e=this.$refs.tableH.querySelector(".search-header"),a=e?e.offsetHeight:0,n=this.$refs.tableH.querySelector(".table-title"),i=n?n.offsetHeight:0,r=this.$refs.tableH.querySelector(".pagination-container"),o=t-(a+i+(r?r.offsetHeight:70)+60),s=Math.max(o,300);this.tableHeight!==s&&(this.tableHeight=s)}catch(t){console.error("计算表格高度时出错:",t),this.tableHeight=500}},showDetails:function(t){this.currentHospital=t.hospitalName,this.detailData=t.materialSourceAmountVoList,this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1},formatAmount:function(t){return null==t?0:t.toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2})},getSubTableData:function(t){var e=this.tableData.find(function(e){return e.name===t});if(!e)return[];var a=Math.round(.75*e.drugCount),n=Math.round(.2*e.drugCount),i=e.drugCount-a-n,r=Math.round(.75*e.totalAmount*100)/100,o=Math.round(.2*e.totalAmount*100)/100,s=Math.round(100*(e.totalAmount-r-o))/100;return[{type:"国家集采",drugCount:a,totalCount:Math.round(.75*e.totalCount),totalUnitCount:Math.round(.75*e.totalUnitCount),totalAmount:r,rawPercentage:"75.00%",percentage:75},{type:"非国家集采",drugCount:n,totalCount:Math.round(.2*e.totalCount),totalUnitCount:Math.round(.2*e.totalUnitCount),totalAmount:o,rawPercentage:"20.00%",percentage:20},{type:"线下采购",drugCount:i,totalCount:Math.round(.05*e.totalCount),totalUnitCount:Math.round(.05*e.totalUnitCount),totalAmount:s,rawPercentage:"5.00%",percentage:5}]},formatPercentage:function(t){var e=100,a="100%";return t.rawPercentage?(e=parseFloat(t.rawPercentage.replace("%","")),a=t.rawPercentage):t.percentage&&(a=(e=parseFloat(t.percentage))+"%"),{percentage:e,rawPercentage:a}},getTagType:function(t){switch(t){case"国家集采":return"success";case"非国家集采":return"info";case"线下采购":return"warning";default:return""}},getProgressColor:function(t,e){if(void 0!==e)return e<30?"#F56C6C":e<60?"#E6A23C":e<80?"#909399":"#67C23A";if(!t)return"#409EFF";switch(t){case"国家集采":return"#67C23A";case"非国家集采":return"#909399";case"线下采购":return"#E6A23C";default:return"#409EFF"}},handleReset:function(){this.itemParams={hospitalId:"",submitTime:[new Date((new Date).getTime()-2592e6).format("yyyy-MM-dd"),(new Date).format("yyyy-MM-dd")],platform:"",drugType:"",regionId:"",pageSize:10,currentPage:1,type:0},this.pagination.currentPage=1,this.initTableData()}}}},XBZy:function(t,e,a){},Xhkj:function(t,e,a){"use strict";a.r(e);var n=a("Humh"),i=a("E5+4");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return i[t]})}(r);a("Q9u3");var o=a("gp09"),s=Object(o.a)(i.default,n.render,n.staticRenderFns,!1,null,"1e56e1a8",null);e.default=s.exports},cUyc:function(t,e,a){"use strict";a.r(e);var n=a("Wmr+"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return n[t]})}(r);e.default=i.a},iUbp:function(t,e,a){"use strict";a("zPe/")},jBh1:function(t,e,a){"use strict";var n=a("T536");a.o(n,"render")&&a.d(e,"render",function(){return n.render}),a.o(n,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return n.staticRenderFns})},jOMy:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var n=s(a("DWNM")),i=s(a("Q9c5")),r=s(a("7XtU")),o=s(a("Xhkj"));function s(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[n.default],name:"material_list",components:{MaterialListComponent:r.default,DeliveryListComponent:o.default},data:function(){return{itemParams:{regionId:"",attribute:"",source:"",submitTime:[new Date((new Date).getTime()-2592e6).format("yyyy-MM-dd"),(new Date).format("yyyy-MM-dd")],hospitalName:"",currentPage:1,pageSize:10},pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-6048e5),t.$emit("pick",[a,e])}},{text:"最近一个月",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-2592e6),t.$emit("pick",[a,e])}},{text:"最近三个月",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-7776e6),t.$emit("pick",[a,e])}}]},params:{hospitalId:"",submitTime:["2025-03-10","2025-04-09"],platform:"",drugType:""},tableHeight:500,dialogVisible:!1,currentHospital:"",detailData:[],hospitals:[{id:1,name:"鄂山西工医院（普通合伙）"},{id:2,name:"江门市中心医院"},{id:3,name:"江门市五邑中医院"},{id:4,name:"江门市人民医院"},{id:5,name:"江门市妇幼保健院"},{id:6,name:"江门市第二人民医院"},{id:7,name:"江门市第三人民医院"},{id:8,name:"江门市结核病防治所"},{id:9,name:"江门市蓬江区杜阮卫生院"},{id:10,name:"江门市江海区人民医院"}],platforms:[{id:1,name:"深圳市药品交易平台"},{id:2,name:"广东省药品交易平台"},{id:3,name:"广州市药品交易平台"}],drugProperties:[{id:1,name:"请选择药品属性"}],pagination:{currentPage:1,pageSize:10,total:0,pageSizes:[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper"},allTableData:[],tableData:[],subTableData:[{type:"国家集采",drugCount:3,totalCount:105,totalUnitCount:1695,totalAmount:1250.05,percentage:100,rawPercentage:"100.00%",deliveryPercentage:80,deliveryRawPercentage:"80.00%",deliveryAmount:1000.04},{type:"非国家集采",drugCount:0,totalCount:0,totalUnitCount:0,totalAmount:0,percentage:0,rawPercentage:"0.00%",deliveryPercentage:0,deliveryRawPercentage:"0.00%",deliveryAmount:0},{type:"线下采购",drugCount:0,totalCount:0,totalUnitCount:0,totalAmount:0,percentage:0,rawPercentage:"0.00%",deliveryPercentage:0,deliveryRawPercentage:"0.00%",deliveryAmount:0}],areaRegoniName:"",regionList:[{code:"",name:"全部"},{code:"440703",name:"蓬江区"},{code:"440704",name:"江海区"},{code:"440705",name:"新会区"},{code:"440781",name:"台山市"},{code:"440783",name:"开平市"},{code:"440784",name:"鹤山市"},{code:"440785",name:"恩平市"}],activeTab:"purchase"}},mounted:function(){var t=this;window.addEventListener("resize",this.handleResize),this.$nextTick(function(){t.handleResize()})},beforeDestroy:function(){window.removeEventListener("resize",this.handleResize)},methods:{handleResize:function(){var t=this;this.$nextTick(function(){var e=t.$children.find(function(t){return"material_list"===t.$options.name});e&&"function"==typeof e.calcTableHeight&&e.calcTableHeight()})},changeRegionName:function(t){for(var e=0;e<this.regionList.length;e++)if(t==this.regionList[e].code){this.areaRegoniName="全部"==this.regionList[e].name?"全市合计":this.regionList[e].name;break}},getHospitalList:function(){var t=this,e=i.default.baseContext+"/supervise/supHospital/all";this.$http_post(e,{}).then(function(e){1==e.state?t.hospitals=e.row:t.$alert(e.message)})},initTableData:function(){var t=this,e=i.default.baseContext+"/supervise/statistics/getMaterialSourceAmountByHospital";this.$http_post(e,this.itemParams).then(function(e){1==e.state?null!=e.rows&&(console.log(e),t.pagination.total=e.records):null!=e.message?t.$message.error(e.message):t.$message.error("系统异常")}).catch(function(t){console.log(t)}),this.allTableData=[{name:"鄂山西工医院（普通合伙）",drugCount:3,totalCount:105,totalUnitCount:1695,totalAmount:1250.05,percentage:100,rawPercentage:"100%",type:"国家集采"},{name:"江门市中心医院",drugCount:188,totalCount:479487,totalUnitCount:5579116,totalAmount:11433471.18,percentage:100,rawPercentage:"50%",type:"国家集采"},{name:"江门市五邑中医院",drugCount:116,totalCount:110174,totalUnitCount:534844,totalAmount:1636597.06,percentage:100,rawPercentage:"20%",type:"国家集采"},{name:"江门市人民医院",drugCount:12,totalCount:20714,totalUnitCount:73984,totalAmount:323798.62,percentage:100,rawPercentage:"100%",type:"国家集采"},{name:"江门市妇幼保健院",drugCount:46,totalCount:18269,totalUnitCount:133999,totalAmount:202980.66,percentage:100,rawPercentage:"100%",type:"国家集采"},{name:"江门市第二人民医院",drugCount:47,totalCount:17262,totalUnitCount:145674,totalAmount:168508.4,percentage:100,rawPercentage:"100%",type:"国家集采"},{name:"江门市第三人民医院",drugCount:3,totalCount:420,totalUnitCount:10340,totalAmount:2482,percentage:100,rawPercentage:"100%",type:"国家集采"},{name:"江门市结核病防治所",drugCount:3,totalCount:390,totalUnitCount:1460,totalAmount:4737.4,percentage:100,rawPercentage:"100%",type:"国家集采"},{name:"江门市蓬江区杜阮卫生院",drugCount:7,totalCount:3458,totalUnitCount:3458,totalAmount:12754.24,percentage:100,rawPercentage:"100%",type:"国家集采"},{name:"江门市江海区人民医院",drugCount:2,totalCount:140,totalUnitCount:140,totalAmount:3233.8,percentage:100,rawPercentage:"100%",type:"国家集采"},{name:"江门市江海区人民医院",drugCount:2,totalCount:140,totalUnitCount:140,totalAmount:3233.8,percentage:100,rawPercentage:"100%",type:"国家集采"}],this.pagination.total=this.allTableData.length,this.getTableData()},getTableData:function(){var t=(this.pagination.currentPage-1)*this.pagination.pageSize,e=t+this.pagination.pageSize;this.tableData=this.allTableData.slice(t,e)},handleCurrentChange:function(t){this.pagination.currentPage=t,this.itemParams.currentPage=t,this.getTableData()},handleSizeChange:function(t){this.pagination.pageSize=t,this.pagination.pageSize=t,this.pagination.currentPage=1,this.getTableData()},calcTableHeight:function(){var t=this.$refs.tableH.offsetHeight,e=document.querySelector(".search-header").offsetHeight;this.tableHeight=t-e-70-30},showDetails:function(t){this.currentHospital=t.name,this.detailData=this.getSubTableData(t.name),this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1},getSubTableData:function(t){var e=this.tableData.find(function(e){return e.name===t});if(!e)return[];var a=Math.round(.75*e.drugCount),n=Math.round(.2*e.drugCount),i=e.drugCount-a-n,r=Math.round(.75*e.totalAmount*100)/100,o=Math.round(.2*e.totalAmount*100)/100,s=Math.round(100*(e.totalAmount-r-o))/100;return[{type:"国家集采",drugCount:a,totalCount:Math.round(.75*e.totalCount),totalUnitCount:Math.round(.75*e.totalUnitCount),totalAmount:r,rawPercentage:"75.00%",percentage:75},{type:"非国家集采",drugCount:n,totalCount:Math.round(.2*e.totalCount),totalUnitCount:Math.round(.2*e.totalUnitCount),totalAmount:o,rawPercentage:"20.00%",percentage:20},{type:"线下采购",drugCount:i,totalCount:Math.round(.05*e.totalCount),totalUnitCount:Math.round(.05*e.totalUnitCount),totalAmount:s,rawPercentage:"5.00%",percentage:5}]},formatAmount:function(t){return null==t?(console.log(t),0):t.toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2})},formatPercentage:function(t){var e=100,a="100%";return t.rawPercentage?(e=parseFloat(t.rawPercentage.replace("%","")),a=t.rawPercentage):t.percentage&&(a=(e=parseFloat(t.percentage))+"%"),{percentage:e,rawPercentage:a}},getTagType:function(t){switch(t){case"国家集采":return"success";case"非国家集采":return"info";case"线下采购":return"warning";default:return""}},getProgressColor:function(t,e){if(void 0!==e)return e<30?"#F56C6C":e<60?"#E6A23C":e<80?"#909399":"#67C23A";if(!t)return"#409EFF";switch(t){case"国家集采":return"#67C23A";case"非国家集采":return"#909399";case"线下采购":return"#E6A23C";default:return"#409EFF"}},handleSearch:function(){this.pagination.currentPage=1,this.getTableData()},handleReset:function(){this.params={hospitalId:"",submitTime:["2025-03-10","2025-04-09"],platform:"",drugType:""},this.pagination.currentPage=1,this.getTableData()},handleTabChange:function(t){console.log("Tab changed to:",t.name),t.name}}}},lK0N:function(t,e,a){"use strict";a("XBZy")},"zPe/":function(t,e,a){}}]);