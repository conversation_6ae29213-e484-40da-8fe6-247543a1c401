(window.webpackJsonp=window.webpackJsonp||[]).push([[56],{"6ASU":function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var s=r(a("Q9c5")),i=r(a("DWNM")),o=r(a("XRYr"));function r(e){return e&&e.__esModule?e:{default:e}}t.default={name:"delivery-list",mixins:[i.default],data:function(){return{pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=(new Date).format("yyyy-MM-dd HH:mm:ss"),a=new Date,s=new Date(a.getTime()-6048e5).format("yyyy-MM-dd HH:mm:ss");e.$emit("pick",[s,t])}},{text:"最近一个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd HH:mm:ss"),a=new Date,s=new Date(a.getTime()-2592e6).format("yyyy-MM-dd HH:mm:ss");e.$emit("pick",[s,t])}},{text:"最近三个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd HH:mm:ss"),a=new Date,s=new Date(a.getTime()-7776e6).format("yyyy-MM-dd HH:mm:ss");e.$emit("pick",[s,t])}}]},adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1,rLoading:{},fileList:[],headers:{},uploadData:{},importDialogVisible:!1,warningData:[],tableHeight:100,dialogVisible:!1,dataList:[],hospitalList:[],params:{deliveryTime:"",orderCode:"",catalogName:"",page:1,limit:10,records:0,deliveryName:"",deliveryCode:"",hospitalName:""},orderData:{data:{},orderItem:[],hospital:{}},dateChangeValue:0}},props:{},computed:{uploadUrl:function(){return s.default.baseContext+"/file/upload"}},watch:{},mounted:function(){var e=this,t=o.default.doCloundRequest(s.default.app_key,s.default.app_security,"");this.headers["x-aep-appkey"]=t["x-aep-appkey"],this.headers["x-aep-signature"]=t["x-aep-signature"],this.headers["x-aep-timestamp"]=t["x-aep-timestamp"],this.headers["x-aep-nonce"]=t["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,this.initRole(),this.onQuery(),this.getHospitalList(),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}},methods:{setDate:function(){var e=(new Date).format("yyyy-MM-dd"),t=new Date,a=new Date(t.getTime()-2592e6).format("yyyy-MM-dd");this.params.deliveryTime=[a,e]},initRole:function(){var e=this.$store.getters.curUser.roleCode;-1!=e.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=e.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=e.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=e.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)},getHospitalList:function(){var e=this,t=s.default.baseContext+"/supervise/supHospital/all";this.$http_post(t,{}).then(function(t){1==t.state?e.hospitalList=t.row:e.$alert(t.message)})},downloadTemplate:function(){var e=s.default.baseContext+"/file/downloadDeliveryImportTemplate",t=document.createElement("a");t.href=e,document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(e)},submitUpload:function(){var e=this;this.rLoading=this.openLoading("导入中，请耐心等待...");var t=this.uploadData.docId;this.$http_post(s.default.baseContext+"/supervise/supDelivery/importCountryDelivery?docId="+t,{},null,{timeout:1e5}).then(function(t){1==t.state?(e.onQuery(),e.rLoading.close(),e.importDialogVisible=!1,e.$alert("导入成功，请前往【配送单批次日志】查看导入结果")):(e.onQuery(),e.rLoading.close(),e.importDialogVisible=!1,e.$message.error(t.message))})},onSuccess:function(e,t,a){if("1"!=e.state)return this.$refs.upload.clearFiles(),this.$alert(e.message),!1;t.docId=e.row.id,this.uploadData={docId:e.row.id,name:e.row.name}},onError:function(e,t,a){this.rLoading.close(),console.log(e),this.$message.error("上传失败,请重试")},handlePreview:function(e){var t=this.downloadUrl+"?docId="+encodeURI(encodeURI(e.docId))+"&fileName="+e.name;console.log(e);var a=document.createElement("a");a.href=t,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(t)},handleExceed:function(e,t){this.$message.warning("一次只能上传一个Excel文件")},beforeUpload:function(e){if(e.size/1024/1024>20)return this.$message.error("上传文件过大，请分开上传，单个文件最大为20M。"),!1;var t=e.name;return"xlsx"==t.substring(t.length-4)||"xls"==t.substring(t.length-3)||(this.$message.error("上传文件格式只能为【.xlsx】或【.xls】"),!1)},beforeRemove:function(e,t){return!this.beforeUpload(e)||this.$confirm("确定移除 "+e.name+"？")},handleRemove:function(e,t){return this.uploadData={},console.log(e.docId,e,t),!0},deliveryImport:function(){this.importDialogVisible=!0},getWaringType:function(e){for(var t=0;t<this.warningData.length;t++)if(this.warningData[t].value==e)return this.warningData[t].label},getWaring:function(e){for(var t=[],a=e.split(","),s=0;s<a.length;s++){var i=this.getWaringType(a[s]);i&&t.push({name:i})}return t},getDictItem:function(e){var t=this,a=this.openLoading();this.$http_post(s.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(s){var i=s.rows;1==s.state?("WARNING"==e&&(t.warningData=i),a.close()):(a.close(),t.$message.error(s.message))})},showDetail:function(e){this.$router.push({name:"deliveryDetail",query:{deliveryCode:e.deliveryCode}})},onSearch:function(e){"reset"==e?(this.params.catalogName="",this.params.deliveryCode="",this.params.hospitalName="",this.params.deliveryName="",this.params.orderCode="",this.params.deliveryTime="",this.params.page=1,this.dateChangeValue=0,this.onQuery()):(this.params.page=1,this.dateChangeValue=1,this.onQuery())},onPageClick:function(e){this.params.page=e,this.onQuery()},onQuery:function(){var e=this;0!=this.dateChangeValue&&""!=this.dateChangeValue||this.setDate();var t=this.params,a=this.openLoading(),i=s.default.baseContext+"/supervise/supDeliveryItem/getDeliveryItemList";this.$http_post(i,t).then(function(t){1==t.state?(e.dataList=t.rows,e.params.records=t.records,a.close()):(a.close(),e.$alert(t.message))})},time:function(e,t){if(void 0==e.submitTime||""==e.submitTime)return"";var a=new Date(e.submitTime),s=a.getFullYear()+"-",i=(a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-",o=a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ";a.getHours(),a.getHours(),a.getMinutes(),a.getMinutes(),a.getSeconds(),a.getSeconds();return s+i+o},init:function(){}},beforeDestroy:function(){window.onresize=null},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e),a=t.getFullYear()+"-",s=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",i=t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ";t.getHours(),t.getHours(),t.getMinutes(),t.getMinutes(),t.getSeconds(),t.getSeconds();return a+s+i}}}},B4nc:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.staticRenderFns=t.render=void 0;var s=function(e){return e&&e.__esModule?e:{default:e}}(a("/umX"));t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left"},[t("el-row",{attrs:{gutter:10}},[t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("配送单号")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入配送单号"},model:{value:e.params.deliveryCode,callback:function(t){e.$set(e.params,"deliveryCode",t)},expression:"params.deliveryCode"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("订单号")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入订单号"},model:{value:e.params.orderCode,callback:function(t){e.$set(e.params,"orderCode",t)},expression:"params.orderCode"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("通用名")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入药品通用名"},model:{value:e.params.catalogName,callback:function(t){e.$set(e.params,"catalogName",t)},expression:"params.catalogName"}})],1)])]),this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("医疗机构")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:e.params.hospitalName,callback:function(t){e.$set(e.params,"hospitalName",t)},expression:"params.hospitalName"}},e._l(e.hospitalList,function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}),1)],1)])]):e._e(),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("配送企业")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入配送企业名称"},model:{value:e.params.deliveryName,callback:function(t){e.$set(e.params,"deliveryName",t)},expression:"params.deliveryName"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("配送时间")]),t("div",{staticClass:"searchInput"},[t("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","pickinvoiceVoucherser-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",clearable:"",align:"left"},model:{value:e.params.deliveryTime,callback:function(t){e.$set(e.params,"deliveryTime",t)},expression:"params.deliveryTime"}})],1)])])],1)],1),t("div",{staticClass:"right"},[t("div",{staticStyle:{"margin-bottom":"5px"}},[t("el-button",{attrs:{size:"small",type:"warning",icon:"el-icon-download"},on:{click:e.deliveryImport}},[e._v("导入配送单")])],1),t("div",[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1)])]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.dataList,"highlight-current-row":"",height:e.tableHeight,border:""}},[t("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单号",width:"200","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"200","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"deliveryName",label:"配送企业",width:"200","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"200","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"单价（元）",width:"100"}}),t("el-table-column",{attrs:{prop:"num",label:"发货数量"}}),t("el-table-column",{attrs:{prop:"amount",label:"金额(元)"}}),t("el-table-column",{attrs:{prop:"orderCode",label:"订单编号",width:"200"}}),t("el-table-column",{attrs:{prop:"deliveryTime",label:"配送时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e._f("formatTime")(a.row.deliveryTime)))])]}}])}),t("el-table-column",{attrs:{prop:"stockStatus",label:"入库状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.stockStatus&&"0"!=a.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未入库")])],1),"1"==a.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已入库")])],1):e._e(),"2"==a.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分入库")])],1):e._e()]}}])}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showDetail(a.row)}}},[e._v("查看详情")])]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)]),t("el-dialog",{staticClass:"upload-box",attrs:{title:"药品配送明细单导入",visible:e.importDialogVisible,"append-to-body":"",width:"40%"},on:{"update:visible":function(t){e.importDialogVisible=t}}},[t("el-form",{ref:"importDataForm",attrs:{"label-position":"left",model:e.uploadData}},[t("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:(0,s.default)({drag:"",limit:1,headers:e.headers,action:e.uploadUrl,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"on-exceed":e.handleExceed,"before-upload":e.beforeUpload,"before-remove":e.beforeRemove,"on-success":e.onSuccess,"on-error":e.onError,multiple:"",accept:".xls,.xlsx","file-list":e.fileList},"multiple",!1)},[t("i",{staticClass:"el-icon-upload"}),t("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),t("em",[e._v("点击上传")])]),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("只能上传【 .xlsx / .xls 】文件")])])],1),t("span",{staticStyle:{color:"red"}},[e._v("提示：请下载 Excel 模板，按照格式进行导入！")]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitUpload("")}}},[e._v("确 定")]),t("el-button",{attrs:{type:"warning"},on:{click:function(t){return e.downloadTemplate("")}}},[e._v("下载模板")]),t("el-button",{on:{click:function(t){e.importDialogVisible=!1}}},[e._v("取消")])],1)],1)],1)},t.staticRenderFns=[]},H7GH:function(e,t,a){"use strict";a.r(t);var s=a("U7wa"),i=a("MoQc");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,function(){return i[e]})}(o);a("oZYT");var r=a("gp09"),l=Object(r.a)(i.default,s.render,s.staticRenderFns,!1,null,"1794ea02",null);t.default=l.exports},MoQc:function(e,t,a){"use strict";a.r(t);var s=a("6ASU"),i=a.n(s);for(var o in s)["default"].indexOf(o)<0&&function(e){a.d(t,e,function(){return s[e]})}(o);t.default=i.a},U7wa:function(e,t,a){"use strict";var s=a("B4nc");a.o(s,"render")&&a.d(t,"render",function(){return s.render}),a.o(s,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return s.staticRenderFns})},oZYT:function(e,t,a){"use strict";a("yf4j")},yf4j:function(e,t,a){}}]);