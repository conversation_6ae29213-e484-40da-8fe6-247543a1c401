(window.webpackJsonp=window.webpackJsonp||[]).push([[90],{"97tf":function(t,e,a){"use strict";var s=a("toQo");a.o(s,"render")&&a.d(e,"render",function(){return s.render}),a.o(s,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return s.staticRenderFns})},Ay8H:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var s=n(a("Q9c5")),r=n(a("DWNM"));function n(t){return t&&t.__esModule?t:{default:t}}e.default={name:"settlementList",mixins:[r.default],data:function(){return{hospitalList:[],rLoading:{},tableHeight:100,dataList:[],params:{page:1,limit:10,records:0,num:"",date:"",bizStatus:"",startDate:"",endDate:""},adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1}},props:{},watch:{},computed:{uploadUrl:function(){}},mounted:function(){var t=this;this.onQuery(),this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}},methods:{onSearch:function(t){"reset"==t?(this.params.num="",this.params.bizStatus="",this.params.date="",this.params.startDate="",this.params.endDate="",this.onQuery()):""!=this.params.num||""!=this.params.bizStatus||""!=this.params.date?(this.params.page=1,this.onQuery()):this.$message.error("请输入查询条件")},onPageClick:function(t){this.params.page=t,this.onQuery()},onQuery:function(){var t=this;this.params.date&&(this.params.startDate=this.params.date[0],this.params.endDate=this.params.date[1]);var e=this.params,a=this.openLoading(),r=s.default.baseContext+"/supervise/supSettlement/list";this.$http_post(r,e).then(function(e){1==e.state?(t.dataList=e.rows,t.params.records=e.records,a.close()):(a.close(),t.$alert(e.message))})},detail:function(t){this.$router.push({name:"settlementDetail",query:{settlementId:t.id}})},recall:function(t){}},beforeDestroy:function(){window.onresize=null},filters:{formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}}}},Dxcs:function(t,e,a){},GMlR:function(t,e,a){"use strict";a("Dxcs")},RIfq:function(t,e,a){"use strict";a.r(e);var s=a("Ay8H"),r=a.n(s);for(var n in s)["default"].indexOf(n)<0&&function(t){a.d(e,t,function(){return s[t]})}(n);e.default=r.a},cE31:function(t,e,a){"use strict";a.r(e);var s=a("97tf"),r=a("RIfq");for(var n in r)["default"].indexOf(n)<0&&function(t){a.d(e,t,function(){return r[t]})}(n);a("GMlR");var l=a("gp09"),i=Object(l.a)(r.default,s.render,s.staticRenderFns,!1,null,"1843b905",null);e.default=i.exports},toQo:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left"},[e("el-row",{attrs:{gutter:10}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("结算单编号")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入通用名"},model:{value:t.params.num,callback:function(e){t.$set(t.params,"num",e)},expression:"params.num"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("状态")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择状态"},model:{value:t.params.bizStatus,callback:function(e){t.$set(t.params,"bizStatus",e)},expression:"params.bizStatus"}},[e("el-option",{key:"1",attrs:{label:"在办",value:"1"}}),e("el-option",{key:"2",attrs:{label:"结算单阶段完成",value:"2"}}),e("el-option",{key:"2",attrs:{label:"不予办理",value:"3"}})],1)],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("生成时间")]),e("div",{staticClass:"searchInput"},[e("el-date-picker",{attrs:{size:"small","value-format":"yyyy-MM-dd HH:mm:ss",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:t.params.date,callback:function(e){t.$set(t.params,"date",e)},expression:"params.date"}})],1)])])],1)],1),e("div",{staticClass:"right"},[e("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1)]),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.dataList,"highlight-current-row":"",border:"",height:t.tableHeight}},[e("el-table-column",{attrs:{type:"index",label:"序号",width:"50"}}),e("el-table-column",{attrs:{prop:"num",label:"结算单编号"}}),e("el-table-column",{attrs:{prop:"count",label:"结算总笔数"}}),e("el-table-column",{attrs:{prop:"totalPrice",label:"结算总金额"}}),e("el-table-column",{attrs:{prop:"backCount",label:"打回笔数"}}),e("el-table-column",{attrs:{prop:"backPrice",label:"打回总金额"}}),e("el-table-column",{attrs:{prop:"bizStatus",label:"业务状态"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.bizStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("在办")])],1):t._e(),"2"==a.row.bizStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("结算单阶段完成")])],1):t._e(),"3"==a.row.bizStatus?e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("不予办理")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"curNode",label:"当前环节"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s("2"==a.row.bizStatus?"结算单阶段完成":"1"==a.row.curNode?"结算申请":"2"==a.row.curNode?"结算申请审核":"3"==a.row.curNode?"结算申请复核":"4"==a.row.curNode?"对账单生成":"5"==a.row.curNode?"结算对账医疗机构确认":"6"==a.row.curNode?"结算对账药企确认":"7"==a.row.curNode?"结算支付审批":"8"==a.row.curNode?"结算支付完成":""))])]}}])}),e("el-table-column",{attrs:{prop:"creationTime",label:"生成时间"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t._f("formatTime")(a.row.creationTime)))])]}}])}),e("el-table-column",{attrs:{prop:"createName",label:"创建人",width:"180px"}}),t.$route.query.type?t._e():e("el-table-column",{attrs:{label:"操作",align:"center",width:"100",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.detail(a.row)}}},[t._v("详情")])]}}],null,!1,316531087)})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)])],1)},e.staticRenderFns=[]}}]);