(window.webpackJsonp=window.webpackJsonp||[]).push([[43],{"1xY1":function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var n=r(a("Q9c5")),s=r(a("DWNM")),i=(r(a("XRYr")),r(a("rGKd")));function r(t){return t&&t.__esModule?t:{default:t}}e.default={name:"countryPurchase",mixins:[s.default],data:function(){return{batchList:[],dataList:[],curBatchList:[],hospitalList:[],adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1,areaRegionAdmin:!1,regionList:[{code:"",name:"全部"},{code:"440703",name:"蓬江区"},{code:"440704",name:"江海区"},{code:"440705",name:"新会区"},{code:"440781",name:"台山市"},{code:"440783",name:"开平市"},{code:"440784",name:"鹤山市"},{code:"440785",name:"恩平市"}],params:{batch:"",records:0,catalogName:"",hospitalName:"",submitTime:[],startTime:"",endTime:"",regionId:""},tableHeight:100,position:"",pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,n=new Date(a.getTime()-6048e5).format("yyyy-MM-dd");t.$emit("pick",[n,e])}},{text:"最近一个月",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,n=new Date(a.getTime()-2592e6).format("yyyy-MM-dd");t.$emit("pick",[n,e])}},{text:"最近三个月",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,n=new Date(a.getTime()-7776e6).format("yyyy-MM-dd");t.$emit("pick",[n,e])}}]}}},props:{},computed:{},watch:{},methods:{formatDate:function(t){var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()+1<10?"0"+e.getDate():e.getDate())},formatBatch:function(t){if(void 0==t||""==t)return"";for(var e=0;e<this.batchList.length;e++)if(this.batchList[e].code==t)return this.batchList[e].batchName+"("+this.batchList[e].taskStartTime.substr(0,10)+"至"+this.batchList[e].taskEndTime.substr(0,10)+")";return""},initRole:function(){var t=this.$store.getters.curUser.roleCode;-1!=t.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=t.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=t.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=t.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)},getHospitalList:function(){var t=this,e=n.default.baseContext+"/supervise/supHospital/all";this.$http_post(e,{}).then(function(e){1==e.state?t.hospitalList=e.row:t.$alert(e.message)})},onSearch:function(t){"reset"==t?(this.params.batch="",this.params.hospitalName="",this.params.regionId="",this.params.submitTime="",this.params.startTime="",this.params.endTime="",this.onQuery()):(this.params.page=1,this.onQuery())},onPageClick:function(t){this.params.page=t,this.onQuery()},onQuery:function(){var t=this,e=this.params,a=this.openLoading(),s=n.default.baseContext+"/supervise/supCountryPurchase/batchSummary";this.$http_get(s,e).then(function(e){if(1==e.state){var n=e.row;t.curBatchList=n.batchList,t.dataList=n.list,a.close(),t.$nextTick(function(){t.calcTableHeight()})}else a.close(),t.$alert(e.message)})},setBatchList:function(t){for(var e=t.rows,a=0;a<e.length;a++)this.batchList.push(e[a])},batchSummaryExport:function(){var t=this,e=this.$loading({lock:!0,text:"正在导出数据，请稍候...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),a=this.params;a.batch=a.batch+"",console.log("导出数据为：",a);var s=n.default.baseContext+"/supervise/supCountryPurchase/batchSummaryExport";this.$post_blob(s,a).then(function(n){e.close();var s=a.batch.split(",");if(console.log(s),t.params.batch=s,"application/json"==n.type){var i=new FileReader;return i.addEventListener("loadend",function(e){t.$message.error(JSON.parse(e.target.result).message)}),void i.readAsText(n)}var r=URL.createObjectURL(n),o=document.createElement("a");o.href=r,o.target="_blank";var c=(new Date).getTime();o.download="国家集采批次汇总（入库）_"+c+".xlsx",o.click(),t.$message.success("导出成功"),setTimeout(function(){URL.revokeObjectURL(r)},100)}).catch(function(a){e.close(),t.$message.error("导出失败："+(a.message||"未知错误")),console.error(a)})},getSummaries:function(t){var e=t.columns,a=t.data,n=[];return e.forEach(function(t,e){if(0!==e){var s=t.property;if(s&&(s.includes("_purchase")||s.includes("_totalPurchaseCount")||s.includes("_countryPrice"))){var i=a.map(function(t){var e=t[s];return e&&!isNaN(Number(e))?Number(e):0});if(i.every(function(t){return 0===t}))n[e]="0";else{var r=i.reduce(function(t,e){var a=Number(e);return isNaN(a)?t:t+a},0);s.includes("_totalPurchase")?n[e]="":s.includes("_countryPrice")?n[e]=r.toFixed(2):n[e]=r.toFixed(0)}}else n[e]=""}else n[e]="合计"}),n},calcTableHeight:function(){if(this.$refs.tableH){var t=window.innerHeight,e=this.$refs.tableH.querySelector(".search-header"),a=e?e.offsetHeight:0,n=t-this.$refs.tableH.getBoundingClientRect().top-a-20;this.tableHeight=Math.max(n,300)}}},mounted:function(){var t=this;i.default.getBatchList(this.setBatchList),this.initRole(),this.onQuery(),this.getHospitalList(),this.$nextTick(function(){t.calcTableHeight()}),window.onresize=function(){t.calcTableHeight()}},beforeDestroy:function(){window.onresize=null}}},"4VsF":function(t,e,a){"use strict";var n=a("nP1S");a.o(n,"render")&&a.d(e,"render",function(){return n.render}),a.o(n,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return n.staticRenderFns})},"6ZdW":function(t,e,a){"use strict";a.r(e);var n=a("4VsF"),s=a("EQ1Q");for(var i in s)["default"].indexOf(i)<0&&function(t){a.d(e,t,function(){return s[t]})}(i);a("vs3U");var r=a("gp09"),o=Object(r.a)(s.default,n.render,n.staticRenderFns,!1,null,"5478b093",null);e.default=o.exports},EQ1Q:function(t,e,a){"use strict";a.r(e);var n=a("1xY1"),s=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,function(){return n[t]})}(i);e.default=s.a},FVSd:function(t,e,a){},nP1S:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header"},[e("el-form",{staticClass:"search-form",attrs:{inline:!0,model:t.params,size:"small"}},[this.adminRole||this.medicalAdmin||this.socialAdmin||this.areaRegionAdmin?e("el-form-item",{attrs:{label:"医疗机构"}},[e("el-select",{staticClass:"search-input",attrs:{clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:t.params.hospitalName,callback:function(e){t.$set(t.params,"hospitalName",e)},expression:"params.hospitalName"}},t._l(t.hospitalList,function(t){return e("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})}),1)],1):t._e(),this.adminRole||this.medicalAdmin||this.socialAdmin?e("el-form-item",{attrs:{label:"区划"}},[e("el-select",{staticClass:"search-input",attrs:{placeholder:"请选择区划",clearable:""},model:{value:t.params.regionId,callback:function(e){t.$set(t.params,"regionId",e)},expression:"params.regionId"}},t._l(t.regionList,function(t){return e("el-option",{key:t.code,attrs:{label:t.name,value:t.code}})}),1)],1):t._e(),e("el-form-item",{attrs:{label:"批次"}},[e("el-select",{staticClass:"search-input",attrs:{multiple:"","collapse-tags":"",clearable:"",filterable:"",placeholder:"请选择批次"},model:{value:t.params.batch,callback:function(e){t.$set(t.params,"batch",e)},expression:"params.batch"}},t._l(t.batchList,function(a){return e("el-option",{key:a.code,attrs:{label:a.batchName,value:a.code}},[e("span",{staticStyle:{float:"left"}},[t._v(t._s(a.batchName))]),e("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"12px"}},[t._v("\n                            "+t._s(a.taskStartTime?a.taskStartTime.substr(0,10):"")+" 至 \n                            "+t._s(a.taskEndTime?a.taskEndTime.substr(0,10):"")+"\n                        ")])])}),1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")]),e("el-button",{attrs:{type:"warning",icon:"el-icon-download"},on:{click:t.batchSummaryExport}},[t._v("导出")])],1)],1)],1),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.dataList,"highlight-current-row":"",height:t.tableHeight,"show-summary":"","summary-method":t.getSummaries,border:""}},[e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"200px",fixed:"left","show-tooltip-when-overflow":"","class-name":"hospital-column"}}),t._l(t.curBatchList,function(a,n){return e("el-table-column",{key:n,attrs:{"show-tooltip-when-overflow":"",align:"center","class-name":"batch-column",label:t.formatBatch(a)}},[e("el-table-column",{attrs:{prop:a+"_purchase",label:"任务量（片/支/粒）",align:"center"}}),e("el-table-column",{attrs:{prop:a+"_totalPurchaseCount",label:"累计入库量（片/支/粒）",align:"center"}}),e("el-table-column",{attrs:{prop:a+"_totalPurchase",label:"完成率（入库）",align:"center"}}),e("el-table-column",{attrs:{prop:a+"_countryPrice",label:"累计金额（入库）",align:"center"}})],1)})],2)],1)])],1)},e.staticRenderFns=[]},rGKd:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var n=i(a("Q9c5")),s=i(a("ERIh"));function i(t){return t&&t.__esModule?t:{default:t}}var r=n.default.baseContext+"/supervise/supDrugBatch/getBatchList";var o={getBatchList:function(t){s.default.$http_api("GET",r,{},{jsonContentType:!1,responseType:null,addBaseUrl:!0}).then(function(e){1==e.state?function(t){return null!=t&&void 0!==t&&"function"==typeof t}(t)&&t(e):console.warn("查询集采批次失败",e.message)}).catch(function(t){console.warn("查询集采批次失败",t.message)})}};e.default=o},vs3U:function(t,e,a){"use strict";a("FVSd")}}]);