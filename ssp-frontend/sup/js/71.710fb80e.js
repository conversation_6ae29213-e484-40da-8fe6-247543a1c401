(window.webpackJsonp=window.webpackJsonp||[]).push([[71],{GmcP:function(e,t,n){"use strict";n.r(t);var s=n("yoqp"),a=n.n(s);for(var r in s)["default"].indexOf(r)<0&&function(e){n.d(t,e,function(){return s[e]})}(r);t.default=a.a},dOdf:function(e,t,n){"use strict";n.r(t);var s=n("ipT+"),a=n("GmcP");for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,function(){return a[e]})}(r);n("jlrx");var i=n("gp09"),o=Object(i.a)(a.default,s.render,s.staticRenderFns,!1,null,"5c0732dc",null);t.default=o.exports},g2nz:function(e,t,n){},"ipT+":function(e,t,n){"use strict";var s=n("y6/P");n.o(s,"render")&&n.d(t,"render",function(){return s.render}),n.o(s,"staticRenderFns")&&n.d(t,"staticRenderFns",function(){return s.staticRenderFns})},jlrx:function(e,t,n){"use strict";n("g2nz")},"y6/P":function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"},[t("span",[e._v("标题")]),t("el-input",{attrs:{placeholder:"请输入发件标题"},model:{value:e.params.title,callback:function(t){e.$set(e.params,"title",t)},expression:"params.title"}}),e.$route.query.isSee?e._e():t("span",[e._v("状态")]),e.$route.query.isSee?e._e():t("el-select",{attrs:{placeholder:"请选择查阅状态"},model:{value:e.params.isSee,callback:function(t){e.$set(e.params,"isSee",t)},expression:"params.isSee"}},[t("el-option",{attrs:{label:"未查阅",value:"0"}}),t("el-option",{attrs:{label:"已查阅",value:"1"}})],1),t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1)]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.dataList,"highlight-current-row":"",height:e.tableHeight},on:{"current-change":e.handleCurrentChange}},[t("el-table-column",{attrs:{label:"标题",prop:"title"}}),t("el-table-column",{attrs:{label:"状态",prop:"isSee"},scopedSlots:e._u([{key:"default",fn:function(n){return["1"==n.row.isSee?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已查阅")])],1):e._e(),"0"==n.row.isSee?t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("待查阅")])],1):e._e()]}}])}),t("el-table-column",{attrs:{label:"创建时间",prop:"creationTime",formatter:e.time}}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"200"},scopedSlots:e._u([{key:"default",fn:function(n){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.del(n.row)}}},[e._v("删除")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.$router.push({path:"sendPage",query:{id:n.row.officialId,editType:"show",updateIsSee:n.row.id,isSee:e.$route.query.isSee}})}}},[e._v("查看")])]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)])],1)},t.staticRenderFns=[]},yoqp:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var s=r(n("Q9c5")),a=r(n("DWNM"));function r(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[a.default],data:function(){return{dataList:[],params:{page:1,limit:10,records:0,isSee:"",title:""},tableHeight:100}},props:{},computed:{},watch:{$route:function(){"0"==this.$route.query.isSee?this.params.isSee="0":this.params.isSee="",this.onQuery()},deep:!0},methods:{time:function(e,t){if(void 0==e.creationTime||""==e.creationTime)return"";var n=new Date(e.creationTime);return n.getFullYear()+"-"+((n.getMonth()+1<10?"0"+(n.getMonth()+1):n.getMonth()+1)+"-")+(n.getDate()<10?"0"+n.getDate()+" ":n.getDate()+" ")+(n.getHours()<10?"0"+n.getHours()+":":n.getHours()+":")+(n.getMinutes()<10?"0"+n.getMinutes()+":":n.getMinutes()+":")+(n.getSeconds()<10?"0"+n.getSeconds():n.getSeconds())},del:function(e){var t=this;this.$alert("确定删除【"+e.title+"】数据吗","确认信息",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,type:"warning"}).then(function(){var n=t.openLoading();t.$http_post(s.default.baseContext+"/supervise/supOfficialReceive/delete/"+e.id,null).then(function(e){1==e.state?(t.$message.success("删除成功"),t.onQuery(),n.close()):(n.close(),t.$message.error(e.message))})}).catch(function(e){console.log(e)})},handleCurrentChange:function(e){},onSearch:function(e){"reset"==e?(this.params.isSee="",this.params.title="",this.onQuery()):""!=this.params.isSee||""!=this.params.title?(this.params.page=1,this.onQuery()):this.$message.warning("请输入标题或状态查询")},onPageClick:function(e){this.params.page=e,this.onQuery()},onQuery:function(){var e=this;"0"==this.$route.query.isSee&&(this.params.isSee="0");var t=this.params,n=this.openLoading(),a=s.default.baseContext+"/supervise/supOfficialReceive/list";this.$http_post(a,t).then(function(t){if(1==t.state){var s=t.rows;e.dataList=s,e.params.records=t.records,n.close()}else n.close(),e.$alert(t.message)})}},mounted:function(){var e=this;this.onQuery(),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null}}}}]);