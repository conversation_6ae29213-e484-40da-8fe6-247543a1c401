(window.webpackJsonp=window.webpackJsonp||[]).push([[81],{"+ImP":function(e,t,r){},"0Nqk":function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var n=o(r("Q9c5")),a=o(r("DWNM"));o(r("XRYr"));function o(e){return e&&e.__esModule?e:{default:e}}t.default={name:"recommendList",mixins:[a.default],data:function(){return{rLoading:{},fileList:[],headers:{},uploadData:{},importDialogVisible:!1,warningData:[],tableHeight:100,dialogVisible:!1,dataList:[],params:{orderStatus:"",page:1,limit:10,records:0,orderNum:"",warning:"",status:""},orderData:{data:{},orderItem:[],hospital:{}}}},props:{},computed:{uploadUrl:function(){return n.default.baseContext+"/file/upload"}},watch:{},methods:{cancel:function(){this.importDialogVisible=!1,this.onQuery()},downloadTemplate:function(){var e=n.default.baseContext+"/file/downloadOrderImportTemplate",t=document.createElement("a");t.href=e,document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(e)},submitUpload:function(){var e=this;this.rLoading=this.openLoading("导入中,请耐心等待...");var t=this.uploadData.docId;this.$http_post(n.default.baseContext+"/supervise/supOrder/importCountryOrder?docId="+t,{},null,{timeout:1e5}).then(function(t){1==t.state?(e.onQuery(),e.rLoading.close(),e.importDialogVisible=!1,e.$alert("导入成功，请前往【订单批次日志】查看导入结果")):(e.onQuery(),e.rLoading.close(),e.importDialogVisible=!1,e.$message.error(t.message))})},onSuccess:function(e,t,r){if("1"!=e.state)return this.$refs.upload.clearFiles(),this.$alert(e.message),!1;t.docId=e.row.id,this.uploadData={docId:e.row.id,name:e.row.name}},onError:function(e,t,r){this.rLoading.close(),console.log(e),this.$message.error("上传失败,请重试")},handlePreview:function(e){var t=this.downloadUrl+"?docId="+encodeURI(encodeURI(e.docId))+"&fileName="+e.name;console.log(e);var r=document.createElement("a");r.href=t,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(t)},handleExceed:function(e,t){this.$message.warning("一次只能上传一个Excel文件")},beforeUpload:function(e){if(e.size/1024/1024>20)return this.$message.error("上传文件过大，请分开上传，单个文件最大为20M。"),!1;var t=e.name;return"xlsx"==t.substring(t.length-4)||"xls"==t.substring(t.length-3)||(this.$message.error("上传文件格式只能为【.xlsx】或【.xls】"),!1)},beforeRemove:function(e,t){return!this.beforeUpload(e)||this.$confirm("确定移除 "+e.name+"？")},handleRemove:function(e,t){return this.uploadData={},console.log(e.docId,e,t),!0},orderImport:function(){this.importDialogVisible=!0},getWaringType:function(e){for(var t=0;t<this.warningData.length;t++)if(this.warningData[t].value==e)return this.warningData[t].label},getWaring:function(e){for(var t=[],r=e.split(","),n=0;n<r.length;n++){var a=this.getWaringType(r[n]);a&&t.push({name:a})}return t},getDictItem:function(e){var t=this,r=this.openLoading();this.$http_post(n.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(n){var a=n.rows;1==n.state?("WARNING"==e&&(t.warningData=a),r.close()):(r.close(),t.$message.error(n.message))})},showOrder:function(e){this.$router.push({name:"recommendDetail",query:{orderId:e.id}})},onSearch:function(e){"reset"==e?(this.params.orderNum="",this.params.warning="",this.onQuery()):""!=this.params.orderNum||""!=this.params.warning?(this.params.page=1,this.onQuery()):this.$message.warning("请输入查询条件")},onPageClick:function(e){this.params.page=e,this.onQuery()},onQuery:function(){var e=this,t=this.params,r=this.openLoading(),a=n.default.baseContext+"/supervise/supRecommendOrder/list";this.$http_post(a,t).then(function(t){1==t.state?(e.dataList=t.rows,e.params.records=t.records,r.close()):(r.close(),e.$alert(t.message))})},time:function(e,t){if(void 0==e.creationTime||""==e.creationTime)return"";var r=new Date(e.creationTime);return r.getFullYear()+"-"+((r.getMonth()+1<10?"0"+(r.getMonth()+1):r.getMonth()+1)+"-")+(r.getDate()<10?"0"+r.getDate()+" ":r.getDate()+" ")+(r.getHours()<10?"0"+r.getHours()+":":r.getHours()+":")+(r.getMinutes()<10?"0"+r.getMinutes()+":":r.getMinutes()+":")+(r.getSeconds()<10?"0"+r.getSeconds():r.getSeconds())}},mounted:function(){var e=this;this.onQuery(),this.getDictItem("WARNING"),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ")+(t.getHours()<10?"0"+t.getHours()+":":t.getHours()+":")+(t.getMinutes()<10?"0"+t.getMinutes()+":":t.getMinutes()+":")+(t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds())}}}},"1oWj":function(e,t,r){"use strict";r("+ImP")},"5KGw":function(e,t,r){"use strict";var n=r("scQl");r.o(n,"render")&&r.d(t,"render",function(){return n.render}),r.o(n,"staticRenderFns")&&r.d(t,"staticRenderFns",function(){return n.staticRenderFns})},R0R3:function(e,t,r){"use strict";r.r(t);var n=r("5KGw"),a=r("YiWP");for(var o in a)["default"].indexOf(o)<0&&function(e){r.d(t,e,function(){return a[e]})}(o);r("1oWj");var i=r("gp09"),s=Object(i.a)(a.default,n.render,n.staticRenderFns,!1,null,"5db33d50",null);t.default=s.exports},YiWP:function(e,t,r){"use strict";r.r(t);var n=r("0Nqk"),a=r.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){r.d(t,e,function(){return n[e]})}(o);t.default=a.a},scQl:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"},[t("span",[e._v("订单号")]),t("el-input",{attrs:{placeholder:"请输入订单号"},model:{value:e.params.orderNum,callback:function(t){e.$set(e.params,"orderNum",t)},expression:"params.orderNum"}}),t("span",[e._v("预警状态")]),t("el-select",{attrs:{placeholder:"请选择状态"},model:{value:e.params.warning,callback:function(t){e.$set(e.params,"warning",t)},expression:"params.warning"}},[t("el-option",{key:"0",attrs:{label:"正常",value:"1"}}),t("el-option",{key:"1",attrs:{label:"异常",value:"2"}})],1),t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1)]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.dataList,"highlight-current-row":"",height:e.tableHeight}},[t("el-table-column",{attrs:{prop:"orderNum",label:"推荐号"}}),t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构"}}),t("el-table-column",{attrs:{prop:"totalPrice",label:"订单总金额(元)"}}),t("el-table-column",{attrs:{prop:"warning",label:"预警状态"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.warning&&"1"!=r.row.warning?e._e():t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("正常")])],1),"2"==r.row.warning?t("div",[t("el-popover",{attrs:{placement:"top-start",width:"250",trigger:"hover"}},[e._l(e.getWaring(r.row.warning),function(r,n){return t("div",{key:n,staticClass:"text item"},[e._v("\n                      "+e._s(r.name)+"\n                ")])}),t("el-tag",{attrs:{slot:"reference",type:"danger"},slot:"reference"},[e._v("查看预警")])],2)],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"creationTime",formatter:e.time,label:"创建时间"}}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showOrder(r.row)}}},[e._v("查看详情")])]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)])],1)},t.staticRenderFns=[]}}]);