(window.webpackJsonp=window.webpackJsonp||[]).push([[64],{"9JVQ":function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});n(a("XRYr"));var i=n(a("Q9c5")),o=n(a("DWNM")),s=n(a("cH4l"));function n(e){return e&&e.__esModule?e:{default:e}}t.default={name:"invoiceItemPay-list",mixins:[o.default],data:function(){return{pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=(new Date).format("yyyy-MM-dd HH:mm:ss"),a=new Date,i=new Date(a.getTime()-6048e5).format("yyyy-MM-dd HH:mm:ss");e.$emit("pick",[i,t])}},{text:"最近一个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd HH:mm:ss"),a=new Date,i=new Date(a.getTime()-2592e6).format("yyyy-MM-dd HH:mm:ss");e.$emit("pick",[i,t])}},{text:"最近三个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd HH:mm:ss"),a=new Date,i=new Date(a.getTime()-7776e6).format("yyyy-MM-dd HH:mm:ss");e.$emit("pick",[i,t])}}]},drugSourceList:[],multipleSelection:[],invoiceItemDataList:[],hospitalList:[],invoiceParams:{source:"",overFlag:"1",invoiceDate:"",page:1,limit:10,records:0,hospitalName:"",deliveryName:"",country:"1",catalogName:"",orderCode:"",deliveryCode:"",itemNo:""},show:!1,title:"医保基金支付",dialogVisible:!1,listLoading:!1,tableHeight:100,insuranceLog:{insuranceBankCard:"********",bankCard:"",bankName:"",phone:"",invoiceItemId:"",invoiceItemNo:"",amount:0,remark:""},confirmDataList:[],showBox:!1}},mounted:function(){var e=this;this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-150}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-150},this.getDictItem("SOURCE"),this.onInvoiceQuery(),this.getHospitalList()},computed:{uploadUrl:function(){return i.default.baseContext+"/file/upload"}},watch:{$route:{handler:function(){},deep:!0}},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+"")},rateFormat:function(e){return e=isNaN(e)||""===e?"--":Number(100*e).toFixed(1)+"%"}},updated:function(){this.toggleSelection(this.invoiceItemDataList)},methods:{getHospitalList:function(){var e=this,t=i.default.baseContext+"/supervise/supHospital/all";this.$http_post(t,{}).then(function(t){1==t.state?e.hospitalList=t.row:e.$alert(t.message)})},getDictItem:function(e){var t=this,a=this.openLoading();this.$http_post(i.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(i){var o=i.rows;1==i.state?("SOURCE"==e&&(t.drugSourceList=o),a.close()):(a.close(),t.$message.error(i.message))})},getDictItemName:function(e){for(var t=0;t<this.drugSourceList.length;t++)if(this.drugSourceList[t].value==e)return this.drugSourceList[t].label},confirmDeduction:function(){var e=this,t=this.openLoading("提交中...");this.$http_post(i.default.baseContext+"/supervise/supDeduction/save",this.confirmDataList,!0).then(function(a){1==a.state?(t.close(),e.orderBox=!1,e.$confirm("扣款单生成成功，前往【扣款单列表】查看详情",{confirmButtonText:"确定",showCancelButton:!1,type:"success"}).then(function(){e.$router.push({path:"deductionList"})}).catch(function(e){console.log(e)})):(t.close(),e.$alert(a.message))})},toggleSelection:function(e){var t=this;e?e.forEach(function(e){e.noticeNum>=3&&t.$refs.multipleTable.toggleRowSelection(e)}):this.$refs.multipleTable.clearSelection()},handleSelectionChange:function(e){this.multipleSelection=e},createDeduction:function(){var e=this;0==this.multipleSelection.length?this.$message({type:"error",message:"所选内容不能为空！"}):(this.confirmDataList=[],this.multipleSelection.forEach(function(t,a){var i=t.hospitalId,o=t.hospitalName,s=e.confirmDataList.findIndex(function(e){return e.hospitalId===i});if("-1"!=s)e.confirmDataList[s].itemData.push(t),e.confirmDataList[s].totalPrice=Number(e.confirmDataList[s].totalPrice)+Number(t.taxesAmount),e.confirmDataList[s].totalPrice=e.confirmDataList[s].totalPrice.toFixed(4);else{var n={hospitalName:o,hospitalId:i,itemData:[t],totalPrice:t.taxesAmount};e.confirmDataList.push(n)}}),this.showBox=!0)},close:function(){this.show=!1},saveInsuranceLog:function(){var e=this,t=this.openLoading(),a=i.default.baseContext+"/supervise/supInsuranceLog/save";this.$http_post(a,this.insuranceLog,!0).then(function(a){1==a.state?(e.$message({type:"success",message:"支付成功！"}),e.onInvoiceQuery(),e.toggleSelection(e.invoiceItemDataList),e.show=!1,t.close()):(t.close(),e.$alert(a.message))})},changeBankCard:function(){var e=s.default.getBankName(this.insuranceLog.bankCard);e&&(this.insuranceLog.bankName=e.bankName)},insurancePay:function(e){this.insuranceLog.amount=e.taxesAmount,this.insuranceLog.invoiceItemId=e.id,this.insuranceLog.invoiceItemNo=e.itemNo,this.show=!0},phoneNotice:function(e){var t=this,a=this.$options.filters.formatTime(e.orderCreationTime),o=e.hospitalName;this.$confirm("是否向"+o+"发出短信提醒？（该医疗机构需配置手机号）","短信提醒",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var s=t.openLoading(),n={busiId:e.id,hospitalId:e.hospitalId,hospitalName:o,orderCreationTime:a,sourceName:"深圳市药品交易平台",orderNum:e.orderCode,invoiceItemNo:e.itemNo,drugCatalogName:e.catalogName,noticeNum:null==e.noticeNum?1:e.noticeNum},l=i.default.baseContext+"/supervise/supOfficial/sendSmsTemplate/insurance_sms_notice";t.$http_post(l,n,!0).then(function(e){1==e.state?(t.$message({type:"success",message:"发送成功"}),t.onInvoiceQuery(),t.toggleSelection(t.invoiceItemDataList),s.close()):(s.close(),t.$alert(e.message))})})},officialNotice:function(e){var t=this,a=this.$options.filters.formatTime(e.orderCreationTime),o=e.hospitalName;this.$confirm("是否向"+o+"发出公文提醒？","公文通知",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var s=t.openLoading(),n={busiId:e.id,hospitalId:e.hospitalId,hospitalName:o,orderCreationTime:a,sourceName:e.source,orderNum:e.orderCode,invoiceItemNo:e.itemNo,drugCatalogName:e.catalogName,noticeNum:null==e.noticeNum?1:Number(e.noticeNum+1)},l=i.default.baseContext+"/supervise/supOfficial/sendTemplate/insurance_fund_notice";t.$http_post(l,n,!0).then(function(e){1==e.state?(t.$message({type:"success",message:"通知成功！"}),t.onInvoiceQuery(),t.toggleSelection(t.invoiceItemDataList),s.close()):(s.close(),t.$alert(e.message))})})},onInvoicePageClick:function(e){this.invoiceParams.page=e,this.onInvoiceQuery()},onInvoiceQuery:function(){var e=this,t=this.invoiceParams,a=this.openLoading(),o=i.default.baseContext+"/supervise/supInvoiceItem/getInvoiceItemList";this.$http_post(o,t).then(function(t){1==t.state?(e.invoiceItemDataList=t.rows,e.invoiceParams.records=t.records,a.close()):(a.close(),e.$alert(t.message))})},onInvoiceSearch:function(e){"reset"==e?(this.invoiceParams.deliveryName="",this.invoiceParams.hospitalName="",this.invoiceParams.invoiceDate="",this.invoiceParams.source="",this.onInvoiceQuery()):(this.invoiceParams.page=1,this.onInvoiceQuery())}}}},"DJw+":function(e,t,a){"use strict";a.r(t);var i=a("Inr0"),o=a("Tz3W");for(var s in o)["default"].indexOf(s)<0&&function(e){a.d(t,e,function(){return o[e]})}(s);a("wIV/");var n=a("gp09"),l=Object(n.a)(o.default,i.render,i.staticRenderFns,!1,null,"13720dc3",null);t.default=l.exports},Inr0:function(e,t,a){"use strict";var i=a("cGsn");a.o(i,"render")&&a.d(t,"render",function(){return i.render}),a.o(i,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return i.staticRenderFns})},Tz3W:function(e,t,a){"use strict";a.r(t);var i=a("9JVQ"),o=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(e){a.d(t,e,function(){return i[e]})}(s);t.default=o.a},cGsn:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.staticRenderFns=t.render=void 0;var i=function(e){return e&&e.__esModule?e:{default:e}}(a("/umX"));t.render=function(){var e,t=this,a=t._self._c;return a("div",{ref:"tableH",staticClass:"content"},[a("div",{staticClass:"search-header flex-row"},[a("div",{staticClass:"left"},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("通用名")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入通用名"},model:{value:t.invoiceParams.catalogName,callback:function(e){t.$set(t.invoiceParams,"catalogName",e)},expression:"invoiceParams.catalogName"}})],1)])]),"2"==t.$route.query.country&&(this.adminRole||this.medicalAdmin||this.socialAdmin)?a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("医疗机构")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:(e={size:"small",clearable:""},(0,i.default)(e,"clearable",""),(0,i.default)(e,"filterable",""),(0,i.default)(e,"placeholder","请选择医疗机构"),e),model:{value:t.invoiceParams.hospitalName,callback:function(e){t.$set(t.invoiceParams,"hospitalName",e)},expression:"invoiceParams.hospitalName"}},t._l(t.hospitalList,function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}),1)],1)])]):t._e(),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("采购平台")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择平台"},model:{value:t.invoiceParams.source,callback:function(e){t.$set(t.invoiceParams,"source",e)},expression:"invoiceParams.source"}},[a("el-option",{key:"1",attrs:{label:"深圳市药品交易平台",value:"1"}}),a("el-option",{key:"2",attrs:{label:"广东省药品交易平台",value:"2"}}),a("el-option",{key:"3",attrs:{label:"广州市药品交易平台",value:"3"}})],1)],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("是否集采")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择"},model:{value:t.invoiceParams.country,callback:function(e){t.$set(t.invoiceParams,"country",e)},expression:"invoiceParams.country"}},[a("el-option",{key:"1",attrs:{label:"是",value:"1"}}),a("el-option",{key:"2",attrs:{label:"否",value:"0"}})],1)],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("订单编号")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入订单编号"},model:{value:t.invoiceParams.orderCode,callback:function(e){t.$set(t.invoiceParams,"orderCode",e)},expression:"invoiceParams.orderCode"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("配送单号")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入配送单号"},model:{value:t.invoiceParams.deliveryCode,callback:function(e){t.$set(t.invoiceParams,"deliveryCode",e)},expression:"invoiceParams.deliveryCode"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("发票号")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入发票号"},model:{value:t.invoiceParams.itemNo,callback:function(e){t.$set(t.invoiceParams,"itemNo",e)},expression:"invoiceParams.itemNo"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("开票时间")]),a("div",{staticClass:"searchInput"},[a("el-date-picker",{attrs:{size:"small","value-format":"yyyy-MM-dd HH:mm:ss",type:"daterange","picker-options":t.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:t.invoiceParams.invoiceDate,callback:function(e){t.$set(t.invoiceParams,"invoiceDate",e)},expression:"invoiceParams.invoiceDate"}})],1)])])],1)],1),a("div",{staticClass:"right"},[a("div",{staticStyle:{"margin-bottom":"5px"}},[t.$route.query.type?t._e():a("el-button",{attrs:{size:"small",type:"warning"},on:{click:t.createDeduction}},[t._v("生成扣款单")])],1),a("div",[a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:t.onInvoiceSearch}},[t._v("查询")]),a("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(e){return t.onInvoiceSearch("reset")}}},[t._v("重置")])],1)])]),a("el-row",{staticClass:"con"},[a("div",{staticClass:"con-left"},[a("el-table",{ref:"multipleTable",staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{"row-key":function(e){return e.id},data:t.invoiceItemDataList,height:t.tableHeight,border:""},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{"reserve-selection":!0,type:"selection",width:"55"}}),a("el-table-column",{attrs:{prop:"code",label:"发票代码",width:"120"}}),a("el-table-column",{attrs:{prop:"itemNo",label:"发票号",width:"120"}}),a("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"180",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",[t._v("\n                    "+t._s(t.getDictItemName(e.row.source))+"\n                  ")])]}}])}),a("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单号",width:"120"}}),a("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"150"}}),a("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150"}}),a("el-table-column",{attrs:{prop:"num",label:"数量"}}),a("el-table-column",{attrs:{prop:"unitPrice",label:"单价"}}),a("el-table-column",{attrs:{prop:"taxesAmount",label:"金额"}}),a("el-table-column",{attrs:{prop:"orderCode",label:"订单编号",width:"120"}}),a("el-table-column",{attrs:{prop:"invoiceDate",label:"入库时间",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("formatTime")(e.row.stockInTime)))])]}}])}),a("el-table-column",{attrs:{prop:"country",label:"来源",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return["1"==e.row.country?a("div",[a("el-tag",{attrs:{type:"success"}},[t._v("国家集采")])],1):t._e(),"0"==e.row.country?a("div",[a("el-tag",{attrs:{type:"warning"}},[t._v("非国家集采")])],1):t._e(),"2"==e.row.country?a("div",[a("el-tag",{attrs:{type:"primary"}},[t._v("线下采购")])],1):t._e()]}}])}),a("el-table-column",{attrs:{prop:"payStatus",label:"回款状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return["1"==e.row.payStatus?a("div",[a("el-tag",{attrs:{type:"success"}},[t._v("已回款")])],1):t._e(),"2"==e.row.payStatus?a("div",[a("el-tag",{attrs:{type:"warning"}},[t._v("部分回款")])],1):t._e(),"0"!=e.row.payStatus&&e.row.payStatus?t._e():a("div",[a("el-tag",{attrs:{type:"danger"}},[t._v("未回款")])],1)]}}])}),a("el-table-column",{attrs:{label:"超时天数",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                        "+t._s(null==e.row.overDay?30:-e.row.overDay)+"\n                    ")]}}])}),a("el-table-column",{attrs:{label:"已通知次数",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                        "+t._s(null==e.row.noticeNum?0:e.row.noticeNum)+"\n                    ")]}}])}),t.$route.query.type?t._e():a("el-table-column",{attrs:{label:"操作",align:"center",width:"160",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.officialNotice(e.row)}}},[t._v("公文通知")]),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.phoneNotice(e.row)}}},[t._v("短信提醒")])]}}],null,!1,2141717331)})],1),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{total:t.invoiceParams.records,"page-size":t.invoiceParams.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onInvoicePageClick}})],1)],1)]),a("el-dialog",{attrs:{title:this.title,visible:t.show,width:"60%"},on:{close:t.close}},[a("div",{staticClass:"dialog-content"},[a("el-form",{ref:"insuranceLog",staticClass:"item-form",attrs:{id:"insuranceLog",model:t.insuranceLog,"label-width":"200px"}},[a("div",{staticClass:"fromBox"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"发票号"}},[a("el-input",{model:{value:t.insuranceLog.invoiceItemNo,callback:function(e){t.$set(t.insuranceLog,"invoiceItemNo",e)},expression:"insuranceLog.invoiceItemNo"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"支付金额"}},[a("el-input",{attrs:{readonly:!0},model:{value:t.insuranceLog.amount,callback:function(e){t.$set(t.insuranceLog,"amount",e)},expression:"insuranceLog.amount"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"医保基金账户"}},[a("el-input",{attrs:{readonly:!0},model:{value:t.insuranceLog.insuranceBankCard,callback:function(e){t.$set(t.insuranceLog,"insuranceBankCard",e)},expression:"insuranceLog.insuranceBankCard"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"配送单位银行账户"}},[a("el-input",{on:{change:t.changeBankCard},model:{value:t.insuranceLog.bankCard,callback:function(e){t.$set(t.insuranceLog,"bankCard",e)},expression:"insuranceLog.bankCard"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"配送单位银行名称"}},[a("el-input",{model:{value:t.insuranceLog.bankName,callback:function(e){t.$set(t.insuranceLog,"bankName",e)},expression:"insuranceLog.bankName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"配送单位短信接收号码"}},[a("el-input",{model:{value:t.insuranceLog.phone,callback:function(e){t.$set(t.insuranceLog,"phone",e)},expression:"insuranceLog.phone"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:t.insuranceLog.remark,callback:function(e){t.$set(t.insuranceLog,"remark",e)},expression:"insuranceLog.remark"}})],1)],1)],1)],1)])],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:t.close}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.saveInsuranceLog}},[t._v("确定")])],1)]),a("el-dialog",{attrs:{title:"生成扣款单",visible:t.showBox,width:"90%"},on:{close:function(e){t.showBox=!1}}},[a("div",{staticClass:"dialog-content"},[a("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{"show-summary":"","sum-text":"汇总",data:t.confirmDataList,"highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构"}}),a("el-table-column",{attrs:{prop:"totalPrice",label:"扣款金额(元)"}}),a("el-table-column",{attrs:{type:"expand",label:"查看金额构成情况",width:"200px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.row.itemData,"highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"itemNo",label:"发票号",width:"120"}}),a("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单号",width:"120"}}),a("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"150"}}),a("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150"}}),a("el-table-column",{attrs:{prop:"num",label:"数量"}}),a("el-table-column",{attrs:{prop:"taxesAmount",label:"金额"}}),a("el-table-column",{attrs:{prop:"orderCode",label:"订单编号",width:"120"}}),a("el-table-column",{attrs:{prop:"invoiceDate",label:"入库时间",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("formatTime")(e.row.stockInTime)))])]}}],null,!0)}),a("el-table-column",{attrs:{prop:"country",label:"来源",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return["1"==e.row.country?a("div",[a("el-tag",{attrs:{type:"success"}},[t._v("国家集采")])],1):t._e(),"0"==e.row.country?a("div",[a("el-tag",{attrs:{type:"warning"}},[t._v("非国家集采")])],1):t._e(),"2"==e.row.country?a("div",[a("el-tag",{attrs:{type:"primary"}},[t._v("线下采购")])],1):t._e()]}}],null,!0)}),a("el-table-column",{attrs:{prop:"payStatus",label:"回款状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return["1"==e.row.payStatus?a("div",[a("el-tag",{attrs:{type:"success"}},[t._v("已回款")])],1):t._e(),"2"==e.row.payStatus?a("div",[a("el-tag",{attrs:{type:"warning"}},[t._v("部分回款")])],1):t._e(),"0"!=e.row.payStatus&&e.row.payStatus?t._e():a("div",[a("el-tag",{attrs:{type:"danger"}},[t._v("未回款")])],1)]}}],null,!0)}),a("el-table-column",{attrs:{label:"超时天数",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                  "+t._s(null==e.row.overDay?30:-e.row.overDay)+"\n                ")]}}],null,!0)}),a("el-table-column",{attrs:{label:"已通知次数",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                  "+t._s(null==e.row.noticeNum?0:e.row.noticeNum)+"\n                ")]}}],null,!0)})],1)]}}])})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.showBox=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.confirmDeduction}},[t._v("确 定")])],1)])],1)},t.staticRenderFns=[]},idiE:function(e,t,a){},"wIV/":function(e,t,a){"use strict";a("idiE")}}]);