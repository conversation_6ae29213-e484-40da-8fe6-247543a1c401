(window.webpackJsonp=window.webpackJsonp||[]).push([[28],{"6Ns9":function(t,e,o){t.exports=o.p+"images/logo-2.cb4e3eb8.png"},"8q9/":function(t,e,o){"use strict";o("NSPs")},GNbS:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this._self._c;return t("div",{staticClass:"s-canvas"},[t("canvas",{attrs:{id:"s-canvas",width:this.contentWidth,height:this.contentHeight}})])},e.staticRenderFns=[]},GfOs:function(t,e,o){"use strict";o.r(e);var r=o("uIWu"),i=o("LWqa");for(var n in i)["default"].indexOf(n)<0&&function(t){o.d(e,t,function(){return i[t]})}(n);o("8q9/");var s=o("gp09"),a=Object(s.a)(i.default,r.render,r.staticRenderFns,!1,null,"b8fac1de",null);e.default=a.exports},LWqa:function(t,e,o){"use strict";o.r(e);var r=o("vidi"),i=o.n(r);for(var n in r)["default"].indexOf(n)<0&&function(t){o.d(e,t,function(){return r[t]})}(n);e.default=i.a},NSPs:function(t,e,o){},OrRo:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default={name:"SIdentify",props:{identifyCode:{type:String,default:"1234"},fontSizeMin:{type:Number,default:25},fontSizeMax:{type:Number,default:30},backgroundColorMin:{type:Number,default:255},backgroundColorMax:{type:Number,default:255},colorMin:{type:Number,default:0},colorMax:{type:Number,default:160},lineColorMin:{type:Number,default:100},lineColorMax:{type:Number,default:255},dotColorMin:{type:Number,default:0},dotColorMax:{type:Number,default:255},contentWidth:{type:Number,default:112},contentHeight:{type:Number,default:31}},methods:{randomNum:function(t,e){return Math.floor(Math.random()*(e-t)+t)},randomColor:function(t,e){return"rgb("+this.randomNum(t,e)+","+this.randomNum(t,e)+","+this.randomNum(t,e)+")"},drawPic:function(){var t=document.getElementById("s-canvas").getContext("2d");t.textBaseline="bottom",t.fillStyle=this.randomColor(this.backgroundColorMin,this.backgroundColorMax),t.fillRect(0,0,this.contentWidth,this.contentHeight);for(var e=0;e<this.identifyCode.length;e++)this.drawText(t,this.identifyCode[e],e);this.drawLine(t),this.drawDot(t)},drawText:function(t,e,o){t.fillStyle=this.randomColor(this.colorMin,this.colorMax),t.font=this.randomNum(this.fontSizeMin,this.fontSizeMax)+"px SimHei";var r=(o+1)*(this.contentWidth/(this.identifyCode.length+1)),i=this.randomNum(this.fontSizeMax,this.contentHeight-5),n=this.randomNum(-45,45);t.translate(r,i),t.rotate(n*Math.PI/180),t.fillText(e,0,0),t.rotate(-n*Math.PI/180),t.translate(-r,-i)},drawLine:function(t){for(var e=0;e<5;e++)t.strokeStyle=this.randomColor(this.lineColorMin,this.lineColorMax),t.beginPath(),t.moveTo(this.randomNum(0,this.contentWidth),this.randomNum(0,this.contentHeight)),t.lineTo(this.randomNum(0,this.contentWidth),this.randomNum(0,this.contentHeight)),t.stroke()},drawDot:function(t){for(var e=0;e<80;e++)t.fillStyle=this.randomColor(0,255),t.beginPath(),t.arc(this.randomNum(0,this.contentWidth),this.randomNum(0,this.contentHeight),1,0,2*Math.PI),t.fill()}},watch:{identifyCode:function(){this.drawPic()}},mounted:function(){this.drawPic()}}},XtEp:function(t,e,o){"use strict";var r=o("GNbS");o.o(r,"render")&&o.d(e,"render",function(){return r.render}),o.o(r,"staticRenderFns")&&o.d(e,"staticRenderFns",function(){return r.staticRenderFns})},eHVj:function(t,e,o){},mRtE:function(t,e,o){"use strict";o("eHVj")},t8bM:function(t,e,o){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{staticClass:"login flex-row"},[e("div",{staticClass:"left"}),e("div",{staticClass:"right flex-column"},[e("div",{staticClass:"title flex-row"},[e("img",{staticClass:"logo",attrs:{src:o("6Ns9")}}),e("h2",[t._v(t._s(t.title))])]),e("div",{staticClass:"login-content"},[e("h2",[t._v("欢迎登陆")]),e("div",[e("div",{staticClass:"login-box flex-column"},[e("span",[t._v("账号")]),e("div",{staticClass:"item flex-row"},[e("input",{ref:"username",staticClass:"el-input el-input__inner",attrs:{type:"text",placeholder:"请输入用户名"}})]),e("span",[t._v("密码")]),e("div",{staticClass:"item flex-row"},[e("input",{ref:"password",staticClass:"el-input el-input__inner",attrs:{type:"password",id:"pwd",placeholder:"请输入密码"},on:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.onLogin.apply(null,arguments)}}})]),e("div",{staticClass:"form-group",staticStyle:{display:"flex"}},[e("div",[e("span",[t._v("验证码：")]),e("input",{directives:[{name:"model",rawName:"v-model",value:t.code,expression:"code"}],staticClass:"el-input el-input__inner",attrs:{type:"text",id:"code",placeholder:"请输入您的验证码"},domProps:{value:t.code},on:{input:function(e){e.target.composing||(t.code=e.target.value)}}})]),e("div",{staticClass:"login-code",on:{click:t.refreshCode}},[e("s-identify",{attrs:{identifyCode:t.identifyCode}})],1)]),e("div",{staticClass:"login-button flex-row"},[e("button",{on:{click:t.onLogin}},[t._v("登   录")])])])])])]),e("el-dialog",{staticClass:"modify-pwd",attrs:{title:"修改密码",visible:t.modifyPwd,"close-on-click-modal":!1,"close-on-press-escape":!1,width:"35%"},on:{"update:visible":function(e){t.modifyPwd=e}}},[e("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:t.ruleForm,"label-position":"left",rules:t.rules,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"旧密码",prop:"oldPwd"}},[e("el-input",{attrs:{type:"password"},model:{value:t.ruleForm.oldPwd,callback:function(e){t.$set(t.ruleForm,"oldPwd",e)},expression:"ruleForm.oldPwd"}})],1),e("el-form-item",{attrs:{label:"新密码",prop:"pass"}},[e("el-input",{attrs:{type:"password",autocomplete:"off",placeholder:"密码中必须包含12-30位的字母及数字"},model:{value:t.ruleForm.pass,callback:function(e){t.$set(t.ruleForm,"pass",e)},expression:"ruleForm.pass"}})],1),e("el-form-item",{attrs:{label:"确认密码",prop:"checkPass"}},[e("el-input",{attrs:{type:"password",autocomplete:"off",placeholder:"密码中必须包含12-30位的字母及数字"},model:{value:t.ruleForm.checkPass,callback:function(e){t.$set(t.ruleForm,"checkPass",e)},expression:"ruleForm.checkPass"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.modifyPwd=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.confirmModify}},[t._v("确 定")])],1)],1)],1)},e.staticRenderFns=[]},uIWu:function(t,e,o){"use strict";var r=o("t8bM");o.o(r,"render")&&o.d(e,"render",function(){return r.render}),o.o(r,"staticRenderFns")&&o.d(e,"staticRenderFns",function(){return r.staticRenderFns})},v7KA:function(t,e,o){"use strict";o.r(e);var r=o("XtEp"),i=o("vzqx");for(var n in i)["default"].indexOf(n)<0&&function(t){o.d(e,t,function(){return i[t]})}(n);o("mRtE");var s=o("gp09"),a=Object(s.a)(i.default,r.render,r.staticRenderFns,!1,null,"668ee6d8",null);e.default=a.exports},vidi:function(t,e,o){Object.defineProperty(e,"__esModule",{value:!0});var r=a(o("DWNM")),i=a(o("Q9c5")),n=a(o("6Cps"));o("j5jW");var s=a(o("v7KA"));function a(t){return t&&t.__esModule?t:{default:t}}var l=o("LalF").Base64;e.default={mixins:[r.default],components:{SIdentify:s.default},data:function(){var t=this;return{identifyCodes:"1234567890",identifyCode:"",code:"",title:"",version:"1.0.0",modifyPwd:!1,ruleForm:{oldPwd:"",pass:"",checkPass:""},user:{},rules:{oldPwd:[{required:!0,message:"请输入原密码",trigger:"blur"}],pass:[{required:!0,validator:function(e,o,r){""===o?r(new Error("请输入新密码")):(new RegExp("(?=.*[0-9])(?=.*[a-zA-Z]).{12,30}").test(o)||r(new Error("您的密码复杂度太低（密码中必须包含12-30位的字母及数字）")),""!==t.ruleForm.checkPass&&t.$refs.ruleForm.validateField("checkPass"),r())},trigger:"blur"}],checkPass:[{required:!0,validator:function(e,o,r){""===o?r(new Error("请再次输入密码")):o!==t.ruleForm.pass?r(new Error("两次输入密码不一致!")):r()},trigger:"blur"}]}}},computed:{},methods:{randomNum:function(t,e){return Math.floor(Math.random()*(e-t)+t)},refreshCode:function(){this.identifyCode="",this.makeCode(this.identifyCodes,4)},makeCode:function(t,e){for(var o=0;o<e;o++)this.identifyCode+=this.identifyCodes[this.randomNum(0,this.identifyCodes.length)];console.log(this.identifyCode)},onLogin:function(){var t=this;if(""!=this.code){if(this.identifyCode!==this.code)return this.code="",this.refreshCode(),void alert("请输入正确的验证码");var e=this.$refs.username.value,o=this.$refs.password.value;return null==e||0==e.length?(alert("请输入用户名"),void this.$refs.username.focus()):null==o||0==o.length?(alert("请输入密码"),void this.$refs.password.focus()):void this.login(e,o,function(e){if(1==e.state){t.$store.dispatch("setCookieToken",e.row.jwtToken);var o=new Date;o.setTime(o.getTime()+864e5),document.cookie="ewtk="+e.message+";path=/;expires="+o.toGMTString();var r=t.$route.query.redirect;r=t.isNotNull(r)?l.decode(r):"/",0==e.row.user.attrMap.resetFlag?(t.user=e.row.user,t.modifyPwd=!0):(t.$router.push(r),t.$http_post(i.default.baseContext+"/supervise/Operation/userloginLog").then(function(t){}).catch(function(t){console.log(t)}))}else alert(e.message)})}alert("请输入验证码")},confirmModify:function(){var t=this;this.$refs.ruleForm.validate(function(e){if(!e)return!1;var o=t.user;if(null!=o){var r={id:o.id,oldPassword:n.default.MD5(t.ruleForm.oldPwd+"").toString(),newPassword:n.default.MD5(t.ruleForm.pass+"").toString()},s=t.openLoading("修改中...");t.$http_post(i.default.baseContext+"/bsp/pubUser/updatePassword",r).then(function(e){if(1==e.state){t.$message.success("修改成功"),t.modifyPwd=!1;var o=t.$route.query.redirect;o=t.isNotNull(o)?l.decode(o):"/",t.$router.push("/"),t.$http_post(i.default.baseContext+"/supervise/Operation/userloginLog").then(function(t){}).catch(function(t){console.log(t)})}else null!=e.message?t.$message.error(e.message):t.$message.error("系统异常");s.close()}).catch(function(t){s.close(),console.log(t)})}})}},mounted:function(){this.title=i.default.title,this.version=i.default.version;var t=this.$store.getters.curUser,e=this.$route.query.redirect;e=this.isNotNull(e)?l.decode(e):"/",this.isEmptyObject(t)||this.$router.push(e)},created:function(){this.refreshCode()}}},vzqx:function(t,e,o){"use strict";o.r(e);var r=o("OrRo"),i=o.n(r);for(var n in r)["default"].indexOf(n)<0&&function(t){o.d(e,t,function(){return r[t]})}(n);e.default=i.a}}]);