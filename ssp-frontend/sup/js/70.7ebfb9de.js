(window.webpackJsonp=window.webpackJsonp||[]).push([[70],{"3fBT":function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",[e("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[e("el-tab-pane",{attrs:{label:"月",name:"first"}}),e("el-tab-pane",{attrs:{label:"季度",name:"second"}}),e("el-tab-pane",{attrs:{label:"年",name:"third"}})],1),e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left"},[e("el-row",{attrs:{gutter:10}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("医疗机构")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:t.itemParams.hospitalId,callback:function(e){t.$set(t.itemParams,"hospitalId",e)},expression:"itemParams.hospitalId"}},t._l(t.hospitalList,function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}),1)],1)])]),"first"!=t.activeName?e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("年份")]),e("div",{staticClass:"searchInput"},["first"!=t.activeName?e("el-date-picker",{attrs:{size:"small","value-format":"yyyy",type:"year",placeholder:"选择年"},model:{value:t.itemParams.year,callback:function(e){t.$set(t.itemParams,"year",e)},expression:"itemParams.year"}}):t._e()],1)])]):t._e(),"second"==t.activeName?e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("季度")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",clearable:"",placeholder:"全部"},model:{value:t.itemParams.quarter,callback:function(e){t.$set(t.itemParams,"quarter",e)},expression:"itemParams.quarter"}},[e("el-option",{key:"1",attrs:{label:"第一季度",value:"1"}}),e("el-option",{key:"2",attrs:{label:"第二季度",value:"2"}}),e("el-option",{key:"3",attrs:{label:"第三季度",value:"3"}}),e("el-option",{key:"4",attrs:{label:"第四季度",value:"4"}})],1)],1)])]):t._e(),"first"==t.activeName?e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("月份")]),e("div",{staticClass:"searchInput"},["first"==t.activeName?e("el-date-picker",{attrs:{size:"small","value-format":"yyyy-MM",type:"month",placeholder:"选择年月"},model:{value:t.itemParams.nowMonth,callback:function(e){t.$set(t.itemParams,"nowMonth",e)},expression:"itemParams.nowMonth"}}):t._e()],1)])]):t._e(),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("通用名")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入通用名"},model:{value:t.itemParams.catalogName,callback:function(e){t.$set(t.itemParams,"catalogName",e)},expression:"itemParams.catalogName"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("同比异常")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",clearable:"",placeholder:"全部"},model:{value:t.itemParams.grewException,callback:function(e){t.$set(t.itemParams,"grewException",e)},expression:"itemParams.grewException"}},[e("el-option",{key:"2",attrs:{label:"异常",value:"1"}}),e("el-option",{key:"3",attrs:{label:"正常",value:"0"}})],1)],1)])]),"third"!=t.activeName?e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("环比异常")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",clearable:"",placeholder:"全部"},model:{value:t.itemParams.chainException,callback:function(e){t.$set(t.itemParams,"chainException",e)},expression:"itemParams.chainException"}},[e("el-option",{key:"2",attrs:{label:"异常",value:"1"}}),e("el-option",{key:"3",attrs:{label:"正常",value:"0"}})],1)],1)])]):t._e(),e("el-col",{attrs:{span:17}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("同比异常百分比")]),e("div",{staticClass:"searchInput"},[e("el-input-number",{staticStyle:{width:"calc(100%/3 - 55px)"},attrs:{size:"small",min:0,max:100,label:"异常浮动百分比"},model:{value:t.itemParams.grewExceptionRate,callback:function(e){t.$set(t.itemParams,"grewExceptionRate",e)},expression:"itemParams.grewExceptionRate"}}),e("span",{staticStyle:{"margin-left":"5px",color:"#ff0000"}},[t._v('注：同比异常百分比为同比上升或下降比例。如设置为50，则表示"同比上升/下降了50%"')])],1)])]),"third"!=t.activeName?e("el-col",{attrs:{span:17}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("环比异常百分比")]),e("div",{staticClass:"searchInput"},[e("el-input-number",{staticStyle:{width:"calc(100%/3 - 55px)"},attrs:{size:"small",min:0,max:100,label:"异常浮动百分比"},model:{value:t.itemParams.chainExceptionRate,callback:function(e){t.$set(t.itemParams,"chainExceptionRate",e)},expression:"itemParams.chainExceptionRate"}})],1)])]):t._e()],1)],1),e("div",{staticClass:"right"},[e("div",[e("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1)])]),"first"==t.activeName?e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{key:Math.random(),staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.amountBarData,height:t.tableHeight,border:""},on:{"sort-change":t.sortChange}},[e("el-table-column",{attrs:{prop:"catalogName",width:"180",label:"通用名",align:"left","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"dosageForm",width:"100",label:"剂型","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"specs",width:"100","show-tooltip-when-overflow":"",label:"规格"}}),e("el-table-column",{attrs:{prop:"packingSpecs",width:"100","show-tooltip-when-overflow":"",label:"包装规格"}}),e("el-table-column",{attrs:{prop:"nowYear",align:"left",label:"统计年份",width:"100","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"nowTime",align:"left",label:"统计月份",width:"100","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"thisDrugItemPrice",align:"left",label:"当月采购金额(元)",width:"180","show-tooltip-when-overflow":"",sortable:"custom"}}),e("el-table-column",{attrs:{prop:"thisDrugSum",align:"left",width:"120","show-tooltip-when-overflow":"",label:"当月采购量"}}),e("el-table-column",{attrs:{prop:"lastMonthDrugSum",align:"left",width:"120","show-tooltip-when-overflow":"",label:"上月采购量"}}),e("el-table-column",{attrs:{prop:"lastYearDrugSum",align:"left",width:"150","show-tooltip-when-overflow":"",label:"去年同月采购量"}}),e("el-table-column",{attrs:{prop:"monthRatio",align:"left",width:"100",label:"环比增长","show-tooltip-when-overflow":"",sortable:"custom"},scopedSlots:t._u([{key:"default",fn:function(a){return["0"==a.row.monthRatio?e("el-tag",{attrs:{type:"warning"}},[t._v("\n                /\n              ")]):a.row.grewException?e("el-tag",{attrs:{type:"danger"}},[t._v("\n                "+t._s(a.row.monthRatio)+"\n              ")]):e("el-tag",{attrs:{type:"success"}},[t._v("\n                "+t._s(a.row.monthRatio)+"\n              ")])]}}],null,!1,530337673)}),e("el-table-column",{attrs:{prop:"chainException",align:"left","show-tooltip-when-overflow":"",label:"环比异常"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.chainException?e("el-tag",{attrs:{type:"danger"}},[t._v("\n                是\n              ")]):e("el-tag",{attrs:{type:"success"}},[t._v("\n                否\n              ")])]}}],null,!1,584294822)}),e("el-table-column",{attrs:{"show-tooltip-when-overflow":"",sortable:"custom",align:"left",width:"100",prop:"yearRatio",label:"同比增长"},scopedSlots:t._u([{key:"default",fn:function(a){return["0"==a.row.yearRatio?e("el-tag",{attrs:{type:"warning"}},[t._v("\n               /\n              ")]):a.row.chainException?e("el-tag",{attrs:{type:"danger"}},[t._v("\n                "+t._s(a.row.yearRatio)+"\n              ")]):e("el-tag",{attrs:{type:"success"}},[t._v("\n                "+t._s(a.row.yearRatio)+"\n              ")])]}}],null,!1,2200532188)}),e("el-table-column",{attrs:{prop:"grewException",align:"center",label:"同比异常"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.grewException?e("el-tag",{attrs:{type:"danger"}},[t._v("\n                是\n              ")]):e("el-tag",{attrs:{type:"success"}},[t._v("\n                否\n              ")])]}}],null,!1,1704548844)}),e("el-table-column",{attrs:{label:"操作",align:"right",fixed:"right",width:"200"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.add("show",a.row)}}},[t._v("查看详情")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showAmount(a.row.detailId,"month")}}},[t._v("数量趋势")])]}}],null,!1,3774220733)})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.itemParams.records,"page-size":t.itemParams.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)]):t._e(),"second"==t.activeName?e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{key:Math.random(),staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.amountBarDataByQuarter,height:t.tableHeight,border:""},on:{"sort-change":t.sortChange}},[e("el-table-column",{attrs:{prop:"catalogName",width:"180",label:"通用名",align:"left","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"dosageForm",width:"100",label:"剂型",align:"left","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"specs",width:"100",align:"left","show-tooltip-when-overflow":"",label:"规格"}}),e("el-table-column",{attrs:{prop:"packingSpecs",width:"100",align:"left","show-tooltip-when-overflow":"",label:"包装规格"}}),e("el-table-column",{attrs:{prop:"nowYear",label:"统计年份",align:"left",width:"100"}}),e("el-table-column",{attrs:{width:"100",align:"left",prop:"aQuarter",label:"统计季度"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            第 "+t._s(e.row.aQuarter)+" 季度\n          ")]}}],null,!1,1166169345)}),e("el-table-column",{attrs:{prop:"thisDrugItemPrice",sortable:"custom",align:"left",width:"180",label:"当季采购金额(元)"}}),e("el-table-column",{attrs:{width:"120",prop:"thisDrugSum",label:"当前季度采购量"}}),e("el-table-column",{attrs:{width:"120",prop:"lastDrugSum",label:"上季度采购量"}}),e("el-table-column",{attrs:{prop:"lastYearDrugSum",width:"130",label:"去年同季采购量"}}),e("el-table-column",{attrs:{width:"120",sortable:"custom",prop:"lastQuarterRatio",label:"环比增长"},scopedSlots:t._u([{key:"default",fn:function(a){return["0"==a.row.lastQuarterRatio?e("el-tag",{attrs:{type:"warning"}},[t._v("\n              /\n            ")]):a.row.grewException?e("el-tag",{attrs:{type:"danger"}},[t._v("\n              "+t._s(a.row.lastQuarterRatio)+"\n            ")]):e("el-tag",{attrs:{type:"success"}},[t._v("\n              "+t._s(a.row.lastQuarterRatio)+"\n            ")])]}}],null,!1,4115586023)}),e("el-table-column",{attrs:{prop:"chainException",align:"center",label:"环比异常"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.chainException?e("el-tag",{attrs:{type:"danger"}},[t._v("\n              是\n            ")]):e("el-tag",{attrs:{type:"success"}},[t._v("\n              否\n            ")])]}}],null,!1,65740710)}),e("el-table-column",{attrs:{width:"120",sortable:"custom",prop:"lastYearRatio",label:"同比增长"},scopedSlots:t._u([{key:"default",fn:function(a){return["0"==a.row.lastYearRatio?e("el-tag",{attrs:{type:"warning"}},[t._v("\n              /\n            ")]):a.row.chainException?e("el-tag",{attrs:{type:"danger"}},[t._v("\n              "+t._s(a.row.lastYearRatio)+"\n            ")]):e("el-tag",{attrs:{type:"success"}},[t._v("\n              "+t._s(a.row.lastYearRatio)+"\n            ")])]}}],null,!1,663066486)}),e("el-table-column",{attrs:{prop:"grewException",align:"center",label:"同比异常"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.grewException?e("el-tag",{attrs:{type:"danger"}},[t._v("\n              是\n            ")]):e("el-tag",{attrs:{type:"success"}},[t._v("\n              否\n            ")])]}}],null,!1,2730039276)}),e("el-table-column",{attrs:{label:"操作",align:"right",width:"200",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.add("show",a.row)}}},[t._v("查看详情")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showAmount(a.row.detailId,"quarter")}}},[t._v("数量趋势")])]}}],null,!1,2239397465)})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.itemParams.records,"page-size":t.itemParams.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)]):t._e(),"third"==t.activeName?e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{key:Math.random(),staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{height:t.tableHeight,data:t.amountBarDataByQuarter,border:""},on:{"sort-change":t.sortChange}},[e("el-table-column",{attrs:{prop:"catalogName",width:"180",label:"通用名",align:"left"}}),e("el-table-column",{attrs:{prop:"dosageForm",width:"150",label:"剂型","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"specs",width:"100","show-tooltip-when-overflow":"",label:"规格"}}),e("el-table-column",{attrs:{prop:"packingSpecs","show-tooltip-when-overflow":"",label:"包装规格"}}),e("el-table-column",{attrs:{prop:"nowYear",label:"统计年份",width:"180"}}),e("el-table-column",{attrs:{prop:"thisDrugItemPrice",sortable:"custom",width:"180",align:"center",label:"今年采购金额(元)"}}),e("el-table-column",{attrs:{prop:"thisDrugSum",label:"今年采购量"}}),e("el-table-column",{attrs:{prop:"lastYearDrugSum",label:"去年采购量"}}),e("el-table-column",{attrs:{sortable:"custom",prop:"yearRatio",label:"同比增长"},scopedSlots:t._u([{key:"default",fn:function(a){return["0"==a.row.yearRatio?e("el-tag",{attrs:{type:"warning"}},[t._v("\n              /\n            ")]):"1"==a.row.grewException?e("el-tag",{attrs:{type:"danger"}},[t._v("\n              "+t._s(a.row.yearRatio)+"\n            ")]):e("el-tag",{attrs:{type:"success"}},[t._v("\n              "+t._s(a.row.yearRatio)+"\n            ")])]}}],null,!1,1374295687)}),e("el-table-column",{attrs:{prop:"grewException",align:"center",label:"同比异常"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.grewException?e("el-tag",{attrs:{type:"danger"}},[t._v("\n              是\n            ")]):e("el-tag",{attrs:{type:"success"}},[t._v("\n              否\n            ")])]}}],null,!1,2730039276)}),e("el-table-column",{attrs:{label:"操作",align:"right",width:"200",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.add("show",a.row)}}},[t._v("查看详情")])]}}],null,!1,1355062622)})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.itemParams.records,"page-size":t.itemParams.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)]):t._e(),e("el-dialog",{attrs:{title:"药品详情",visible:t.dialogVisible,width:"45%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("div",{staticClass:"dialog-content"},[e("el-form",{ref:"form",staticClass:"item-form",attrs:{model:t.formData,"label-width":"110px"}},[e("span",{staticStyle:{color:"red"}},[t._v("提示：当药品本位码与药品规格及包装规格都相同时则视为同一种药！")]),e("div",{staticClass:"fromBox"},[e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"通用名",prop:"catalogId"}},[e("el-select",{attrs:{filterable:"",disabled:t.isSetPrice},model:{value:t.formData.catalogId,callback:function(e){t.$set(t.formData,"catalogId",e)},expression:"formData.catalogId"}},t._l(t.drugCatalogList,function(a){return e("el-option",{key:a.id,attrs:{label:a.name,value:a.id}},[e("span",{staticStyle:{float:"left"}},[t._v(t._s(a.name))]),e("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v(t._s(a.category))])])}),1)],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"商品名",prop:"goodsName"}},[e("el-input",{attrs:{readonly:t.isSetPrice},model:{value:t.formData.goodsName,callback:function(e){t.$set(t.formData,"goodsName",e)},expression:"formData.goodsName"}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"剂型",prop:"dosageForm"}},[e("el-input",{attrs:{readonly:t.isSetPrice},model:{value:t.formData.dosageForm,callback:function(e){t.$set(t.formData,"dosageForm",e)},expression:"formData.dosageForm"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"规格",prop:"specs"}},[e("el-input",{attrs:{readonly:t.isSetPrice},model:{value:t.formData.specs,callback:function(e){t.$set(t.formData,"specs",e)},expression:"formData.specs"}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"包装规格",prop:"packingSpecs"}},[e("el-input",{attrs:{readonly:t.isSetPrice},model:{value:t.formData.packingSpecs,callback:function(e){t.$set(t.formData,"packingSpecs",e)},expression:"formData.packingSpecs"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"单位",prop:"unit"}},[e("el-select",{attrs:{placeholder:"请选择药品单位",disabled:t.isSetPrice},model:{value:t.formData.unit,callback:function(e){t.$set(t.formData,"unit",e)},expression:"formData.unit"}},t._l(t.unitList,function(t){return e("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})}),1)],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"药本位码",prop:"standardCode"}},[e("el-input",{attrs:{readonly:t.isSetPrice},model:{value:t.formData.standardCode,callback:function(e){t.$set(t.formData,"standardCode",e)},expression:"formData.standardCode"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"药品类别",prop:"category"}},[e("el-select",{attrs:{disabled:t.isSetPrice},model:{value:t.formData.category,callback:function(e){t.$set(t.formData,"category",e)},expression:"formData.category"}},t._l(t.categoryList,function(t){return e("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})}),1)],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"医保目录",prop:"medicalInsurance"}},[e("el-select",{attrs:{disabled:t.isSetPrice},model:{value:t.formData.medicalInsurance,callback:function(e){t.$set(t.formData,"medicalInsurance",e)},expression:"formData.medicalInsurance"}},[e("el-option",{attrs:{label:"甲类",value:"1"}}),e("el-option",{attrs:{label:"乙类",value:"2"}})],1)],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"药品类型",prop:"country"}},[e("el-select",{attrs:{disabled:!0},model:{value:t.formData.country,callback:function(e){t.$set(t.formData,"country",e)},expression:"formData.country"}},[e("el-option",{attrs:{label:"国家集采",value:"1"}}),e("el-option",{attrs:{label:"非国家集采",value:"0"}}),e("el-option",{attrs:{label:"医院线上药品",value:"3"}}),e("el-option",{attrs:{label:"医院线下药品",value:"2"}})],1)],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"生产企业",prop:"company"}},[e("el-input",{attrs:{readonly:t.isSetPrice},model:{value:t.formData.drugCompanyName,callback:function(e){t.$set(t.formData,"drugCompanyName",e)},expression:"formData.drugCompanyName"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"批准文号",prop:"approvalNumber"}},[e("el-input",{attrs:{readonly:t.isSetPrice},model:{value:t.formData.approvalNumber,callback:function(e){t.$set(t.formData,"approvalNumber",e)},expression:"formData.approvalNumber"}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:"1"==t.formData.country?12:24}},[e("el-form-item",{staticClass:"col-left",attrs:{label:"状态",prop:"status"}},[e("el-switch",{attrs:{"active-value":"1","inactive-value":"0",disabled:t.isSetPrice},model:{value:t.formData.status,callback:function(e){t.$set(t.formData,"status",e)},expression:"formData.status"}})],1)],1),"1"==t.formData.country?e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"药品批次",prop:"countryType"}},[e("el-select",{model:{value:t.formData.countryType,callback:function(e){t.$set(t.formData,"countryType",e)},expression:"formData.countryType"}},t._l(t.countryTypeList,function(t){return e("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})}),1)],1)],1):t._e()],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{staticClass:"col-left",attrs:{label:"备注",prop:"remark"}},[e("el-input",{attrs:{type:"textarea",readonly:t.isSetPrice},model:{value:t.formData.remark,callback:function(e){t.$set(t.formData,"remark",e)},expression:"formData.remark"}})],1)],1)],1)],1)])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("关 闭")])],1)])],1),e("el-dialog",{attrs:{visible:t.chartShow,width:"45%"},on:{"update:visible":function(e){t.chartShow=e}}},[e(t.currentComponent,{ref:"asyncDialog",tag:"component",attrs:{detailId:t.detailId,type:t.type,hospitalId:t.hospitalId},on:{close:t.close}}),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.chartShow=!1}}},[t._v("关 闭")])],1)],1)],1)},e.staticRenderFns=[]},CsHR:function(t,e,a){"use strict";a.r(e);var l=a("Ki91"),o=a.n(l);for(var r in l)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return l[t]})}(r);e.default=o.a},JPmy:function(t,e,a){"use strict";a("xWXB")},Ki91:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var l=r(a("Q9c5")),o=r(a("DWNM"));r(a("XRYr"));function r(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[o.default],data:function(){return{detailId:"",hospitalId:"",activeName:"first",countryTypeList:[{name:"第一批集采",value:"1"},{name:"第二批集采",value:"6"}],itemParams:{quarter:Math.floor(((new Date).getMonth()+3)/3).toString(),year:(new Date).format("yyyy"),dimension:"month",hospitalId:"",catalogName:"",page:1,limit:10,records:0,nowMonth:(new Date).format("yyyy-MM"),chainExceptionRate:50,grewExceptionRate:50,grewException:"1",chainException:"1",sortProp:"",sortOrder:""},currentComponent:"drugAmount",isSetPrice:!0,drugCatalogList:[],formData:{code:"",drugCompanyName:"",category:"",id:"",source:"",goodsName:"",catalogName:"",catalogId:"",country:"0",countryType:"",electionScope:"",dosageForm:"",specs:"",packingSpecs:"",packing:"",unit:"",qualityLevel:"",company:"",approvalNumber:"",standardCode:"",medicalInsurance:"",attribute:"",adjuvant:"0",net:"0",coefficient:"",remark:"",version:"",status:""},unitList:[{name:"盒",value:"盒"},{name:"支",value:"支"},{name:"片",value:"片"},{name:"丸",value:"丸"},{name:"粒",value:"粒"},{name:"袋",value:"袋"},{name:"瓶",value:"瓶"},{name:"剂",value:"剂"}],categoryList:[{name:"中药",value:"1"},{name:"西药",value:"2"}],drugSourceList:[],dialogVisible:!1,tableHeight:600,rLoading:{},hospitalList:[],amountBarData:[],amountBarDataByQuarter:[],chartShow:!1,type:""}},methods:{sortChange:function(t){this.itemParams.sortProp=t.prop,this.itemParams.sortOrder=t.order,this.getAmountData()},getHospitalList:function(){var t=this,e=l.default.baseContext+"/supervise/supHospital/all";this.$http_post(e,{}).then(function(e){1==e.state?t.hospitalList=e.row:t.$alert(e.message)})},handleClick:function(){this.getAmountData()},getAmountData:function(){var t=this,e="";"first"==this.activeName&&(e=l.default.baseContext+"/supervise/statistics/getDrugAmountByMonth"),"second"==this.activeName&&(e=l.default.baseContext+"/supervise/statistics/getDrugAmountByQuarter"),"third"==this.activeName&&(e=l.default.baseContext+"/supervise/statistics/getDrugAmountByYear"),null!=this.itemParams.chainExceptionRate&&""!=this.itemParams.chainExceptionRate||(this.itemParams.chainExceptionRate=50),null!=this.itemParams.grewExceptionRate&&""!=this.itemParams.grewExceptionRate||(this.itemParams.grewExceptionRate=50);var a=this.openLoading();this.$http_post(e,this.itemParams).then(function(e){1==e.state?(a.close(),null!=e.rows&&(t.$set(t,"amountBarData",e.rows),t.$set(t,"amountBarDataByQuarter",e.rows),t.itemParams.records=e.records)):(a.close(),null!=e.message?t.$message.error(e.message):t.$message.error("系统异常"))}).catch(function(t){a.close(),console.log(t)})},showAmount:function(t,e){var a=this;this.detailId=t,this.hospitalId=this.itemParams.hospitalId,this.type=e,this.chartShow=!0,this.$nextTick(function(){a.$refs.asyncDialog.show=!0})},getDictItem:function(t){var e=this,a=this.openLoading();this.$http_post(l.default.baseContext+"/supervise/supDictItem/getItemList",{code:t}).then(function(l){var o=l.rows;1==l.state?("SOURCE"==t&&(e.drugSourceList=o),a.close()):(a.close(),e.$message.error(l.message))})},getDrugCatalogList:function(){var t=this;this.drugCatalogList=[],url=l.default.baseContext+"/supervise/supDrugCatalog/getDrugCatalogList";var e=this.openLoading();this.$http_post(url,null).then(function(a){if(1==a.state)for(var l=a.rows,o=0;o<l.length;o++){var r=l[o].category,s="";"1"==r&&(s="中药"),"2"==r&&(s="西药");var n={id:l[o].id,name:l[o].name,category:s};t.drugCatalogList.push(n)}else t.$alert(a.message);e.close()})},add:function(t,e){var a=this;if("show"==t){this.isSetPrice=!0;var o=this.openLoading();this.$http_post(l.default.baseContext+"/supervise/supDrugDetail/info/"+e.detailId,{}).then(function(t){1==t.state?null!=t.row?(a.formData={version:t.row.version,catalogName:t.row.catalogName,drugCompanyName:t.row.drugCompanyName,category:t.row.category,id:t.row.id,code:t.row.code,source:t.row.source,catalogId:t.row.catalogId,goodsName:t.row.goodsName,country:t.row.country,countryType:t.row.countryType,electionScope:t.row.electionScope,dosageForm:t.row.dosageForm,specs:t.row.specs,packingSpecs:t.row.packingSpecs,packing:t.row.packing,unit:t.row.unit,qualityLevel:t.row.qualityLevel,company:t.row.company,approvalNumber:t.row.approvalNumber,standardCode:t.row.standardCode,medicalInsurance:t.row.medicalInsurance,attribute:t.row.attribute,adjuvant:t.row.adjuvant,net:t.row.net,coefficient:t.row.coefficient,remark:t.row.remark,status:t.row.status},a.dialogVisible=!0):a.$message.error("系统异常"):null!=t.message?a.$message.error(t.message):a.$message.error("系统异常"),o.close()})}},close:function(){this.dialogVisible=!1,this.chartShow=!1},onSearch:function(t){"reset"==t?(this.itemParams={quarter:Math.floor(((new Date).getMonth()+3)/3).toString(),year:(new Date).format("yyyy"),dimension:"month",hospitalId:"",detailId:"",catalogName:"",page:1,limit:10,records:0,nowMonth:(new Date).format("yyyy-MM"),chainExceptionRate:50,grewExceptionRate:50,grewException:"1",chainException:"1",sortProp:"",sortOrder:""},this.itemParams.page=1,this.getAmountData()):(this.itemParams.page=1,this.getAmountData())},onPageClick:function(t){this.itemParams.page=t,this.getAmountData()},getDictItemName:function(t){for(var e=0;e<this.drugSourceList.length;e++)if(this.drugSourceList[e].value==t)return this.drugSourceList[e].label}},mounted:function(){this.getHospitalList(),this.getAmountData(),this.getDrugCatalogList()},beforeDestroy:function(){window.onresize=null}}},RVXv:function(t,e,a){"use strict";a.r(e);var l=a("tvZ+"),o=a("CsHR");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return o[t]})}(r);a("JPmy");var s=a("gp09"),n=Object(s.a)(o.default,l.render,l.staticRenderFns,!1,null,"0e5625d8",null);e.default=n.exports},"tvZ+":function(t,e,a){"use strict";var l=a("3fBT");a.o(l,"render")&&a.d(e,"render",function(){return l.render}),a.o(l,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return l.staticRenderFns})},xWXB:function(t,e,a){}}]);