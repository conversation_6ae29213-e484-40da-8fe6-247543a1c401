(window.webpackJsonp=window.webpackJsonp||[]).push([[57],{"8TmG":function(e,t,a){"use strict";a.r(t);var r=a("8wwp"),s=a.n(r);for(var l in r)["default"].indexOf(l)<0&&function(e){a.d(t,e,function(){return r[e]})}(l);t.default=s.a},"8wwp":function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=o(a("/umX")),s=o(a("DWNM")),l=o(a("Q9c5"));function o(e){return e&&e.__esModule?e:{default:e}}t.default={name:"countryDrug-list",mixins:[s.default],data:function(){var e;return{show:!1,currentComponent:"",country:"",drugCode:"",busiCode:"",drugSourceList:[],disabled:!0,dataList:[],brandFold:!1,drugSourceOption:[],drugCatalogList:[],params:{packingSpecs:"",code:"",specs:"",catalogName:"",page:1,limit:10,records:0,standardCode:"",goodsName:"",source:"",catalogId:"",country:"1",dosageForm:"",company:"",approvalNumber:""},drugDetail:(e={version:"",code:"",drugCompanyName:"",category:"",id:"",source:"",goodsName:"",catalogName:"",catalogId:"",country:"0",countryType:"",electionScope:"",dosageForm:"",specs:"",packingSpecs:"",packing:"",unit:"",qualityLevel:"",company:"",approvalNumber:"",standardCode:"",medicalInsurance:"",attribute:"",adjuvant:"0",net:"0",coefficient:"",remark:""},(0,r.default)(e,"version",""),(0,r.default)(e,"status",""),e),unitList:[{name:"盒",value:"盒"},{name:"支",value:"支"},{name:"片",value:"片"},{name:"丸",value:"丸"},{name:"粒",value:"粒"},{name:"袋",value:"袋"},{name:"瓶",value:"瓶"},{name:"剂",value:"剂"}],electionScopeList:[{name:"中选品种",value:"1"},{name:"非中选品种",value:"2"},{name:"替补品种",value:"3"}],categoryList:[{name:"中药",value:"1"},{name:"西药",value:"2"}],dialogVisible:!1,tableHeight:100}},mounted:function(){var e=this;this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100},this.initCurrentComponent(),this.initData(),document.addEventListener("click",this.onHideSearchCon)},beforeDestroy:function(){window.onresize=null,document.removeEventListener("click",this.onHideSearchCon)},methods:{initCurrentComponent:function(){this.currentComponent="drugPrice"},close:function(){this.$refs.asyncDialog.show=!1,this.show=!1,this.dialogVisible=!1,this.initDrugList()},setPrice:function(e){var t=this;this.busiCode=e.busiCode,this.country=e.country,this.drugCode=e.code,this.show=!0,this.$nextTick(function(){t.$refs.asyncDialog.show=!0})},getDictItemName:function(e){for(var t=0;t<this.drugSourceList.length;t++)if(this.drugSourceList[t].value==e)return this.drugSourceList[t].label},getDictItem:function(e){var t=this,a=this.openLoading();this.$http_post(l.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(r){var s=r.rows;1==r.state?("SOURCE"==e&&(t.drugSourceList=s),a.close()):(a.close(),t.$message.error(r.message))})},showDrugDeatil:function(e){this.drugDetail=e,this.dialogVisible=!0},initData:function(){this.getDrugCatalogList(),this.initDrugList(),this.getDictItem("SOURCE")},getDrugCatalogList:function(){var e=this;this.drugCatalogList=[],url=l.default.baseContext+"/supervise/supDrugCatalog/getDrugCatalogList";var t=this.openLoading();this.$http_post(url,null).then(function(a){if(1==a.state)for(var r=a.rows,s=0;s<r.length;s++){var l=r[s].category,o="";"1"==l&&(o="中药"),"2"==l&&(o="西药");var n={id:r[s].id,name:r[s].name,category:o};e.drugCatalogList.push(n)}else e.$alert(a.message);t.close()})},onHideSearchCon:function(e){for(var t=0;t<e.path.length;t++){var a=e.path[t];if(a==this.$refs.searchCon||a==this.$refs.foldBtn)return}this.brandFold=!1},handleCurrentChange:function(e){},onSearch:function(e){"reset"==e?(this.params={packingSpecs:"",code:"",specs:"",page:1,limit:10,records:0,standardCode:"",source:"",goodsName:"",catalogId:"",country:"1",dosageForm:"",company:"",approvalNumber:""},this.initData()):(this.params.page=1,this.initData()),this.brandFold=!1},onPageClick:function(e){this.params.page=e,this.initData()},initDrugList:function(){var e=this,t=this.params,a=this.openLoading(),r=l.default.baseContext+"/supervise/supDrugDetail/getSupDrugList";this.$http_post(r,t).then(function(t){if(1==t.state){var r=t.rows;e.dataList=r,e.params.records=t.records,a.close()}else a.close(),e.$alert(t.message)})},changeFoldState:function(){this.brandFold=!this.brandFold}}}},TAXm:function(e,t,a){"use strict";a.r(t);var r=a("wVqA"),s=a("8TmG");for(var l in s)["default"].indexOf(l)<0&&function(e){a.d(t,e,function(){return s[e]})}(l);a("hPsz");var o=a("gp09"),n=Object(o.a)(s.default,r.render,r.staticRenderFns,!1,null,"6ef4497b",null);t.default=n.exports},X5CJ:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"},[t("span",[e._v("药本位码")]),t("el-input",{attrs:{placeholder:"请输入药品本位码"},model:{value:e.params.standardCode,callback:function(t){e.$set(e.params,"standardCode",t)},expression:"params.standardCode"}}),t("span",{ref:"foldBtn"},[t("span",{staticStyle:{"margin-left":"20px"},on:{click:e.changeFoldState}},[e._v(e._s(e.brandFold?"收起":"更多"))]),t("el-button",{staticStyle:{"margin-right":"20px"},attrs:{type:"text"},on:{click:e.changeFoldState}},[t("i",{class:e.brandFold?"el-icon-arrow-up":"el-icon-arrow-down"})])],1),t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.brandFold,expression:"brandFold"}],ref:"searchCon",staticClass:"searchCon"},[t("el-form",{ref:"searchForm",staticClass:"item-item",attrs:{model:e.params,"label-position":"left","label-width":"150px"}},[t("div",{staticClass:"fromBox"},[t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"商品名",prop:"goodsName"}},[t("el-input",{attrs:{placeholder:"请输入商品名"},model:{value:e.params.goodsName,callback:function(t){e.$set(e.params,"goodsName",t)},expression:"params.goodsName"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"药本位码",prop:"standardCode"}},[t("el-input",{attrs:{placeholder:"请输入药本位码"},model:{value:e.params.standardCode,callback:function(t){e.$set(e.params,"standardCode",t)},expression:"params.standardCode"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"通用名",prop:"catalogId"}},[t("el-select",{attrs:{placeholder:"请选择通用名",filterable:""},model:{value:e.params.catalogId,callback:function(t){e.$set(e.params,"catalogId",t)},expression:"params.catalogId"}},e._l(e.drugCatalogList,function(a){return t("el-option",{key:a.id,attrs:{label:a.name,value:a.id}},[t("span",{staticStyle:{float:"left"}},[e._v(e._s(a.name))]),t("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(a.category))])])}),1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"商品名",prop:"goodsName"}},[t("el-input",{attrs:{placeholder:"请输入商品名"},model:{value:e.params.goodsName,callback:function(t){e.$set(e.params,"goodsName",t)},expression:"params.goodsName"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"剂型",prop:"dosageForm"}},[t("el-input",{attrs:{placeholder:"请输入药品剂型"},model:{value:e.params.dosageForm,callback:function(t){e.$set(e.params,"dosageForm",t)},expression:"params.dosageForm"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"生产企业",prop:"company"}},[t("el-input",{attrs:{placeholder:"请输入药品生产企业"},model:{value:e.params.company,callback:function(t){e.$set(e.params,"company",t)},expression:"params.company"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"规格",prop:"specs"}},[t("el-input",{attrs:{placeholder:"请输入药品规格"},model:{value:e.params.specs,callback:function(t){e.$set(e.params,"specs",t)},expression:"params.specs"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"包装规格",prop:"specs"}},[t("el-input",{attrs:{placeholder:"请输入药品包装规格"},model:{value:e.params.packingSpecs,callback:function(t){e.$set(e.params,"packingSpecs",t)},expression:"params.packingSpecs"}})],1)],1)],1)],1)]),t("span",{staticClass:"programme-btn",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-refresh-left"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")]),t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")])],1)],1)],1)]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.dataList,"highlight-current-row":"",height:e.tableHeight},on:{"current-change":e.handleCurrentChange}},[t("el-table-column",{attrs:{prop:"busiCode",label:"药品编码",width:"150"}}),t("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"180"}}),t("el-table-column",{attrs:{prop:"goodsName",label:"商品名",width:"150"}}),t("el-table-column",{attrs:{prop:"dosageForm",label:"剂型"}}),t("el-table-column",{attrs:{prop:"specs",label:"规格"}}),t("el-table-column",{attrs:{prop:"packingSpecs",label:"包装规格"}}),t("el-table-column",{attrs:{prop:"unit",label:"单位"}}),t("el-table-column",{attrs:{prop:"standardCode",label:"药品本位码",width:"180"}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"单价(元)"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.unitPrice?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v(e._s(a.row.unitPrice))])],1):t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("暂无")])],1)]}}])}),t("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"200"}}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"200"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showDrugDeatil(a.row)}}},[e._v("查看详情\n                        ")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.setPrice(a.row)}}},[e._v("设置采购价格")])]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)]),t("el-dialog",{attrs:{title:"【"+e.drugDetail.catalogName+"】药品详情",visible:e.dialogVisible,width:"45%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",[t("el-form",{ref:"showDrugDetailForm",staticClass:"item-form",attrs:{model:e.drugDetail,"label-width":"110px"}},[t("div",{staticClass:"fromBox"},[t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"采购平台"}},[e._v("\n                                "+e._s(e.getDictItemName(e.drugDetail.source))+"\n                            ")])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"药品本位码"}},[e._v("\n                                "+e._s(e.drugDetail.standardCode)+"\n                            ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"通用名"}},[e._v("\n                                "+e._s(e.drugDetail.catalogName)+"\n                            ")])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"商品名"}},[e._v("\n                                "+e._s(e.drugDetail.goodsName)+"\n                            ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"药品单价"}},[e.drugDetail.unitPrice?t("span",[e._v(" "+e._s(e.drugDetail.unitPrice+"元"))]):e._e(),e.drugDetail.unitPrice?e._e():t("span",[e._v("暂无价格")])])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"药品编码"}},[e._v("\n                                "+e._s(e.drugDetail.code)+"\n                            ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"剂型"}},[e._v("\n                                "+e._s(e.drugDetail.dosageForm)+"\n                            ")])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"规格"}},[e._v("\n                                "+e._s(e.drugDetail.specs)+"\n                            ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"包装规格"}},[e._v("\n                                "+e._s(e.drugDetail.packingSpecs)+"\n                            ")])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"单位"}},[e._v("\n                                "+e._s(e.drugDetail.unit)+"\n                            ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"生产企业",prop:"company"}},[e._v("\n                                "+e._s(e.drugDetail.drugCompanyName)+"\n                            ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"医保目录"}},["1"==e.drugDetail.medicalInsurance?t("span",[e._v("甲类")]):e._e(),"2"==e.drugDetail.medicalInsurance?t("span",[e._v("乙类")]):e._e()])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"药品类别"}},["1"==e.drugDetail.category?t("span",[e._v("中药")]):e._e(),"2"==e.drugDetail.category?t("span",[e._v("西药")]):e._e(),"1"!=e.drugDetail.category&&"2"!=e.drugDetail.category?t("span",[e._v("其它")]):e._e()])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"国家集中集采"}},["1"==e.drugDetail.country?t("span",[e._v("是")]):e._e(),"0"==e.drugDetail.country?t("span",[e._v("否")]):e._e()])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"批准文号"}},[e._v("\n                                "+e._s(e.drugDetail.approvalNumber)+"\n                            ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"包装材质"}},[e._v("\n                                "+e._s(e.drugDetail.packing)+"\n                            ")])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"质量层次"}},[e._v("\n                                "+e._s(e.drugDetail.qualityLevel)+"\n                            ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"基药属性"}},[e._v("\n                                "+e._s(e.drugDetail.attribute)+"\n                            ")])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"拆零系数"}},[e._v("\n                                "+e._s(e.drugDetail.coefficient)+"\n                            ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"是否辅药"}},["1"==e.drugDetail.adjuvant?t("span",[e._v("是")]):e._e(),"0"==e.drugDetail.adjuvant?t("span",[e._v("否")]):e._e()])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"是否挂网"}},["1"==e.drugDetail.net?t("span",[e._v("是")]):e._e(),"0"==e.drugDetail.net?t("span",[e._v("否")]):e._e()])],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{staticClass:"col-left",attrs:{label:"备注"}},[e._v("\n                                "+e._s(e.drugDetail.remark)+"\n                            ")])],1)],1)],1)])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1,e.drugDetail={}}}},[e._v("关 闭")])],1)]),t("el-dialog",{attrs:{title:"药品采购价格",visible:e.show,width:"45%"},on:{"update:visible":function(t){e.show=t}}},[t(e.currentComponent,{ref:"asyncDialog",tag:"component",attrs:{drugCode:e.drugCode,busiCode:e.busiCode,country:e.country},on:{close:e.close}})],1)],1)},t.staticRenderFns=[]},b0Uy:function(e,t,a){},hPsz:function(e,t,a){"use strict";a("b0Uy")},wVqA:function(e,t,a){"use strict";var r=a("X5CJ");a.o(r,"render")&&a.d(t,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return r.staticRenderFns})}}]);