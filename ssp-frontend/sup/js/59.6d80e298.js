(window.webpackJsonp=window.webpackJsonp||[]).push([[59],{"5smv":function(e,a,t){"use strict";var o=t("Iu2S");t.o(o,"render")&&t.d(a,"render",function(){return o.render}),t.o(o,"staticRenderFns")&&t.d(a,"staticRenderFns",function(){return o.staticRenderFns})},Ftqr:function(e,a,t){"use strict";t.r(a);var o=t("UkXT"),r=t.n(o);for(var l in o)["default"].indexOf(l)<0&&function(e){t.d(a,e,function(){return o[e]})}(l);a.default=r.a},"GUw+":function(e,a,t){"use strict";t.r(a);var o=t("5smv"),r=t("Ftqr");for(var l in r)["default"].indexOf(l)<0&&function(e){t.d(a,e,function(){return r[e]})}(l);t("m+oj");var s=t("gp09"),n=Object(s.a)(r.default,o.render,o.staticRenderFns,!1,null,"60d6d391",null);a.default=n.exports},Iu2S:function(e,a){Object.defineProperty(a,"__esModule",{value:!0});a.render=function(){var e=this,a=e._self._c;return a("el-dialog",{attrs:{title:e.title,visible:e.show,width:"45%"},on:{"update:visible":function(a){e.show=a}}},[a("div",{staticClass:"dialog-content"},[a("el-form",{ref:"form",staticClass:"item-form",attrs:{model:e.formData,"label-width":"110px",rules:e.rules}},[a("div",{staticClass:"fromBox"},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"通用名",prop:"catalogId"}},[a("el-select",{attrs:{filterable:"",placeholder:"请选择通用名"},on:{change:e.catalogChange,blur:e.selectChange},model:{value:e.formData.catalogId,callback:function(a){e.$set(e.formData,"catalogId",a)},expression:"formData.catalogId"}},e._l(e.drugCatalogList,function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}},[a("span",{staticStyle:{float:"left"}},[e._v(e._s(t.name))]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.category))])])}),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"商品名",prop:"goodsName"}},[a("el-input",{attrs:{placeholder:"请输入商品名"},model:{value:e.formData.goodsName,callback:function(a){e.$set(e.formData,"goodsName",a)},expression:"formData.goodsName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"剂型",prop:"dosageForm"}},[a("el-input",{attrs:{placeholder:"请输入药品剂型"},model:{value:e.formData.dosageForm,callback:function(a){e.$set(e.formData,"dosageForm",a)},expression:"formData.dosageForm"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"规格",prop:"specs"}},[a("el-input",{attrs:{placeholder:"请输入药品规格,如 100mg"},model:{value:e.formData.specs,callback:function(a){e.$set(e.formData,"specs",a)},expression:"formData.specs"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包装规格",prop:"packingSpecs"}},[a("el-input",{attrs:{placeholder:"请输入药品包装规格"},model:{value:e.formData.packingSpecs,callback:function(a){e.$set(e.formData,"packingSpecs",a)},expression:"formData.packingSpecs"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"单位",prop:"unit"}},[a("el-select",{attrs:{placeholder:"请选择药品单位"},model:{value:e.formData.unit,callback:function(a){e.$set(e.formData,"unit",a)},expression:"formData.unit"}},e._l(e.unitList,function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})}),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"药本位码",prop:"standardCode"}},[a("el-input",{attrs:{placeholder:"请输入药品本位码"},model:{value:e.formData.standardCode,callback:function(a){e.$set(e.formData,"standardCode",a)},expression:"formData.standardCode"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"生产企业",prop:"company"}},[a("el-input",{attrs:{placeholder:"请输入药品生产企业"},model:{value:e.formData.drugCompanyName,callback:function(a){e.$set(e.formData,"drugCompanyName",a)},expression:"formData.drugCompanyName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"医保目录",prop:"medicalInsurance"}},[a("el-select",{attrs:{placeholder:"请选择医保目录类别"},model:{value:e.formData.medicalInsurance,callback:function(a){e.$set(e.formData,"medicalInsurance",a)},expression:"formData.medicalInsurance"}},[a("el-option",{attrs:{label:"甲类",value:"1"}}),a("el-option",{attrs:{label:"乙类",value:"2"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"药品类别",prop:"category"}},[a("el-select",{attrs:{placeholder:"请选择药品类别"},model:{value:e.formData.category,callback:function(a){e.$set(e.formData,"category",a)},expression:"formData.category"}},e._l(e.categoryList,function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})}),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"国家集中集采",prop:"country"}},[a("el-switch",{attrs:{"active-value":"1","inactive-value":"0",disabled:e.countryDisabled},on:{change:e.countryChange},model:{value:e.formData.country,callback:function(a){e.$set(e.formData,"country",a)},expression:"formData.country"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"批准文号",prop:"approvalNumber"}},[a("el-input",{attrs:{placeholder:"请输入药品批准文号"},model:{value:e.formData.approvalNumber,callback:function(a){e.$set(e.formData,"approvalNumber",a)},expression:"formData.approvalNumber"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{staticClass:"col-left",attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请填写备注"},model:{value:e.formData.remark,callback:function(a){e.$set(e.formData,"remark",a)},expression:"formData.remark"}})],1)],1)],1)],1)])],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.close}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确 定")])],1)])},a.staticRenderFns=[]},UkXT:function(e,a,t){Object.defineProperty(a,"__esModule",{value:!0});var o=l(t("Q9c5")),r=l(t("DWNM"));l(t("dqtc"));function l(e){return e&&e.__esModule?e:{default:e}}a.default={mixins:[r.default],name:"drug-edit",data:function(){return{countryDisabled:!1,show:!1,formData:{id:"",source:"",code:"",goodsName:"",catalogId:"",country:"0",countryType:"",electionScope:"",dosageForm:"",specs:"",packingSpecs:"",packing:"",unit:"",qualityLevel:"",company:"",approvalNumber:"",standardCode:"",medicalInsurance:"",attribute:"",adjuvant:"0",net:"0",coefficient:"",remark:""},categoryList:[{name:"中药",value:"1"},{name:"西药",value:"2"}],title:"",drugCatalogList:[],unitList:[{name:"盒",value:"盒"},{name:"支",value:"支"},{name:"片",value:"片"},{name:"丸",value:"丸"},{name:"粒",value:"粒"},{name:"袋",value:"袋"},{name:"瓶",value:"瓶"},{name:"剂",value:"剂"}],rules:{code:[{required:!0,message:"请输入药品编码",trigger:"blur"}],catalogId:[{required:!0,message:"请选择药品目录",trigger:"change"}],source:[{required:!0,message:"请选择药品目录",trigger:"change"}]}}},props:{failId:{type:String,default:""}},watch:{failId:function(e){e&&this.init()}},mounted:function(){this.getDrugCatalogList(),this.failId&&this.init()},methods:{catalogChange:function(e){var a=this.drugCatalogList.filter(function(a){return a.id==e});this.formData.catalogName=a[0].name,this.formData.category=a[0].category},selectChange:function(e){e.target.value&&(this.formData.catalogName=e.target.value,this.formData.catalogId=e.target.value)},countryChange:function(e){this.formData.source="1"==e?"-1":""},init:function(){var e=this,a=this.openLoading();this.$http_post(o.default.baseContext+"/supervise/importFailLog/show/"+this.failId,{}).then(function(t){if(1==t.state){if(null!=t.row){var o=JSON.parse(t.row.data);e.title=t.row.msg,e.$set(e,"formData",o)}else e.$message.error("系统异常");a.close()}else null!=t.message?e.$message.error(t.message):e.$message.error("系统异常")}).catch(function(e){a.close(),console.log(e)})},save:function(){var e=this;this.$refs.form.validate(function(a){if(!a)return!1;e.formData.failId=e.failId;var t=o.default.baseContext+"/supervise/supDrugDetail/edit",r=e.openLoading();e.$http_post(t,e.formData,!0).then(function(a){r.close(),0==a.state&&e.$alert(a.message),1!=a.state||a.row||(e.$message.success("操作成功"),e.close()),1==a.state&&a.row&&e.$alert("该药品已存在，确定升级版本吗","确认信息",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,type:"warning"}).then(function(){var t=e.openLoading();e.formData.id=a.row.id,e.formData.code=a.row.code,e.formData.version=a.row.version,e.$http_post(o.default.baseContext+"/supervise/supDrugDetail/updateVersion",e.formData,!0).then(function(a){t.close(),1==a.state?(e.$message.success("操作成功"),e.close()):e.$message.error(a.message)})}).catch(function(e){console.log(e)})})})},getDrugCatalogList:function(){var e=this;url=o.default.baseContext+"/supervise/supDrugCatalog/getDrugCatalogList";var a=this.openLoading();this.$http_post(url,null).then(function(t){if(1==t.state)for(var o=t.rows,r=0;r<o.length;r++){var l=o[r].category,s="";"1"==l&&(s="中药"),"2"==l&&(s="西药");var n={id:o[r].id,name:o[r].name,category:s};e.drugCatalogList.push(n)}else e.$alert(t.message);a.close()})},close:function(){this.$emit("close")}}}},hkn2:function(e,a,t){},"m+oj":function(e,a,t){"use strict";t("hkn2")}}]);