(window.webpackJsonp=window.webpackJsonp||[]).push([[88],{"9FT0":function(t,e,a){},CmVb:function(t,e,a){"use strict";a.r(e);var s=a("I9WZ"),n=a.n(s);for(var l in s)["default"].indexOf(l)<0&&function(t){a.d(e,t,function(){return s[t]})}(l);e.default=n.a},I9WZ:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var s=l(a("Q9c5")),n=l(a("DWNM"));function l(t){return t&&t.__esModule?t:{default:t}}e.default={name:"medicalApply-list",mixins:[n.default],data:function(){return{deliveryAdmin:!1,pickerOptions:{shortcuts:[{text:"上个月",onClick:function(t){var e=new Date,a=e.getFullYear(),s=e.getMonth();0==s&&(s=12,a-=1),s<10&&(s="0"+s);var n=new Date(a+"-"+s+"-01").format("yyyy-MM-dd HH:mm:ss"),l=new Date(a,s,0),r=new Date(a+"-"+s+"-"+l.getDate()).format("yyyy-MM-dd HH:mm:ss");t.$emit("pick",[n,r])}},{text:"今年至上个月",onClick:function(t){var e=new Date,a=e.getFullYear(),s=e.getMonth();0==s&&(s=12,a-=1),s<10&&(s="0"+s);var n=new Date(a+"-01-01").format("yyyy-MM-dd HH:mm:ss"),l=new Date(a,s,0),r=new Date(a+"-"+s+"-"+l.getDate()).format("yyyy-MM-dd HH:mm:ss");t.$emit("pick",[n,r])}}]},multipleSelection:[],dataList:[],hospitalList:[],params:{page:1,limit:10,records:0,num:"",date:"",status:"",startDate:"",endDate:"",curNode:"4",bizStatus:"1"},show:!1,title:"医保基金支付",dialogVisible:!1,listLoading:!1,tableHeight:100,confirmDataList:[],showBox:!1}},mounted:function(){var t=this;this.getDictItem("SOURCE"),this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-150}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-150},this.onQuery(),this.getHospitalList()},computed:{},watch:{$route:{handler:function(){},deep:!0}},filters:{formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}},updated:function(){this.toggleSelection(this.dataList)},methods:{detail:function(t){this.$router.push({name:"settlementDetail",query:{settlementId:t.id}})},confirmReconcile:function(){var t=this,e=this.openLoading("提交中...");this.$http_post(s.default.baseContext+"/supervise/supReconcile/save",this.confirmDataList,!0).then(function(a){1==a.state?(e.close(),t.onQuery(),t.showBox=!1):(e.close(),t.$alert(a.message))})},toggleSelection:function(t){},handleSelectionChange:function(t){this.multipleSelection=t},createReconcile:function(){var t=this;if(console.log("..",this.multipleSelection),0==this.multipleSelection.length)this.$message({type:"error",message:"所选内容不能为空！"});else{var e=this.multipleSelection.map(function(t){return t.id});console.log(e);var a=s.default.baseContext+"/supervise/supSettlement/rsItemList";this.$http_post(a,e,!0).then(function(e){1==e.state?(t.confirmDataList=e.rows,t.showBox=!0,console.log(t.confirmDataList)):t.$alert(e.message)})}},close:function(){this.show=!1},onPageClick:function(t){this.params.page=t,this.onInvoiceQuery()},onQuery:function(){var t=this,e=this.params,a=this.openLoading(),n=s.default.baseContext+"/supervise/supSettlement/list";this.$http_post(n,e).then(function(e){1==e.state?(t.dataList=e.rows,t.params.records=e.records,a.close()):(a.close(),t.$alert(e.message))})},onSearch:function(t){"reset"==t?(this.params.num="",this.params.status="",this.params.date="",this.params.startDate="",this.params.endDate="",this.onQuery()):""!=this.params.num||""!=this.params.status||""!=this.params.date?(this.params.page=1,this.onQuery()):this.$message.error("请输入查询条件")},getDictItemName:function(t){for(var e=0;e<this.drugSourceList.length;e++)if(this.drugSourceList[e].value==t)return this.drugSourceList[e].label},getDictItem:function(t){var e=this,a=this.openLoading();this.$http_post(s.default.baseContext+"/supervise/supDictItem/getItemList",{code:t}).then(function(s){var n=s.rows;1==s.state?("SOURCE"==t&&(e.drugSourceList=n),"WARNING"==t&&(e.warningData=n),a.close()):(a.close(),e.$message.error(s.message))})},getHospitalList:function(){var t=this,e=s.default.baseContext+"/supervise/supHospital/all";this.$http_post(e,{}).then(function(e){1==e.state?t.hospitalList=e.row:t.$alert(e.message)})},numAdd:function(t,e){var a,s,n,l;try{a=t.toString().split(".")[1].length}catch(t){a=0}try{s=e.toString().split(".")[1].length}catch(t){s=0}if(l=Math.abs(a-s),n=Math.pow(10,Math.max(a,s)),l>0){var r=Math.pow(10,l);a>s?(t=Number(t.toString().replace(".","")),e=Number(e.toString().replace(".",""))*r):(t=Number(t.toString().replace(".",""))*r,e=Number(e.toString().replace(".","")))}else t=Number(t.toString().replace(".","")),e=Number(e.toString().replace(".",""));return(t+e)/n}}}},Jr8g:function(t,e,a){"use strict";a("9FT0")},MOVY:function(t,e,a){"use strict";var s=a("bdcF");a.o(s,"render")&&a.d(e,"render",function(){return s.render}),a.o(s,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return s.staticRenderFns})},Wnog:function(t,e,a){"use strict";a.r(e);var s=a("MOVY"),n=a("CmVb");for(var l in n)["default"].indexOf(l)<0&&function(t){a.d(e,t,function(){return n[t]})}(l);a("Jr8g");var r=a("gp09"),i=Object(r.a)(n.default,s.render,s.staticRenderFns,!1,null,"5fcead44",null);e.default=i.exports},bdcF:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left"},[e("el-row",{attrs:{gutter:10}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("结算单编号")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入通用名"},model:{value:t.params.num,callback:function(e){t.$set(t.params,"num",e)},expression:"params.num"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("申请时间")]),e("div",{staticClass:"searchInput"},[e("el-date-picker",{attrs:{size:"small","value-format":"yyyy-MM-dd HH:mm:ss",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:t.params.date,callback:function(e){t.$set(t.params,"date",e)},expression:"params.date"}})],1)])])],1)],1),e("div",{staticClass:"right"},[e("div",{staticStyle:{"margin-bottom":"5px"}},[t.$route.query.type?t._e():e("el-button",{attrs:{size:"small",type:"warning"},on:{click:t.createReconcile}},[t._v("生成对账单\n                ")])],1),e("div",[e("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1)])]),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{ref:"multipleTable",staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{"row-key":function(t){return t.id},data:t.dataList,height:t.tableHeight,border:""},on:{"selection-change":t.handleSelectionChange}},[e("el-table-column",{attrs:{"reserve-selection":!0,type:"selection",width:"50"}}),e("el-table-column",{attrs:{type:"index",label:"序号",width:"50"}}),e("el-table-column",{attrs:{prop:"num",label:"结算单编号"}}),e("el-table-column",{attrs:{prop:"count",label:"结算总笔数"}}),e("el-table-column",{attrs:{prop:"totalPrice",label:"结算总金额"}}),e("el-table-column",{attrs:{prop:"backCount",label:"打回笔数"}}),e("el-table-column",{attrs:{prop:"backPrice",label:"打回总金额"}}),e("el-table-column",{attrs:{prop:"creationTime",label:"生成时间"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t._f("formatTime")(a.row.creationTime)))])]}}])}),e("el-table-column",{attrs:{prop:"createName",label:"创建人"}}),t.$route.query.type?t._e():e("el-table-column",{attrs:{label:"操作",align:"center",width:"100",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.detail(a.row)}}},[t._v("详情")])]}}],null,!1,316531087)})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)]),e("el-dialog",{attrs:{title:"确认对账单列表",visible:t.showBox,width:"90%"},on:{close:function(e){t.showBox=!1}}},[e("div",{staticClass:"dialog-content"},[e("el-table",{ref:"confirmTable",staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.confirmDataList,border:""}},[e("el-table-column",{attrs:{type:"index",label:"序号",width:"50"}}),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"150"}}),e("el-table-column",{attrs:{prop:"deliveryName",label:"配送机构",width:"200"}}),e("el-table-column",{attrs:{prop:"invoiceCode",label:"发票代码",width:"120"}}),e("el-table-column",{attrs:{prop:"invoiceNo",label:"发票号",width:"120"}}),e("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150"}}),e("el-table-column",{attrs:{prop:"num",label:"开票数量"}}),e("el-table-column",{attrs:{prop:"unitPrice",label:"单价"}}),e("el-table-column",{attrs:{prop:"taxesAmount",label:"金额"}}),e("el-table-column",{attrs:{prop:"orderNum",label:"订单编号",width:"120"}}),e("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"180",align:"left"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",[t._v("\n                            "+t._s(t.getDictItemName(a.row.source))+"\n                        ")])]}}])}),e("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单号",width:"120"}}),e("el-table-column",{attrs:{prop:"deliveryName",label:"配送企业",width:"180"}}),e("el-table-column",{attrs:{prop:"country",label:"来源",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                        "+t._s("1"==e.row.country?"国家集采":"2"==e.row.country?"非国家集采":"线下采购")+"\n                    ")]}}])})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.showBox=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.confirmReconcile}},[t._v("确 定")])],1)])],1)},e.staticRenderFns=[]}}]);