(window.webpackJsonp=window.webpackJsonp||[]).push([[45],{"0ZDf":function(t,e,a){"use strict";a.r(e);var s=a("dJ3l"),o=a.n(s);for(var n in s)["default"].indexOf(n)<0&&function(t){a.d(e,t,function(){return s[t]})}(n);e.default=o.a},"6NKM":function(t,e,a){"use strict";var s=a("Bggy");a.o(s,"render")&&a.d(e,"render",function(){return s.render}),a.o(s,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return s.staticRenderFns})},Bggy:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0;var s=function(t){return t&&t.__esModule?t:{default:t}}(a("/umX"));e.render=function(){var t,e=this,a=e._self._c;return a("div",{ref:"tableH",staticClass:"content"},[a("div",{staticClass:"search-header flex-row"},[a("div",{staticClass:"left"},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[e._v("批次")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择批次"},model:{value:e.params.batch,callback:function(t){e.$set(e.params,"batch",t)},expression:"params.batch"}},[a("el-option",{attrs:{label:"全部",value:""}}),e._l(e.batchList,function(t){return a("el-option",{key:t.code,attrs:{label:t.batchName,value:t.code}})})],2)],1)])]),this.adminRole||this.medicalAdmin||this.socialAdmin?a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[e._v("区划")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:{size:"small",placeholder:"请选择区划"},model:{value:e.params.regionId,callback:function(t){e.$set(e.params,"regionId",t)},expression:"params.regionId"}},e._l(e.regionList,function(t){return a("el-option",{key:t.code,attrs:{label:t.name,value:t.code}})}),1)],1)])]):e._e(),this.adminRole||this.medicalAdmin||this.socialAdmin||this.areaRegionAdmin?a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[e._v("医疗机构")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:(t={size:"small",clearable:""},(0,s.default)(t,"clearable",""),(0,s.default)(t,"filterable",""),(0,s.default)(t,"placeholder","请选择医疗机构"),t),model:{value:e.params.hospitalId,callback:function(t){e.$set(e.params,"hospitalId",t)},expression:"params.hospitalId"}},e._l(e.hospitalList,function(t){return a("el-option",{key:t.name,attrs:{label:t.name,value:t.id}})}),1)],1)])]):e._e(),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[e._v("药品通用名")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入药品通用名称"},model:{value:e.params.catalogName,callback:function(t){e.$set(e.params,"catalogName",t)},expression:"params.catalogName"}})],1)])])],1)],1),a("div",{staticClass:"right"},[a("div",{staticStyle:{"margin-bottom":"5px"}},[a("el-button",{attrs:{size:"small",type:"warning",icon:"el-icon-upload"},on:{click:e.purchaseInvoiceExport}},[e._v("Excel 导出")])],1),a("div",[a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),a("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1)])]),a("el-row",{staticClass:"con"},[a("div",{staticClass:"con-left"},[a("el-table",{key:Math.random(),ref:"lazyTable",staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.dataList,border:"",lazy:"",load:e.load,"row-key":"id",height:e.tableHeight,"tree-props":{hasChildren:"hasChildren"}}},[a("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",align:"left",width:"200px","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"rate",formatter:e.rateFormat,label:"任务时间段",width:"200","show-tooltip-when-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.taskStartTime)+" 至 "+e._s(t.row.taskEndTime))])]}}])}),a("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150px","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"specs",align:"center",label:"规格",width:"80px","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"purchase",align:"center",label:"约定采购量（片/支/粒）","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"totalPurchase",align:"center",label:"累计采购量（片/支/粒）","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"rate",align:"center",formatter:e.rateFormat,label:"完成率","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"totalPrice",align:"center",label:"采购金额(元)","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"totalPayPrice",align:"center",label:"已回款金额(元)","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"totalPayPrice",align:"center",label:"已完成回款比例",formatter:e.ratePayFormat}}),a("el-table-column",{attrs:{align:"center",label:"批次",width:"130","show-tooltip-when-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.formatBatch(t.row.batch)))])]}}])})],1)],1)])],1)},e.staticRenderFns=[]},DKI8:function(t,e,a){"use strict";a("a1oT")},a1oT:function(t,e,a){},dJ3l:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var s=r(a("Q9c5")),o=r(a("DWNM")),n=r(a("rGKd"));function r(t){return t&&t.__esModule?t:{default:t}}e.default={name:"purchaseSchedule",mixins:[o.default],data:function(){return{adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1,areaRegionAdmin:!1,hospitalList:[],batchList:[],rLoading:{},tableHeight:100,dataList:[],regionList:[{code:"",name:"全部"},{code:"440703",name:"蓬江区"},{code:"440704",name:"江海区"},{code:"440705",name:"新会区"},{code:"440781",name:"台山市"},{code:"440783",name:"开平市"},{code:"440784",name:"鹤山市"},{code:"440785",name:"恩平市"}],params:{hospitalId:"",catalogName:"",batch:""}}},props:{},computed:{},watch:{},methods:{purchaseInvoiceExport:function(){var t=this,e=this.$store.getters.curUser.id,a=this.$store.getters.curUser.roleCode;e||this.$message.error("用户未登录"),this.$confirm("确定导出国家集采回款情况数据吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var o=s.default.baseContext+"/supervise/supCountryPurchase/purchaseInvoiceExport?hospitalId="+t.params.hospitalId+"&batch="+t.params.batch+"&catalogName="+t.params.catalogName+"&userId="+e+"&roleValue="+a;t.$get_blob(o).then(function(e){if("application/json"==e.type){var a=new FileReader;return a.addEventListener("loadend",function(e){t.$message.error(JSON.parse(e.target.result).message)}),void a.readAsText(e)}var s=URL.createObjectURL(e),o=document.createElement("a");o.href=s,o.target="_blank",o.download="国家集采回款情况数据.xls",o.click()}).catch(function(t){console.log(t)})}).catch(function(t){console.log(t)})},getHospitalList:function(){var t=this,e=s.default.baseContext+"/supervise/supHospital/all";this.$http_post(e,{}).then(function(e){1==e.state?t.hospitalList=e.row:t.$alert(e.message)})},onSearch:function(t){"reset"==t?(this.params.batch="",this.params.hospitalId="",this.params.catalogName="",this.onQuery()):this.onQuery()},formatBatch:function(t){if(void 0==t||""==t)return"";for(var e=0;e<this.batchList.length;e++)if(this.batchList[e].code==t)return this.batchList[e].batchName},ratePayFormat:function(t){var e=t.totalPayPrice/t.totalPrice;return isNaN(e)||""===e?e=0==t.totalPurchase?"0.00%":"--":(e=Number(100*e).toFixed(2))>100?e="100%":e+="%",e},getTrend:function(t,e,a){var o=this,n={hospitalName:t,batch:this.params.batch,taskStartTime:e,catalogName:this.params.catalogName},r=this.openLoading(),l=s.default.baseContext+"/supervise/supCountryPurchase/getPurchaseList";this.$http_post(l,n).then(function(t){if(1==t.state){if(null!=t.rows)if(null==a||void 0==a){var e=t.rows;o.dataList=e}else a(t.rows);r.close()}else r.close(),o.$alert(t.message)})},load:function(t,e,a){var s=t.hospitalName,o=t.taskStartTime;this.getTrend(s,o,a)},rateFormat:function(t){var e=t.totalPurchase/t.purchase;return isNaN(e)||""===e?e="--":(e=Number(100*e).toFixed(2),e+="%"),e},onQuery:function(){var t=this,e=this.openLoading(),a=s.default.baseContext+"/supervise/supCountryPurchase/getListByHospital";this.$http_post(a,this.params).then(function(a){1==a.state?(t.dataList=a.rows,e.close(),t.expandAnyRow(0)):(e.close(),t.$alert(a.message))})},expandAnyRow:function(t){var e=this;setTimeout(function(){e.$nextTick(function(){e.$refs.lazyTable.$el.querySelectorAll(".el-table__row--level-0")[t].querySelector(".el-table__expand-icon").click()})},500)},setBatchList:function(t){for(var e=t.rows,a=0;a<e.length;a++)this.batchList.push(e[a])},initRole:function(){var t=this.$store.getters.curUser.roleCode;-1!=t.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=t.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=t.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=t.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)}},mounted:function(){var t=this;this.initRole(),this.onQuery(),this.getHospitalList(),n.default.getBatchList(this.setBatchList),this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null},filters:{formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}}}},rGKd:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var s=n(a("Q9c5")),o=n(a("ERIh"));function n(t){return t&&t.__esModule?t:{default:t}}var r=s.default.baseContext+"/supervise/supDrugBatch/getBatchList";var l={getBatchList:function(t){o.default.$http_api("GET",r,{},{jsonContentType:!1,responseType:null,addBaseUrl:!0}).then(function(e){1==e.state?function(t){return null!=t&&void 0!==t&&"function"==typeof t}(t)&&t(e):console.warn("查询集采批次失败",e.message)}).catch(function(t){console.warn("查询集采批次失败",t.message)})}};e.default=l},"wg/Q":function(t,e,a){"use strict";a.r(e);var s=a("6NKM"),o=a("0ZDf");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,function(){return o[t]})}(n);a("DKI8");var r=a("gp09"),l=Object(r.a)(o.default,s.render,s.staticRenderFns,!1,null,"161f3490",null);e.default=l.exports}}]);