(window.webpackJsonp=window.webpackJsonp||[]).push([[46],{AL52:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var n=o(a("Q9c5")),s=o(a("DWNM"));o(a("rGKd"));function o(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[s.default],data:function(){return{batchList:[],editAble:!0,dataList:[],params:{page:1,limit:10,records:0,date:"",name:"",startDate:"",endDate:""},formData:{code:"",id:"",name:"",executeDate:"",executeYear:""},dialogVisible:!1,tableHeight:100,rules:{code:[{required:!0,message:"请选择执行名称",trigger:"blur"}],executeDate:[{required:!0,message:"请选择执行日期",trigger:"blur"}]},initVisible:!1,year:(new Date).getFullYear()+""}},components:{},props:{},computed:{},watch:{},filters:{formatDate:function(t){if(void 0==t||""==t)return"";var e=new Date(t),a=e.getFullYear()+"-",n=(e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-",s=e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ";e.getHours(),e.getHours(),e.getMinutes(),e.getMinutes(),e.getSeconds(),e.getSeconds();return a+n+s}},methods:{initJob:function(){this.initVisible=!0},saveInit:function(){var t=this;this.$alert("确定初始化【"+this.year+"】年的数据吗，初始化会删除当前年已配置数据","确认信息",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,type:"warning"}).then(function(){var e=t.openLoading(),a=n.default.baseContext+"/supervise/supSettlementJob/initJob?year="+t.year;t.$http_post(a,null).then(function(a){1==a.state?(t.$message.success("初始化完成"),t.onQuery(),t.initVisible=!1):t.$alert(a.message),e.close()})}).catch(function(t){console.log(t)})},add:function(t,e){if("add"==t&&(this.formData={code:"",id:"",name:"",executeDate:"",executeYear:""},this.dialogVisible=!0),"edit"==t){var a=new Date(e.executeDate),n=a.getFullYear()+"-",s=(a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-",o=a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ";this.formData={id:e.id,code:e.code,name:e.name,executeDate:n+s+o,executeYear:e.executeYear},this.dialogVisible=!0}},save:function(){var t=this;this.$refs.form.validate(function(e){if(!e)return!1;"5"==t.formData.code&&(t.formData.name="医疗机构对账自动审核"),"6"==t.formData.code&&(t.formData.name="配送企业对账自动复核");var a=t.openLoading(),s=n.default.baseContext+"/supervise/supSettlementJob/save";t.$http_post(s,t.formData,!0).then(function(e){1==e.state?(t.$message.success("添加成功"),t.onQuery(),t.dialogVisible=!1):t.$alert(e.message),a.close()})})},del:function(t){var e=this;this.$alert("确定删除【"+t.name+"】数据吗，删除后则无法自动执行","确认信息",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,type:"warning"}).then(function(){var a=e.openLoading();e.$http_post(n.default.baseContext+"/supervise/supSettlementJob/del/"+t.id,null).then(function(t){1==t.state?(e.$message.success("删除成功"),e.onQuery(),a.close()):(a.close(),e.$message.error("删除失败，请稍后再试"))})}).catch(function(t){console.log(t)})},onSearch:function(t){"reset"==t?(this.params.name="",this.params.date="",this.params.startDate="",this.params.endDate="",this.onQuery()):this.onQuery()},onPageClick:function(t){this.params.page=t,this.onQuery()},onQuery:function(){var t=this;this.params.date&&(this.params.startDate=this.params.date[0],this.params.endDate=this.params.date[1]);var e=this.params,a=this.openLoading(),s=n.default.baseContext+"/supervise/supSettlementJob/list";this.$http_post(s,e).then(function(e){if(1==e.state){var n=e.rows;t.dataList=n,t.params.records=e.records,a.close()}else a.close(),t.$alert(e.message)})}},mounted:function(){var t=this;this.onQuery(),this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null}}},PyBl:function(t,e,a){"use strict";a("ruoB")},jzKJ:function(t,e,a){"use strict";var n=a("vdPh");a.o(n,"render")&&a.d(e,"render",function(){return n.render}),a.o(n,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return n.staticRenderFns})},rGKd:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var n=o(a("Q9c5")),s=o(a("ERIh"));function o(t){return t&&t.__esModule?t:{default:t}}var i=n.default.baseContext+"/supervise/supDrugBatch/getBatchList";var r={getBatchList:function(t){s.default.$http_api("GET",i,{},{jsonContentType:!1,responseType:null,addBaseUrl:!0}).then(function(e){1==e.state?function(t){return null!=t&&void 0!==t&&"function"==typeof t}(t)&&t(e):console.warn("查询集采批次失败",e.message)}).catch(function(t){console.warn("查询集采批次失败",t.message)})}};e.default=r},ruoB:function(t,e,a){},"tT+V":function(t,e,a){"use strict";a.r(e);var n=a("jzKJ"),s=a("yc7m");for(var o in s)["default"].indexOf(o)<0&&function(t){a.d(e,t,function(){return s[t]})}(o);a("PyBl");var i=a("gp09"),r=Object(i.a)(s.default,n.render,n.staticRenderFns,!1,null,"0d11bd6e",null);e.default=r.exports},vdPh:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left flex-row"},[e("span",[t._v("批次日期")]),e("el-date-picker",{staticStyle:{"margin-right":"10px"},attrs:{size:"small","value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:t.params.date,callback:function(e){t.$set(t.params,"date",e)},expression:"params.date"}}),e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1),e("div",{staticClass:"right"},[e("el-button",{attrs:{icon:"el-icon-plus",type:"primary"},on:{click:function(e){return t.initJob()}}},[t._v("初始化全年")]),e("el-button",{attrs:{icon:"el-icon-plus",type:"primary"},on:{click:function(e){return t.add("add")}}},[t._v("新增")])],1)]),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.dataList,"highlight-current-row":"",height:t.tableHeight}},[e("el-table-column",{attrs:{type:"index",label:"序号",width:"50"}}),e("el-table-column",{attrs:{prop:"name",label:"执行环节名称","min-width":"120px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                        "+t._s("5"==e.row.code?"医疗机构对账自动审核":"6"==e.row.code?"配送企业对账自动复核":"")+"\n                    ")]}}])}),e("el-table-column",{attrs:{prop:"executeDate",label:"执行日期","min-width":"120px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                        "+t._s(t._f("formatDate")(e.row.executeDate))+"\n                    ")]}}])}),e("el-table-column",{attrs:{label:"操作",align:"right",width:"200"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.add("edit",a.row)}}},[t._v("编辑")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.del(a.row)}}},[t._v("删除")])]}}])})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)]),e("el-dialog",{attrs:{title:"执行管理",visible:t.dialogVisible,width:"45%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("div",{staticClass:"dialog-content"},[e("el-form",{ref:"form",staticClass:"item-form",attrs:{model:t.formData,"label-width":"90px",rules:t.rules}},[e("div",{staticClass:"fromBox"},[e("el-row",{attrs:{type:"flex"}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"名称",prop:"code"}},[e("el-select",{attrs:{placeholder:"全部"},model:{value:t.formData.code,callback:function(e){t.$set(t.formData,"code",e)},expression:"formData.code"}},[e("el-option",{attrs:{label:"医疗机构对账自动审核",value:"5"}}),e("el-option",{attrs:{label:"配送企业对账自动复核",value:"6"}})],1)],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"执行日期",prop:"executeDate"}},[e("el-date-picker",{attrs:{"value-format":"yyyy-MM-dd",type:"date",placeholder:"选择日期"},model:{value:t.formData.executeDate,callback:function(e){t.$set(t.formData,"executeDate",e)},expression:"formData.executeDate"}})],1)],1)],1)],1)])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("确 定")])],1)]),e("el-dialog",{attrs:{title:"初始化配置",visible:t.initVisible,width:"45%"},on:{"update:visible":function(e){t.initVisible=e}}},[e("div",{staticClass:"dialog-content"},[e("div",{staticClass:"fromBox"},[e("el-row",{attrs:{type:"flex"}},[e("el-col",{attrs:{span:24}},[e("span",{staticStyle:{"margin-right":"10px"}},[t._v("初始化年份")]),e("el-date-picker",{attrs:{type:"year",placeholder:"选择年份"},model:{value:t.year,callback:function(e){t.year=e},expression:"year"}})],1)],1)],1)]),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.initVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.saveInit}},[t._v("确 定")])],1)])],1)},e.staticRenderFns=[]},yc7m:function(t,e,a){"use strict";a.r(e);var n=a("AL52"),s=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,function(){return n[t]})}(o);e.default=s.a}}]);