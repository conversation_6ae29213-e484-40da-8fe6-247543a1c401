(window.webpackJsonp=window.webpackJsonp||[]).push([[19],{"0ga+":function(e,t,a){"use strict";a("FZSB")},"4FEp":function(e,t,a){"use strict";a("Bff2")},"7T3q":function(e,t,a){"use strict";var r=a("Ngc1");a.o(r,"render")&&a.d(t,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return r.staticRenderFns})},Bff2:function(e,t,a){},D4No:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[t("el-tab-pane",{attrs:{label:"角色管理",name:"first"}},["first"==e.activeName?t("RoleList",{attrs:{menuList:e.menu}}):e._e()],1),t("el-tab-pane",{attrs:{label:"菜单管理",name:"second"}},["second"==e.activeName?t("MenuManagement",{attrs:{menuList:e.menu},on:{onUpdate:e.getMenuList}}):e._e()],1)],1)},t.staticRenderFns=[]},FZSB:function(e,t,a){},Fff9:function(e,t,a){"use strict";a.r(t);var r=a("7T3q"),s=a("w2R/");for(var n in s)["default"].indexOf(n)<0&&function(e){a.d(t,e,function(){return s[e]})}(n);a("4FEp");var o=a("gp09"),i=Object(o.a)(s.default,r.render,r.staticRenderFns,!1,null,"bb8ac610",null);t.default=i.exports},GMAw:function(e,t,a){"use strict";var r=a("Sn8c");a.o(r,"render")&&a.d(t,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return r.staticRenderFns})},Giqt:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=n(a("Q9c5")),s=n(a("DWNM"));function n(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[s.default],name:"menuManagement",data:function(){return{searchName:"",tableData:[],tableHeight:370,selected:0,disabledPath:!1,formData:{id:"",name:"",type:"1",path:"",icon:"",parentId:"#",sortOrder:1,status:"0",remark:""},rules:{name:[{required:!0,message:"请输入资源名称",trigger:["blur","change"]}],path:[{required:!0,message:"请输入资源路径",trigger:["blur","change"]}]},dialogVisible:!1,superiorBox:!1,pagination:{total:0,limit:10,page:1},parentId:"",superiorId:"",superiorName:""}},props:{menuList:{type:Array,default:function(){return[]}}},mounted:function(){var e=this;this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-45}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-45},this.getList()},methods:{add:function(){this.formData={id:"",name:"",type:"1",path:"",icon:"",parentId:"#",sortOrder:1,status:"0",remark:""},this.dialogVisible=!0},getList:function(){var e=this,t={parentId:this.parentId,name:this.searchName,page:this.pagination.page,limit:this.pagination.limit},a=this.openLoading();this.$http_post(r.default.baseContext+"/bsp/pubMenu/query",t).then(function(t){1==t.state?(null!=t.rows?(e.tableData=t.rows,e.pagination.total=t.records):e.$message.error("系统异常"),a.close()):(a.close(),e.$alert(t.message))})},onClick:function(e,t,a){this.parentId=e.id,this.getList()},search:function(e){"reset"==e?(this.searchName="",this.$refs.menuTree.setCurrentKey(null),this.parentId="",this.getList()):""!=this.searchName?(this.pagination.page=1,this.getList()):this.$message.warning("请输入资源名称查询")},onPageClick:function(e){this.pagination.page=e,this.getList()},sourceType:function(e){0==e?(this.formData.path="#",this.disabledPath=!0):this.disabledPath=!1},edit:function(e){var t=this,a=this.openLoading();this.$http_post(r.default.baseContext+"/bsp/pubMenu/show",{id:e}).then(function(e){if(1==e.state){if(null!=e.row){var r=e.row;t.formData={id:r.id,name:r.name,type:r.type,path:r.path,icon:r.icon,parentId:r.parentId,parentName:t.recursive(r.parentId,t.menuList),sortOrder:r.sortOrder,status:r.status,remark:r.remark},t.dialogVisible=!0}else t.$message.error("系统异常");a.close()}else a.close(),t.$message.error(e.message)})},remove:function(e){var t=this;this.$confirm("确定删除【"+e.name+"】吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var a=t.openLoading("删除中...");t.$http_post(r.default.baseContext+"/bsp/pubMenu/delete",{id:e.id}).then(function(e){1==e.state?(t.getList(),t.$emit("onUpdate","delete"),t.$message.success("删除成功"),a.close()):(a.close(),t.$message.error(e.message))})}).catch(function(e){console.log(e)})},save:function(){var e=this;this.$refs.ruleForm.validate(function(t){if(!t)return!1;var a=e.openLoading("删除中..."),s={name:e.formData.name,type:e.formData.type,path:e.formData.path,icon:e.formData.icon,parentId:e.formData.parentId,sortOrder:e.formData.sortOrder,status:e.formData.status,remark:e.formData.remark},n="/bsp/pubMenu/save";null!=e.formData.id&&""!=e.formData.id&&(n="/bsp/pubMenu/update",s.id=e.formData.id),e.$http_post(r.default.baseContext+n,s).then(function(t){1==t.state?(e.dialogVisible=!1,e.getList(),e.$emit("onUpdate","save"),-1!=n.indexOf("update")?e.$message.success("编辑成功"):e.$message.success("添加成功"),a.close()):(a.close(),e.$message.error(t.message))})})},selectSuperior:function(){this.superiorBox=!0},onSuperior:function(e,t,a){this.superiorId=e.id,this.superiorName=e.name},saveSuperior:function(){this.formData.parentId=this.superiorId,this.formData.parentName=this.superiorName,this.superiorBox=!1},recursive:function(e,t){for(var a=0;a<t.length;a++){if(e==t[a].id)return t[a].name;if(t[a].children){var r=this.recursive(e,t[a].children);if(r)return r}}}},computed:{},beforeDestroy:function(){window.onresize=null}}},Ngc1:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"menu-management"},[t("div",{staticClass:"top flex-row"},[t("div",{staticClass:"flex-row left"},[t("span",[e._v("资源名称")]),t("el-input",{attrs:{placeholder:"请输入资源名称"},model:{value:e.searchName,callback:function(t){e.searchName=t},expression:"searchName"}}),t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),t("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(t){return e.search("reset")}}},[e._v("重置")])],1),t("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:e.add}},[e._v("新增")])],1),t("div",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-card",{staticClass:"box-card",attrs:{shadow:"never"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("应用系统资源树")])]),t("div",{staticClass:"text item"},[t("el-tree",{ref:"menuTree",attrs:{data:e.menuList,"expand-on-click-node":!1,"check-on-click-node":!0,"default-expand-all":"","node-key":"id","highlight-current":!0,props:{children:"children",label:"name"}},on:{"node-click":e.onClick},scopedSlots:e._u([{key:"default",fn:function(a){var r=a.node;return a.data,t("span",{staticClass:"custom-tree-node"},[t("span",[e._v(e._s(r.label))])])}}])})],1)])],1),t("div",{staticClass:"con-right"},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,height:e.tableHeight}},[t("el-table-column",{attrs:{prop:"icon",label:"资源图标"}}),t("el-table-column",{attrs:{prop:"name",label:"资源名称"}}),t("el-table-column",{attrs:{prop:"path",label:"资源路径"}}),t("el-table-column",{attrs:{width:"100",prop:"sortOrder",label:"排序"}}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.edit(a.row.id)}}},[e._v("编辑")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.remove(a.row)}}},[e._v("删除")])]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.pagination.total,"page-size":e.pagination.limit,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)]),t("el-dialog",{attrs:{title:""!=e.formData.id?"编辑菜单":"新增菜单",visible:e.dialogVisible,width:"50%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",{staticClass:"dialog-content"},[e.dialogVisible?t("el-form",{ref:"ruleForm",staticClass:"item-form",attrs:{model:e.formData,rules:e.rules,"label-position":"left","label-width":"90px"}},[t("el-row",{attrs:{type:"flex"}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"资源名称",prop:"name"}},[t("el-input",{attrs:{placeholder:"请输入资源名称"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1)],1),t("el-col",{staticClass:"col-right",attrs:{span:12}},[t("el-form-item",{attrs:{label:"资源图标"}},[t("el-input",{attrs:{placeholder:"请选择资源图标"},model:{value:e.formData.icon,callback:function(t){e.$set(e.formData,"icon",t)},expression:"formData.icon"}},[t("template",{slot:"append"},[t("div",{staticClass:"combination-right"},[t("i",{staticClass:"el-icon-picture-outline"})])])],2)],1)],1)],1),t("el-row",{attrs:{type:"flex"}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"资源类型"}},[t("el-radio-group",{on:{change:e.sourceType},model:{value:e.formData.type,callback:function(t){e.$set(e.formData,"type",t)},expression:"formData.type"}},[t("el-radio",{attrs:{label:"0"}},[e._v("目录")]),t("el-radio",{attrs:{label:"1"}},[e._v("菜单")])],1)],1)],1),t("el-col",{staticClass:"col-right",attrs:{span:12}},[t("el-form-item",{attrs:{label:"上级资源"}},[t("el-input",{attrs:{disabled:"",placeholder:"请选择上级资源"},model:{value:e.formData.parentName,callback:function(t){e.$set(e.formData,"parentName",t)},expression:"formData.parentName"}},[t("template",{slot:"append"},[t("div",{staticClass:"combination-right",on:{click:e.selectSuperior}},[t("i",{staticClass:"el-icon-position"})])])],2)],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"资源路径",prop:"path"}},[t("el-input",{attrs:{disabled:e.disabledPath,placeholder:"请输入菜单路径，最顶层为(#)号"},model:{value:e.formData.path,callback:function(t){e.$set(e.formData,"path",t)},expression:"formData.path"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"状态"}},[t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},model:{value:e.formData.status,callback:function(t){e.$set(e.formData,"status",t)},expression:"formData.status"}})],1)],1),t("el-col",{staticClass:"col-right",attrs:{span:12}},[t("el-form-item",{attrs:{label:"排序"}},[t("el-input-number",{attrs:{min:1,max:100},model:{value:e.formData.sortOrder,callback:function(t){e.$set(e.formData,"sortOrder",t)},expression:"formData.sortOrder"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"备注"}},[t("el-input",{attrs:{type:"textarea",autosize:{minRows:3,maxRows:6}},model:{value:e.formData.remark,callback:function(t){e.$set(e.formData,"remark",t)},expression:"formData.remark"}})],1)],1)],1)],1):e._e()],1),t("el-dialog",{staticClass:"superior-box",attrs:{width:"30%",title:"选择上级资源",visible:e.superiorBox,"append-to-body":""},on:{"update:visible":function(t){e.superiorBox=t}}},[t("el-tree",{attrs:{data:e.menuList,"expand-on-click-node":!1,"check-on-click-node":!0,"default-expand-all":"","node-key":"id","highlight-current":!0,props:{children:"children",label:"name"}},on:{"node-click":e.onSuperior},scopedSlots:e._u([{key:"default",fn:function(a){var r=a.node;return a.data,t("span",{staticClass:"custom-tree-node"},[t("span",[e._v(e._s(r.label))])])}}])}),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.superiorBox=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.saveSuperior}},[e._v("确 定")])],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确 定")])],1)],1)],1)},t.staticRenderFns=[]},RNh7:function(e,t,a){},Sn8c:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"},[t("span",[e._v("角色名称")]),t("el-input",{attrs:{placeholder:"请输入角色名称"},model:{value:e.params.roleName,callback:function(t){e.$set(e.params,"roleName",t)},expression:"params.roleName"}}),t("span",[e._v("角色编码")]),t("el-input",{attrs:{placeholder:"请输入角色编码"},model:{value:e.params.roleCode,callback:function(t){e.$set(e.params,"roleCode",t)},expression:"params.roleCode"}}),t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1),t("el-button",{attrs:{icon:"el-icon-plus",type:"primary"},on:{click:function(t){return e.add("add")}}},[e._v("新增")])],1),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.dataList,"highlight-current-row":"",height:e.tableHeight},on:{"current-change":e.handleCurrentChange}},[t("el-table-column",{attrs:{prop:"roleName",label:"角色名"}}),t("el-table-column",{attrs:{prop:"roleCode",label:"角色值"}}),t("el-table-column",{attrs:{width:"100",prop:"roleWeight",label:"权重",align:"center"}}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.add("edit",a.row)}}},[e._v("编辑")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.del(a.row)}}},[e._v("删除")])]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1),t("div",{staticClass:"con-right"},[t("el-card",{staticClass:"box-card",attrs:{shadow:"never"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("菜单列表")]),t("el-button",{attrs:{type:"text"},on:{click:e.saveMenu}},[e._v("保存")])],1),t("div",{staticClass:"text item"},[t("el-tree",{ref:"menuTree",attrs:{data:e.menuList,"expand-on-click-node":!1,"check-on-click-node":!0,"show-checkbox":"","default-expand-all":"","node-key":"id","default-checked-keys":e.currentList,props:{children:"children",label:"name"}}})],1)])],1)]),t("el-dialog",{attrs:{title:"角色",visible:e.dialogVisible,width:"45%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",{staticClass:"dialog-content"},[t("el-form",{staticClass:"item-form",attrs:{model:e.formData,"label-width":"90px"}},[t("el-row",{attrs:{type:"flex"}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"角色名称"}},[t("el-input",{attrs:{placeholder:"请输入角色名称"},model:{value:e.formData.roleName,callback:function(t){e.$set(e.formData,"roleName",t)},expression:"formData.roleName"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"角色编码"}},[t("el-input",{attrs:{placeholder:"请输入角色编码"},model:{value:e.formData.roleCode,callback:function(t){e.$set(e.formData,"roleCode",t)},expression:"formData.roleCode"}})],1)],1)],1),t("el-row",{attrs:{type:"flex"}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"角色权重"}},[t("el-input",{attrs:{placeholder:"请输入角色权重"},model:{value:e.formData.roleWeight,callback:function(t){e.$set(e.formData,"roleWeight",t)},expression:"formData.roleWeight"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"排序"}},[t("el-input-number",{attrs:{min:1,max:100},model:{value:e.formData.sortOrder,callback:function(t){e.$set(e.formData,"sortOrder",t)},expression:"formData.sortOrder"}})],1)],1)],1)],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确 定")])],1)])],1)},t.staticRenderFns=[]},YVAF:function(e,t,a){"use strict";var r=a("D4No");a.o(r,"render")&&a.d(t,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return r.staticRenderFns})},bF75:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=i(a("cTNI")),s=i(a("Fff9")),n=i(a("Q9c5")),o=i(a("DWNM"));function i(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[o.default],components:{RoleList:r.default,MenuManagement:s.default},data:function(){return{activeName:"first",menu:[]}},mounted:function(){this.getMenuList()},methods:{handleClick:function(e,t){},getMenuList:function(){var e=this,t=this.openLoading();this.$http_post(n.default.baseContext+"/bsp/pubMenu/list",{}).then(function(a){if(1==a.state){if(null!=a.rows){var r=e.simpleToTree(a.rows,"id","parentId","#","children");e.menu=r}else e.$message.error("系统异常，请重试");t.close()}else t.close(),null!=a.message?e.$message.error(a.message):e.$message.error("系统异常，请重试")})}}}},cTNI:function(e,t,a){"use strict";a.r(t);var r=a("GMAw"),s=a("m8Lr");for(var n in s)["default"].indexOf(n)<0&&function(e){a.d(t,e,function(){return s[e]})}(n);a("oQj9");var o=a("gp09"),i=Object(o.a)(s.default,r.render,r.staticRenderFns,!1,null,"f88f45f4",null);t.default=i.exports},li61:function(e,t,a){"use strict";a.r(t);var r=a("YVAF"),s=a("qBb0");for(var n in s)["default"].indexOf(n)<0&&function(e){a.d(t,e,function(){return s[e]})}(n);a("0ga+");var o=a("gp09"),i=Object(o.a)(s.default,r.render,r.staticRenderFns,!1,null,"2796a3f4",null);t.default=i.exports},m8Lr:function(e,t,a){"use strict";a.r(t);var r=a("wtpK"),s=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,function(){return r[e]})}(n);t.default=s.a},oQj9:function(e,t,a){"use strict";a("RNh7")},qBb0:function(e,t,a){"use strict";a.r(t);var r=a("bF75"),s=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,function(){return r[e]})}(n);t.default=s.a},"w2R/":function(e,t,a){"use strict";a.r(t);var r=a("Giqt"),s=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,function(){return r[e]})}(n);t.default=s.a},wtpK:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=n(a("Q9c5")),s=n(a("DWNM"));function n(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[s.default],data:function(){return{dataList:[],params:{page:1,limit:10,records:0,roleCode:"",roleName:""},currentRow:"",formData:{id:"",roleCode:"",roleName:"",roleWeight:"",sortOrder:1},dialogVisible:!1,tableHeight:100,currentList:[]}},props:{menuList:{type:Array,default:function(){return[]}}},computed:{},watch:{},methods:{add:function(e,t){var a=this;if("add"==e&&(this.formData={roleCode:"",roleName:"",roleWeight:"",sortOrder:1},this.dialogVisible=!0),"edit"==e){var s=this.openLoading();this.$http_post(r.default.baseContext+"/bsp/pubRole/show",{id:t.id}).then(function(e){1==e.state?null!=e.row?(a.formData={id:e.row.id,roleCode:e.row.roleCode,roleName:e.row.roleName,roleWeight:e.row.roleWeight,sortOrder:e.row.sortOrder},a.dialogVisible=!0):a.$message.error("系统异常"):null!=e.message?a.$message.error(e.message):a.$message.error("系统异常"),s.close()})}},save:function(){var e=this,t="",a={code:this.formData.roleCode};null!=this.formData.id&&""!=this.formData.id?(a.id=this.formData.id,t=r.default.baseContext+"/bsp/pubRole/update"):t=r.default.baseContext+"/bsp/pubRole/save";var s=this.openLoading();this.$http_post(r.default.baseContext+"/bsp/pubRole/checkCode",a).then(function(a){1==a.state?e.$http_post(t,e.formData).then(function(a){1==a.state?(-1!=t.indexOf("update")?e.$message.success("修改成功"):e.$message.success("添加成功"),e.onQuery(),e.dialogVisible=!1):e.$alert(a.message),s.close()}):e.$alert(a.message),s.close()})},del:function(e){var t=this;this.$alert("确定删除【"+e.roleName+"】数据吗","确认信息",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,type:"warning"}).then(function(){var a=t.openLoading();t.$http_post(r.default.baseContext+"/bsp/pubRole/delete",{id:e.id}).then(function(e){1==e.state?(t.$message.success("删除成功"),t.onQuery(),a.close()):(a.close(),t.$message.error("删除失败，请稍后再试"))})}).catch(function(e){console.log(e)})},handleCurrentChange:function(e){var t=this;if(null!=e){this.currentRow=e,this.$refs.menuTree.setCheckedKeys([]);var a=this.openLoading();this.$http_post(r.default.baseContext+"/bsp/pubMenu/listByRole",{roleId:e.id}).then(function(e){if(1==e.state){if(null!=e.rows){for(var r=[],s=0,n=e.rows.length;s<n;s++)r.push(e.rows[s].menuId);t.currentList=r}else t.$message.error("系统异常，请重试");a.close()}else a.close(),null!=e.message?t.$message.error(e.message):t.$message.error("系统异常，请重试")})}},saveMenu:function(){var e=this;if(""!=this.currentRow&&null!=this.currentRow){var t=this.$refs.menuTree.getCheckedKeys().join(","),a=this.openLoading("保存中...");this.$http_post(r.default.baseContext+"/bsp/pubMenu/saveMenuRole",{roleId:this.currentRow.id,menuIds:t}).then(function(t){1==t.state?(e.$message.success("保存成功"),a.close()):(a.close(),null!=t.message?e.$message.error(t.message):e.$message.error("系统异常，请重试"))})}else this.$message.error("请先选择左侧角色")},onSearch:function(e){"reset"==e?(this.params.roleCode="",this.params.roleName="",this.onQuery()):""!=this.params.roleCode||""!=this.params.roleName?(this.params.page=1,this.onQuery()):this.$message.warning("请输入角色名称或角色编码查询")},onPageClick:function(e){this.params.page=e,this.onQuery()},onQuery:function(){var e=this,t=this.params;this.isLink&&(t.isLink="0");var a=this.openLoading(),s=r.default.baseContext+"/bsp/pubRole/query";this.$http_post(s,t).then(function(t){if(1==t.state){e.currentRow="",e.$refs.menuTree.setCheckedKeys([]);var r=t.rows;e.dataList=r,e.params.records=t.records,a.close()}else a.close(),e.$alert(t.message)})}},mounted:function(){var e=this;this.onQuery(),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null}}}}]);