(window.webpackJsonp=window.webpackJsonp||[]).push([[16],{"1DhX":function(t,e,a){},"1KJN":function(t,e,a){},"2Fsv":function(t,e,a){},"48vQ":function(t,e,a){"use strict";a.r(e);var n=a("r3Vc"),s=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,function(){return n[t]})}(o);e.default=s.a},"79BE":function(t,e,a){"use strict";a.r(e);var n=a("dGdr"),s=a("ILGX");for(var o in s)["default"].indexOf(o)<0&&function(t){a.d(e,t,function(){return s[t]})}(o);a("WP+m");var r=a("gp09"),i=Object(r.a)(s.default,n.render,n.staticRenderFns,!1,null,"d72be3d6",null);e.default=i.exports},E1wh:function(t,e,a){"use strict";a("zzNX")},ILGX:function(t,e,a){"use strict";a.r(e);var n=a("pG0o"),s=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,function(){return n[t]})}(o);e.default=s.a},K3F5:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left flex-row"}),e("div",{staticClass:"right"},["todo"==this.$route.query.type?e("el-button",{attrs:{type:"success",icon:"el-icon-check"},on:{click:function(e){return t.submit()}}},[t._v("提交\n            ")]):t._e(),e("el-button",{attrs:{type:"info",icon:"el-icon-arrow-left"},on:{click:t.goBack}},[t._v("返回\n            ")])],1)]),e("el-tabs",{model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[e("el-tab-pane",{attrs:{label:"结算信息",name:"first"}})],1),e("div",{staticClass:"form"},[e("el-form",{ref:"dataForm",staticClass:"item-form",attrs:{model:t.settlementData.data,"label-position":"left","label-width":"110px"}},[e("div",{staticClass:"fromBox"},[e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"结算编号"}},[e("el-input",{attrs:{value:t.settlementData.data.num}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"创建人"}},[e("el-input",{attrs:{value:t.settlementData.data.createName}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"生成时间"}},[e("el-input",{attrs:{value:t._f("formatTime")(t.settlementData.data.creationTime)}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"业务状态"}},["1"==t.settlementData.data.bizStatus?e("el-input",{attrs:{value:"在办"}}):t._e(),"2"==t.settlementData.data.bizStatus?e("el-input",{attrs:{value:"结算单阶段完成"}}):t._e(),"3"==t.settlementData.data.bizStatus?e("el-input",{attrs:{value:"退回办结"}}):t._e()],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"结算总笔数"}},[e("el-input",{attrs:{value:t.settlementData.data.count}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"结算总金额"}},[e("el-input",{attrs:{value:t.settlementData.data.totalPrice}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"打回笔数"}},[e("el-input",{attrs:{value:t.settlementData.data.backCount}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"打回总金额"}},[e("el-input",{attrs:{value:t.settlementData.data.backPrice}})],1)],1)],1)],1)])],1),e("div",{staticClass:"memberTab"},[e("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.curTab,callback:function(e){t.curTab=e},expression:"curTab"}},[e("el-tab-pane",{attrs:{label:"结算明细信息"}},[void 0!=t.settlementData.items?e("item-detail",{ref:"settlementItem",attrs:{name:"0",itemArr:t.settlementData.items}}):t._e()],1),e("el-tab-pane",{attrs:{label:"流程信息"}},[void 0!=t.settlementData.courses?e("process",{ref:"process",attrs:{name:"1",processData:t.settlementData.courses}}):t._e()],1)],1)],1),t.opinionShow?e("el-dialog",{attrs:{title:"审核意见",visible:t.opinionShow,width:"50%"},on:{"update:visible":function(e){t.opinionShow=e}}},[e("div",{staticClass:"orderBox-dialog-content"},[e("el-form",{ref:"opinionDataForm",staticClass:"item-form",attrs:{id:"opinionData",model:t.opinionDataForm,"label-width":"90px"}},[e("div",{staticClass:"fromBox"},[e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"是否通过"}},[e("el-radio-group",{model:{value:t.opinionDataForm.status,callback:function(e){t.$set(t.opinionDataForm,"status",e)},expression:"opinionDataForm.status"}},[e("el-radio",{attrs:{label:"1"}},[t._v("通过")]),e("el-radio",{attrs:{label:"2"}},[t._v("不通过")])],1)],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"意见"}},[e("el-input",{attrs:{type:"textarea",placeholder:"请输入意见"},model:{value:t.opinionDataForm.opinion,callback:function(e){t.$set(t.opinionDataForm,"opinion",e)},expression:"opinionDataForm.opinion"}})],1)],1)],1)],1)])],1),e("div",{staticClass:"dialog-footer flex-row"},[e("span"),e("span",[e("el-button",{on:{click:function(e){t.opinionShow=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.confirmOpinion()}}},[t._v("确 定")])],1)])]):t._e()],1)},e.staticRenderFns=[]},KB2I:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var n=o(a("DWNM")),s=o(a("Q9c5"));function o(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[n.default],name:"settlementItem-list",data:function(){return{warningData:[],drugSourceList:[],orderItemId:"",itemCourseShow:!1,itemCourseArr:[]}},props:{itemArr:{type:Array,default:function(){return[]}}},mounted:function(){this.getDictItem("SOURCE"),this.getDictItem("WARNING")},components:{},methods:{itemCourse:function(t){var e=this,a=this.openLoading();this.$http_get(s.default.baseContext+"/supervise/supSettlement/itemCourse/"+t.id).then(function(t){t.rows;1==t.state?(e.itemCourseShow=!0,e.itemCourseArr=t.rows,a.close()):(a.close(),e.$message.error(t.message))})},getWaringType:function(t){for(var e=0;e<this.warningData.length;e++)if(this.warningData[e].value==t)return this.warningData[e].label},getWaring:function(t){for(var e=[],a=t.split(","),n=0;n<a.length;n++){var s=this.getWaringType(a[n]);s&&e.push({name:s})}return e},getPrice:function(t,e){if(t){var a=t.match(/\d+/g);return e?(e/a.join("")).toFixed(3):""}return""},getDictItemName:function(t){for(var e=0;e<this.drugSourceList.length;e++)if(this.drugSourceList[e].value==t)return this.drugSourceList[e].label},getDictItem:function(t){var e=this,a=this.openLoading();this.$http_post(s.default.baseContext+"/supervise/supDictItem/getItemList",{code:t}).then(function(n){var s=n.rows;1==n.state?("SOURCE"==t&&(e.drugSourceList=s),"WARNING"==t&&(e.warningData=s),a.close()):(a.close(),e.$message.error(n.message))})},close:function(){this.$refs.asyncDialog.dialogVisible=!1}},filters:{formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}}}},M7Vz:function(t,e,a){"use strict";a.r(e);var n=a("cenf"),s=a("x59a");for(var o in s)["default"].indexOf(o)<0&&function(t){a.d(e,t,function(){return s[t]})}(o);a("YuYJ");var r=a("gp09"),i=Object(r.a)(s.default,n.render,n.staticRenderFns,!1,null,"76d4cc20",null);e.default=i.exports},RUFv:function(t,e,a){"use strict";a.r(e);var n=a("UCDD"),s=a("48vQ");for(var o in s)["default"].indexOf(o)<0&&function(t){a.d(e,t,function(){return s[t]})}(o);a("E1wh"),a("Wdxc");var r=a("gp09"),i=Object(r.a)(s.default,n.render,n.staticRenderFns,!1,null,"215df5f7",null);e.default=i.exports},UCDD:function(t,e,a){"use strict";var n=a("ltVK");a.o(n,"render")&&a.d(e,"render",function(){return n.render}),a.o(n,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return n.staticRenderFns})},"WP+m":function(t,e,a){"use strict";a("1KJN")},Wdxc:function(t,e,a){"use strict";a("1DhX")},YuYJ:function(t,e,a){"use strict";a("2Fsv")},cenf:function(t,e,a){"use strict";var n=a("j9r/");a.o(n,"render")&&a.d(e,"render",function(){return n.render}),a.o(n,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return n.staticRenderFns})},dGdr:function(t,e,a){"use strict";var n=a("K3F5");a.o(n,"render")&&a.d(e,"render",function(){return n.render}),a.o(n,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return n.staticRenderFns})},"j9r/":function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"item-item"},[e("div",{staticClass:"flex-row-default"},[e("div",{staticClass:"box-card box-right"},[e("div",{staticClass:"text item"},[e("el-table",{ref:"data_table",staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.itemArr,"header-cell-style":{background:"#f5f7fa"},border:""}},[e("el-table-column",{attrs:{type:"index",label:"序号",width:"50"}}),e("el-table-column",{attrs:{prop:"itemNum",label:"明细编码",width:"200"}}),e("el-table-column",{attrs:{prop:"invoiceCode",label:"发票代码",width:"120"}}),e("el-table-column",{attrs:{prop:"invoiceNo",label:"发票号",width:"120"}}),e("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"180",align:"left"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",[t._v("\n                                "+t._s(t.getDictItemName(a.row.source))+"\n                            ")])]}}])}),e("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单号",width:"120"}}),e("el-table-column",{attrs:{prop:"deliveryName",label:"配送企业",width:"180"}}),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"150"}}),e("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150"}}),e("el-table-column",{attrs:{prop:"num",label:"开票数量"}}),e("el-table-column",{attrs:{prop:"unitPrice",label:"单价"}}),e("el-table-column",{attrs:{prop:"taxesAmount",label:"金额"}}),e("el-table-column",{attrs:{prop:"orderNum",label:"订单编号",width:"120"}}),e("el-table-column",{attrs:{prop:"country",label:"来源",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                            "+t._s("1"==e.row.country?"国家集采":"2"==e.row.country?"非国家集采":"线下采购")+"\n                        ")]}}])}),e("el-table-column",{attrs:{label:"超时天数",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                            "+t._s(null==e.row.overDay?30:-e.row.overDay)+"\n                        ")]}}])}),e("el-table-column",{attrs:{label:"已通知次数",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                            "+t._s(null==e.row.noticeNum?0:e.row.noticeNum)+"\n                        ")]}}])}),e("el-table-column",{attrs:{label:"详情状态",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.status?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("在办")])],1):t._e(),"2"==a.row.status?e("div",[e("el-tag",{attrs:{type:"primary"}},[t._v("办结")])],1):t._e(),"3"==a.row.status?e("div",[e("el-tag",{attrs:{type:"error"}},[t._v("退回")])],1):t._e()]}}])}),e("el-table-column",{attrs:{label:"明细日志",fixed:"right","min-width":"80px"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{size:"small"},on:{click:function(e){return t.itemCourse(a.row)}}},[t._v("查看")])]}}])})],1)],1)])]),t.itemCourseShow?e("el-dialog",{attrs:{title:"明细日志",visible:t.itemCourseShow,width:"50%"},on:{"update:visible":function(e){t.itemCourseShow=e}}},[e("div",{staticClass:"orderBox-dialog-content"},[e("el-table",{ref:"data_table",staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.itemCourseArr,"header-cell-style":{background:"#f5f7fa"},border:""}},[e("el-table-column",{attrs:{type:"index",label:"序号",width:"50"}}),e("el-table-column",{attrs:{prop:"curNodeName",label:"环节名称"}}),e("el-table-column",{attrs:{prop:"creationTime",label:"操作时间"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t._f("formatTime")(a.row.creationTime)))])]}}],null,!1,3080248501)}),e("el-table-column",{attrs:{prop:"status",label:"审批状态"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.status?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("通过")])],1):t._e(),"1"!=a.row.status?e("div",[e("el-tag",{attrs:{type:"error"}},[t._v("不通过")])],1):t._e()]}}],null,!1,3016323678)}),e("el-table-column",{attrs:{prop:"userName",label:"审批人"}}),e("el-table-column",{attrs:{prop:"opinion",label:"审批意见"}})],1)],1),e("div",{staticClass:"dialog-footer flex-row"},[e("span"),e("span",[e("el-button",{on:{click:function(e){t.itemCourseShow=!1}}},[t._v("取 消")])],1)])]):t._e()],1)},e.staticRenderFns=[]},ltVK:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{staticClass:"view"},[e("h2",[t._v("过程意见")]),e("div",{staticClass:"block process"},[e("el-timeline",t._l(t.processData,function(a,n){return e("el-timeline-item",{key:n,attrs:{placement:"top"}},[e("div",{staticClass:"card-div",staticStyle:{"background-color":"rgb(244 247 250)","padding-top":"10px"}},[e("h3",{staticStyle:{"margin-left":"10px"}},[e("span",[t._v("环节名称:"+t._s(t._f("nodeSplit")(n)))])]),e("el-row",t._l(a,function(a,n){return e("el-col",{key:n,attrs:{span:8}},[e("el-card",[e("h3",[e("i",{staticClass:"iconfont icon-renyuan"}),e("span",[t._v(t._s(a.userName))])]),e("span",[e("i"),t._v("接收时间："+t._s(t._f("formatTime")(a.receiveTime)))]),e("span",[e("i"),t._v("办理时间："+t._s(t._f("formatTime")(a.sendTime)))]),e("p",[t._v(t._s(a.opinion))])])],1)}),1)],1)])}),1)],1)])},e.staticRenderFns=[]},pG0o:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var n=l(a("omC7")),s=l(a("M7Vz")),o=l(a("RUFv")),r=l(a("Q9c5")),i=l(a("DWNM"));function l(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[i.default],name:"settlementDetail",data:function(){return{activeName:"first",curTab:"0",dialogVisible:!1,settlementData:{data:{},items:[]},listLoading:!1,tableHeight:100,opinionShow:!1,opinionDataForm:{status:"",opinion:""},submitDataForm:{data:{},status:"",opinion:""}}},components:{itemDetail:s.default,process:o.default},filters:{formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}},watch:{},mounted:function(){this.$route.query.settlementId&&this.getSettlementDetail()},methods:{confirmOpinion:function(){var t=this;if(this.opinionDataForm.status)if("2"!=this.opinionDataForm.status||this.opinionDataForm.opinion){console.log(this.opinionDataForm),this.submitDataForm.data=JSON.parse((0,n.default)(this.settlementData.data)),this.submitDataForm.opinion=this.opinionDataForm.opinion,this.submitDataForm.status=this.opinionDataForm.status;var e=this.openLoading("查询中");if("2"==this.opinionDataForm.status){var a=r.default.baseContext+"/supervise/supSettlement/acceptFail";this.$http_post(a,this.submitDataForm,!0).then(function(a){1==a.state?(t.$message({type:"success",message:"提交成功！"}),e.close(),t.$router.push({name:"settlementTodo"})):(e.close(),t.$alert(a.message))})}else{a=r.default.baseContext+"/supervise/supSettlement/nextData";this.$http_post(a,this.submitDataForm,!0).then(function(a){1==a.state?(t.$message({type:"success",message:"提交成功！"}),e.close(),t.$router.push({name:"settlementTodo"})):(e.close(),t.$alert(a.message))})}}else this.$message({type:"error",message:"不通过请填写意见！"});else this.$message({type:"error",message:"请选择是否通过！"})},submit:function(){this.opinionShow=!0},goBack:function(){this.$router.go(-1)},handleClick:function(t,e){this.curTab=t.index},getSettlementDetail:function(){var t=this,e=this.openLoading("查询中"),a=r.default.baseContext+"/supervise/supSettlement/show/"+this.$route.query.settlementId;this.$http_post(a,{}).then(function(a){1==a.state?(t.settlementData=a.row,console.log(t.settlementData),e.close()):(e.close(),t.$alert(a.message))})}}}},r3Vc:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default={name:"process",components:{},data:function(){return{}},computed:{},props:{processData:{type:Object,required:!0}},watch:{processData:function(t,e){console.log("----",t)}},methods:{},filters:{nodeSplit:function(t){return t.split(",")[1]},formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}}}},x59a:function(t,e,a){"use strict";a.r(e);var n=a("KB2I"),s=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,function(){return n[t]})}(o);e.default=s.a},zzNX:function(t,e,a){}}]);