<template>
	<div class="headBar flex flex-justify-between lc-box-shadwB">
		<div class="headBox flex flex-justify-between">
			<div class="flex flex-align-center">
				<b class="head-log"></b>
				<span class="s-fts1 lc-font-w s-ftc3">深圳市自助终端<span v-if="isDemo">-测试环境</span></span>
			</div>
			<div class="headBar-r2" v-if="this.$route.meta.isMachineCodePage!=true">
				<p><span>{{device.name}}</span></p>
				<div class="flex flex-justify-between">
					<p><span>设备编号</span><!--A10001-->{{device.code}}</p>
					<p><span>版本号</span><!--v1.0.0-->{{device.openFlag}}</p>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
	import * as Cookies from "js-cookie"
	import base from '@_src/core/base';
	export default {
		mixins: [base],
		data() {
			return {
				isDemo: true,
				device:{},
			}
		},
		computed:{
			haveMsg() {
				if (this.$store.getters['personCentre/newMessageCount'] > 0) {
					return true;
				} else {
					return false;
				}
			},
			personOrHome() {
				let data = this.$store.getters['personCentre/personOrHome'];
				return data;
			},
			isSuspension() {
				let noticeInfo = this.$store.getters.getNotice
				if (noticeInfo != null && noticeInfo.isSuspension == true) {
					return true
				} else {
					return false
				}
			},
			isLogin() {
				let userData = {...this.$store.getters.userInfo}
				if (userData != null && userData.isLogin == true) {
					return true
				} else {
					return false
				}
			},
		},
		mounted(){
			var deviceData = Cookies.get("deviceData");
			var deviceObj = JSON.parse(deviceData);
			setTimeout(() => {
				this.getDeviceData(deviceObj.machineCode);
			}, 500);
		},
		methods: {
			getDeviceData(machineCode){
				this.$http_post(this.Config.baseContext + "/cms/cmsDevice/handleMachineCode",{machineCode: machineCode}).then((ret)=>{
					const rLoading = this.openLoading();
					if(ret.state==1){
						let data = ret.row;
						this.$set(this,"device",data);
						if(this.device.openFlag!="1"){
							this.$router.push({
								path: "/vip/machineCode"
							})
						}
						rLoading.close();
					}else{
						this.$message.error(ret.message);
						rLoading.close();
					}
				})
			},
			goToMessage() {
				console.log('跳消息中心');
				this.$router.push({
					path: "/personalCentre/message"
				})
			},
			jumpTo(val) {
				if (val == "home") {
					console.log("跳主页")
					//控制右上角按钮显示
					this.$store.dispatch("personCentre/personOrHome", "home");
					this.$router.push({
						path: "/home"
					})
				} else if (val == "personalCentre") {
					console.log("跳个人中心")
					//判断是否已经登陆，如果已经登陆则不用走登陆流程
					let userData = this.$store.getters.userInfo
					if (userData.isLogin == true) {
						//控制右上角按钮显示
						this.$store.dispatch("personCentre/personOrHome", "person")
						this.$router.push({
							path: "/personalCentre"
						})
					} else {
						//先弹身份证窗口，再弹登陆窗口
						this.userLogin(1, res => {
							console.log('用户信息')
							console.log(this.$store.getters.userInfo)
							if (res != null) {
								//弹窗登陆窗口
								//控制登陆弹窗是否显示
								this.$store.dispatch('personCentre/loginDialogShow', true)
							}
						})
					}
				}
			},
			//退出
			logout() {
				var that = this;
				this.$confirm({
					msg: "确认退出登陆吗?",
					okClick: function () {
						//清空用户数据
						console.log('清空用户数据')
						that.$store.dispatch("userInfo", {});
						//清除pullMessage定时器
						console.log('headBar清除pullMessage定时器')
						clearInterval(that.$store.getters['personCentre/personMessageTimer']);
						//控制右上角按钮显示
						that.$store.dispatch("personCentre/personOrHome", "home")
						console.log("退出登陆，跳主页")
						that.$router.push({
							path: "/home"
						})
					},
					cancleClick: function () {
					}
				});
			}
		},
		components: {
		}
	}
</script>
<style scoped>
.headBar{
	width:100%;
	position: relative;
	height:1.2rem;
	line-height: .4rem;
	background:linear-gradient(to left, rgba(41,144,255,1) , rgba(89,169,255,0.9));
	padding:0 0.5rem;
}
.head-log{
	width:0.6rem;
	height:0.6rem;
	margin-right: .12rem;
	background: url(../../../images/desktop/logo.png) no-repeat center;
	-webkit-background-size: 100%;
	background-size: 100%;
}
.lc-box-shadwB{
	box-shadow: 0 0.001rem 0.05rem 0.04rem rgba(38, 88, 143, 0.22);
}
.headBox{
	width:100%;
	height:100%;
}
.headBox>div{
	width:9rem;
	height:100%;
	color:#fff;
}
.headBox>div>span{
	color:#fff;
	font-size:0.36rem;
	letter-spacing: 0.04rem;
}
.headBox>div>span>span{
	color:red;
	font-size:0.36rem;
}
.headBox>div.headBar-r{
	width:7rem;
	display:flex;
	justify-content: flex-end;
	align-items:center;
}
.headBox>div.headBar-r .btn{
	width:2.3rem;
	height:0.7rem;
	background:#fff;
	color:#3295ff;
	font-weight: bold;
	display:flex;
	align-items:center;
	justify-content:center;
}
.headBox>div.headBar-r .btn i{
	font-size:0.32rem;
	position:relative;
	top:0.02rem;
	color:#3295ff;
	margin-right:0.08rem;
}
.headBox>div.headBar-r a{
  position: relative;
  margin-right: .27rem;
  top: .1rem;
}
.headBox>div.headBar-r a .icon-tongzhi{
  font-size: .5rem;
}
.headBox>div.headBar-r .msg{
  display: inline-block;
  width: .15rem;
  height: .15rem;
  background: red;
  border-radius: 1rem;
  top: -.03rem;
  right: .06rem;
  position: absolute;
}
.headBox>div.headBar-r .btn .icon-zhuye{
	font-size:0.34rem;
}
.headBox>div.headBar-r .quit{
	margin-right:0.35rem;
}
.headBox>div.headBar-r .btn .icon-tuichu{
	font-size:0.3rem;
}
/*旧版headBar*/
.headBox>div.headBar-r2{
  width: 5rem;
}
.headBox>div.headBar-r2 p{
	font-size:0.2rem;
}
.headBox>div.headBar-r2 span{
	font-size:0.2rem;
	margin-right:0.08rem;
	color:rgba(255,255,255,0.6);
}
.headBox>div.headBar-r2>p{
	font-size:0.29rem;
	margin-top:0.24rem;

	text-align: justify;
	height:0.4rem;
}
.headBox>div.headBar-r2>p>span{
	font-size:0.29rem;
	color:#fff;
	margin-right:0;
}
.headBox>div.headBar-r2>p:after {
    content: " ";
    display: inline-block;
    width: 100%;
}
</style>