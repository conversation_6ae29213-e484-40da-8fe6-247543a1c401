(window.webpackJsonp=window.webpackJsonp||[]).push([[97],{"+GFb":function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var l=r(a("Q9c5")),n=r(a("DWNM"));function r(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[n.default],name:"stockIn-detail",data:function(){return{activeName:"first",curTab:"0",dialogVisible:!1,stockInData:{data:{},stockInItem:[]},listLoading:!1,tableHeight:100}},components:{},filters:{formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}},watch:{},mounted:function(){this.$route.query.stockInCode&&this.getstockInDetail()},methods:{goBack:function(){this.$router.go(-1)},handleClick:function(t,e){this.curTab=t.index},getstockInDetail:function(){var t=this,e=this.openLoading("查询中"),a=l.default.baseContext+"/supervise/supStockIn/show/"+this.$route.query.stockInCode;this.$http_post(a,{}).then(function(a){1==a.state?(t.stockInData=a.row,e.close()):(e.close(),t.$alert(a.message))})}}}},"5LN7":function(t,e,a){"use strict";a.r(e);var l=a("+GFb"),n=a.n(l);for(var r in l)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return l[t]})}(r);e.default=n.a},AGeH:function(t,e,a){"use strict";a("zb5s")},Qd8L:function(t,e,a){"use strict";var l=a("f68/");a.o(l,"render")&&a.d(e,"render",function(){return l.render}),a.o(l,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return l.staticRenderFns})},SeqD:function(t,e,a){"use strict";a.r(e);var l=a("Qd8L"),n=a("5LN7");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return n[t]})}(r);a("AGeH");var o=a("gp09"),s=Object(o.a)(n.default,l.render,l.staticRenderFns,!1,null,"23aa9aea",null);e.default=s.exports},"f68/":function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left flex-row"}),e("el-button",{attrs:{type:"info",icon:"el-icon-arrow-left"},on:{click:t.goBack}},[t._v("返回\n        ")])],1),e("el-tabs",{model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[e("el-tab-pane",{attrs:{label:"入库单信息",name:"first"}})],1),e("div",{staticClass:"form"},[e("el-form",{ref:"dataForm",staticClass:"item-form",attrs:{id:"stockInData2",model:t.stockInData,"label-position":"left","label-width":"110px"}},[e("div",{staticClass:"fromBox"},[e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"入库单号"}},[e("el-input",{attrs:{value:t.stockInData.data.code}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"医疗机构"}},[e("el-input",{attrs:{value:t.stockInData.data.hospitalName}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"入库联系人"}},[e("el-input",{attrs:{value:t.stockInData.data.stockCreator}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"配送企业"}},[e("el-input",{attrs:{value:t.stockInData.data.deliveryName}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"入库时间"}},[e("el-input",{attrs:{value:t._f("formatTime")(t.stockInData.data.stockInTime)}})],1)],1)],1)],1)])],1),e("div",{staticClass:"memberTab"},[e("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.curTab,callback:function(e){t.curTab=e},expression:"curTab"}},[e("el-tab-pane",{attrs:{label:"入库明细信息"}},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"data_table",staticStyle:{width:"100%"},attrs:{data:t.stockInData.stockInItem,"header-cell-style":{background:"#f5f7fa"},border:"","highlight-current-row":""}},[t._e(),e("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单编号",width:"120"}}),e("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"170"}}),e("el-table-column",{attrs:{prop:"specs",label:"规格",width:"90"}}),e("el-table-column",{attrs:{prop:"packingSpecs",label:"包装规格",width:"100"}}),e("el-table-column",{attrs:{prop:"dosageForm",label:"剂型",width:"100"}}),e("el-table-column",{attrs:{prop:"unitPrice",label:"单价(元)"}}),e("el-table-column",{attrs:{prop:"approvalNumber",label:"批准文号",width:"150"}}),e("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"150"}}),e("el-table-column",{attrs:{prop:"num",label:"入库数量",width:"80"}}),e("el-table-column",{attrs:{prop:"amount",label:"金额(元)"}}),e("el-table-column",{attrs:{prop:"orderCode",label:"订单编号",width:"120"}}),e("el-table-column",{attrs:{prop:"country",label:"国家集采",width:"90",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.country?e("div",[e("el-tag",{attrs:{type:"primary"}},[t._v("是")])],1):t._e(),"1"!=a.row.country?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("否")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"stockStatus",label:"入库状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.stockStatus&&"0"!=a.row.stockStatus?t._e():e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("未入库")])],1),"1"==a.row.stockStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已入库")])],1):t._e(),"2"==a.row.stockStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分入库")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"returnNum",label:"拒收数量",width:"80"}}),e("el-table-column",{attrs:{prop:"refusedReason",label:"拒收原因",width:"80"}})],1)],1)],1)],1)],1)},e.staticRenderFns=[]},zb5s:function(t,e,a){}}]);