(window.webpackJsonp=window.webpackJsonp||[]).push([[100],{"13DX":function(t,e,n){"use strict";n.r(e);var a=n("GUbY"),s=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,function(){return a[t]})}(r);e.default=s.a},"97zb":function(t,e,n){},FXBJ:function(t,e,n){"use strict";n.r(e);var a=n("tbsi"),s=n("13DX");for(var r in s)["default"].indexOf(r)<0&&function(t){n.d(e,t,function(){return s[t]})}(r);n("wHUQ");var i=n("gp09"),o=Object(i.a)(s.default,a.render,a.staticRenderFns,!1,null,"2df3034f",null);e.default=o.exports},GUbY:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0});var a=r(n("DWNM")),s=r(n("Q9c5"));function r(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[a.default],name:"failLog",data:function(){return{currentComponent:"",failId:"",batchId:"",title:"",sBox:!1,formInline:{status:""},tableHeight:100,failList:[],failParams:{page:1,total:0,limit:10},drugData:{id:"",code:"",goodsName:"",catalogId:"",country:"0",countryType:"",electionScope:"",dosageForm:"",specs:"",specsAttr:"",packing:"",unit:"",qualityLevel:"",company:"",approvalNumber:"",standardCode:"",medicalInsurance:"",attribute:"",adjuvant:"0",net:"0",coefficient:"",remark:""}}},mounted:function(){var t=this;this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-90}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-90},this.batchId=this.$route.query.batchId,this.initCurrentComponent(),this.searchBtn()},methods:{initCurrentComponent:function(){var t=this,e=this.$route.query.busiType;e||this.$message.error("业务类型不能为空"),this.$http_post(s.default.baseContext+"/supervise/importConfig/componentPath",{busiType:e}).then(function(e){if("1"==e.state){var n=e.row;t.currentComponent=n}else t.$message.error(e.message)})},close:function(){this.$refs.asyncDialog.show=!1,this.searchBtn()},searchBtn:function(t){var e=this,n={};"reset"==t&&(this.formInline.status=""),n.page=this.failParams.page,n.limit=this.failParams.limit,n.batchId=this.batchId,n.status=this.formInline.status,this.$http_post(s.default.baseContext+"/supervise/importFailLog/query",n).then(function(t){"1"==t.state?(e.failList=t.rows,e.failParams.total=t.records):e.$message.error(t.message)})},handleCurrentChange:function(t){this.failParams.page=t,this.searchBtn()},process:function(t){this.failId=t.id,this.$refs.asyncDialog.show=!0},ignore:function(t){var e=this,n=this.openLoading("忽略中...");this.$http_post(s.default.baseContext+"/supervise/importFailLog/updateStatus/"+t.id+"?status=-1").then(function(t){1==t.state?(e.$message.success("忽略成功"),e.searchBtn(),n.close()):null!=t.message?e.$message.error(t.message):e.$message.error("系统异常")}).catch(function(t){n.close(),console.log(t)})}}}},T7Ll:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left flex-row"},[e("span",[t._v("状态")]),e("el-select",{attrs:{placeholder:"请选择处理状态"},model:{value:t.formInline.status,callback:function(e){t.$set(t.formInline,"status",e)},expression:"formInline.status"}},[e("el-option",{attrs:{value:"0",label:"待处理"}}),e("el-option",{attrs:{value:"1",label:"已处理"}}),e("el-option",{attrs:{value:"-1",label:"不处理"}})],1),e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(e){return t.searchBtn("search")}}},[t._v("查询")]),e("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:function(e){return t.searchBtn("reset")}}},[t._v("重置")])],1),e("el-button",{attrs:{type:"info",icon:"el-icon-back"},on:{click:function(e){return t.$router.push({path:"batchLog",query:{busiType:t.$route.query.busiType}})}}},[t._v("返回\n        ")])],1),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.failList,height:t.tableHeight}},[e("el-table-column",{attrs:{type:"index",width:"50",label:"序号"}}),e("el-table-column",{attrs:{label:"数据",align:"center",width:"500"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("el-popover",{attrs:{placement:"top-start",title:"数据",width:"500",trigger:"hover",content:n.row.data}},[e("span",{attrs:{slot:"reference"},slot:"reference"},[t._v(t._s(n.row.data))])])]}}])}),e("el-table-column",{attrs:{label:"错误原因",align:"center",prop:"msg",width:""}}),e("el-table-column",{attrs:{align:"center",label:"状态",prop:"status",width:"130"},scopedSlots:t._u([{key:"default",fn:function(n){return[0==n.row.status?e("el-tag",{attrs:{effect:"plain",type:"warning"}},[t._v("\n                            待处理\n                        ")]):1==n.row.status?e("el-tag",{attrs:{effect:"plain",type:"success"}},[t._v("\n                            已处理\n                        ")]):e("el-tag",{attrs:{effect:"plain",type:"danger"}},[t._v("\n                            已忽略\n                        ")])]}}])}),e("el-table-column",{attrs:{label:"操作",align:"center",width:"180"},scopedSlots:t._u([{key:"default",fn:function(n){return[t.currentComponent?e("el-button",{attrs:{disabled:0!=n.row.status,icon:"el-icon-check",type:"primary",size:"small"},on:{click:function(e){return t.process(n.row)}}},[t._v("处理\n                        ")]):t._e(),e("el-button",{attrs:{disabled:0!=n.row.status,icon:"el-icon-close",type:"warning",size:"small"},on:{click:function(e){return t.ignore(n.row)}}},[t._v("忽略\n                        ")])]}}])})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{"page-size":t.failParams.limit,layout:"total,prev, pager, next, jumper",total:t.failParams.total},on:{"current-change":t.handleCurrentChange}})],1)],1)]),e(t.currentComponent,{ref:"asyncDialog",tag:"component",attrs:{failId:t.failId},on:{close:t.close}})],1)},e.staticRenderFns=[]},tbsi:function(t,e,n){"use strict";var a=n("T7Ll");n.o(a,"render")&&n.d(e,"render",function(){return a.render}),n.o(a,"staticRenderFns")&&n.d(e,"staticRenderFns",function(){return a.staticRenderFns})},wHUQ:function(t,e,n){"use strict";n("97zb")}}]);