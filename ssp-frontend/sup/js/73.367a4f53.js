(window.webpackJsonp=window.webpackJsonp||[]).push([[73],{"9CDZ":function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left"},[e("el-row",{attrs:{gutter:10}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("药品通用名")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入药品通用名"},model:{value:t.params.catalogName,callback:function(e){t.$set(t.params,"catalogName",e)},expression:"params.catalogName"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("剂型")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入药品剂型"},model:{value:t.params.dosageForm,callback:function(e){t.$set(t.params,"dosageForm",e)},expression:"params.dosageForm"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("规格")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入药品规格"},model:{value:t.params.specs,callback:function(e){t.$set(t.params,"specs",e)},expression:"params.specs"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("包装规格")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入药品包装规格"},model:{value:t.params.packingSpecs,callback:function(e){t.$set(t.params,"packingSpecs",e)},expression:"params.packingSpecs"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("类别")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",placeholder:"请选择药品类别"},model:{value:t.params.category,callback:function(e){t.$set(t.params,"category",e)},expression:"params.category"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"中药",value:"1"}}),e("el-option",{attrs:{label:"西药",value:"2"}})],1)],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("基药属性")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small"},model:{value:t.params.attribute,callback:function(e){t.$set(t.params,"attribute",e)},expression:"params.attribute"}},[e("el-option",{attrs:{label:"请选择基药属性",value:""}}),e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"空",value:"0"}}),e("el-option",{attrs:{label:"国基",value:"1"}}),e("el-option",{attrs:{label:"省基",value:"2"}})],1)],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("是否集采")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",placeholder:"请选择"},model:{value:t.params.country,callback:function(e){t.$set(t.params,"country",e)},expression:"params.country"}},[e("el-option",{key:"",attrs:{label:"全部",value:""}}),e("el-option",{key:"1",attrs:{label:"是",value:"1"}}),e("el-option",{key:"2",attrs:{label:"否",value:"0"}})],1)],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("医保药品码")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入医保药品代码"},model:{value:t.params.medicalInsuranceCode,callback:function(e){t.$set(t.params,"medicalInsuranceCode",e)},expression:"params.medicalInsuranceCode"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("生产企业")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入生产企业"},model:{value:t.params.drugCompanyName,callback:function(e){t.$set(t.params,"drugCompanyName",e)},expression:"params.drugCompanyName"}})],1)])])],1)],1),e("div",{staticClass:"right"},[e("div",{staticStyle:{"margin-bottom":"5px"}},[e("el-badge",{staticStyle:{"margin-right":"5px"},attrs:{size:"small",value:t.orderData.orderItem.length,max:99}},[e("el-button",{attrs:{type:"success",icon:"el-icon-shopping-cart-2"},on:{click:t.goToCart}},[t._v("购物车")])],1)],1),e("div",[e("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1)])]),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.dataList,border:"","highlight-current-row":"",height:t.tableHeight}},[e("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"180","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"standardCode",label:"本位码",width:"180","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"medicalInsuranceCode",label:"国家医保码",width:"180","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"180",align:"left"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",[t._v("\n                "+t._s(t.getDictItemName(a.row.source))+"\n              ")])]}}])}),e("el-table-column",{attrs:{prop:"dosageForm",label:"剂型","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"specs",label:"规格","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"packingSpecs",label:"包装规格","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"unitPrice",label:"最小单位价(元)",width:"140","show-tooltip-when-overflow":""},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{directives:[{name:"show",rawName:"v-show",value:a.row.specsPrice&&"1"!=a.row.systemContrast,expression:"scope.row.specsPrice&&scope.row.systemContrast!='1'"}]},[e("el-tag",{attrs:{type:"info"}},[t._v(t._s(a.row.specsPrice))])],1),e("div",{directives:[{name:"show",rawName:"v-show",value:"1"==a.row.systemContrast,expression:"scope.row.systemContrast=='1'"}]},[e("el-tag",{attrs:{type:"success"}},[t._v(t._s(a.row.specsPrice+"(推荐)"))])],1),e("div",{directives:[{name:"show",rawName:"v-show",value:!a.row.specsPrice,expression:"!scope.row.specsPrice"}]},[e("el-tag",{attrs:{type:"warning"}},[t._v("暂无")])],1)]}}])}),e("el-table-column",{attrs:{prop:"qualityLevel",width:"100",label:"质量层次","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"packing","show-tooltip-when-overflow":"",label:"包装材质"}}),e("el-table-column",{attrs:{prop:"unit",label:"单位","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"attribute",width:"80","show-tooltip-when-overflow":"",label:"基药属性"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",["0"==a.row.attribute?e("div",{attrs:{type:"success"}},[t._v("空")]):t._e(),"1"==a.row.attribute?e("div",{attrs:{type:"success"}},[t._v("国基")]):t._e(),"2"==a.row.attribute?e("div",{attrs:{type:"success"}},[t._v("省基")]):t._e()])]}}])}),e("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"200","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{align:"center",prop:"country",label:"国家集采"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.country?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("是")])],1):e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("否")])],1)]}}])}),e("el-table-column",{attrs:{label:"操作",align:"right",width:"200",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return[1==a.row.isSelected?e("el-tag",{attrs:{type:"info",round:"",plain:"",size:"small"}},[t._v("已选择\n              ")]):t._e(),e("el-button",{attrs:{type:"success",round:"",plain:"",size:"small"},on:{click:function(e){return t.showDrugDetail(a.row)}}},[t._v("查看详情\n              ")]),1!=a.row.isSelected?e("el-button",{attrs:{type:"primary",round:"",plain:"",size:"small"},on:{click:function(e){return t.chooseDrug(a.$index,a.row)}}},[t._v("选择药品\n              ")]):t._e()]}}])})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)]),e("el-dialog",{attrs:{title:"【"+t.drugDetail.catalogName+"】药品详情",visible:t.showDrugDetailBox,width:"45%"},on:{"update:visible":function(e){t.showDrugDetailBox=e}}},[e("div",[e("el-form",{ref:"showDrugDetailForm",staticClass:"item-form",attrs:{model:t.drugDetail,"label-width":"110px"}},[e("div",{staticClass:"fromBox"},[e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"采购平台"}},[t._v("\n                  "+t._s(t.getDictItemName(t.drugDetail.source))+"\n                ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"药品本位码"}},[t._v("\n                  "+t._s(t.drugDetail.standardCode)+"\n                ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"通用名"}},[t._v("\n                  "+t._s(t.drugDetail.catalogName)+"\n                ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"商品名"}},[t._v("\n                  "+t._s(t.drugDetail.goodsName)+"\n                ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"商品价格"}},[t._v("\n                  "+t._s(t.drugDetail.unitPrice+"元")+"\n                ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"最小单位价"}},[t._v("\n                  "+t._s(t.drugDetail.specsPrice+"元")+"\n                ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"药品编码"}},[t._v("\n                  "+t._s(t.drugDetail.code)+"\n                ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"药品类别"}},["1"==t.drugDetail.category?e("span",[t._v("中药")]):t._e(),"2"==t.drugDetail.category?e("span",[t._v("西药")]):t._e(),"1"!=t.drugDetail.category&&"2"!=t.drugDetail.category?e("span",[t._v("其它")]):t._e()])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"剂型"}},[t._v("\n                  "+t._s(t.drugDetail.dosageForm)+"\n                ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"规格"}},[t._v("\n                  "+t._s(t.drugDetail.specs)+"\n                ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"包装规格"}},[t._v("\n                  "+t._s(t.drugDetail.packingSpecs)+"\n                ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"单位"}},[t._v("\n                  "+t._s(t.drugDetail.unit)+"\n                ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"医保目录"}},["1"==t.drugDetail.medicalInsurance?e("span",[t._v("甲类")]):t._e(),"2"==t.drugDetail.medicalInsurance?e("span",[t._v("乙类")]):t._e()])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"国家集中集采"}},["1"==t.drugDetail.country?e("span",[t._v("是")]):t._e(),"0"==t.drugDetail.country?e("span",[t._v("否")]):t._e()])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"生产企业",prop:"company"}},[t._v("\n                  "+t._s(t.drugDetail.drugCompanyName)+"\n                ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"批准文号"}},[t._v("\n                  "+t._s(t.drugDetail.approvalNumber)+"\n                ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"包装材质"}},[t._v("\n                  "+t._s(t.drugDetail.packing)+"\n                ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"质量层次"}},[t._v("\n                  "+t._s(t.drugDetail.qualityLevel)+"\n                ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"基药属性"}},["0"==t.drugDetail.attribute?e("span",[t._v("空")]):t._e(),"1"==t.drugDetail.attribute?e("span",[t._v("国基")]):t._e(),"2"==t.drugDetail.attribute?e("span",[t._v("省基")]):t._e()])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"医保药品代码",prop:"medicalInsuranceCode"}},[t._v("\n                  "+t._s(t.drugDetail.medicalInsuranceCode)+"\n                ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{staticClass:"col-left",attrs:{label:"备注"}},[t._v("\n                  "+t._s(t.drugDetail.remark)+"\n                ")])],1)],1)],1)])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.showDrugDetailBox=!1,t.drugDetail={}}}},[t._v("关 闭")])],1)]),t.drugBox?e("el-dialog",{attrs:{title:"购物车",visible:t.drugBox,"close-on-click-modal":!1,"close-on-press-escape":!1,width:"90%"},on:{"update:visible":function(e){t.drugBox=e}}},[e("div",{staticClass:"orderBox-dialog-content"},[e("el-form",{ref:"form",staticClass:"item-form",attrs:{"label-width":"90px",id:"orderData",model:t.orderData}},[e("el-table",{ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.orderData.orderItem},on:{"selection-change":t.handleSelectionChange}},[t._v("\n              highlight-current-row\n            >\n            "),e("el-table-column",{attrs:{type:"selection",width:"50"}}),e("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"180",align:"left"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",[e("el-tag",{attrs:{type:"-1"==a.row.source?"warning":"success"}},[t._v(t._s(t.getDictItemName(a.row.source)))])],1)]}}],null,!1,3035575222)}),e("el-table-column",{attrs:{prop:"busiCode",label:"药品编码",width:"120",align:"center"}}),e("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"180",align:"center"}}),e("el-table-column",{attrs:{prop:"category",label:"类别",width:"70",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.category?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("中药")])],1):t._e(),"2"==a.row.category?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("西药")])],1):t._e(),"1"!=a.row.category&&"2"!=a.row.category?e("div",[e("el-tag",{attrs:{type:"info"}},[t._v("其它")])],1):t._e()]}}],null,!1,*********)}),e("el-table-column",{attrs:{prop:"specs",label:"规格",width:"90",align:"center"}}),e("el-table-column",{attrs:{prop:"packingSpecs",label:"包装规格",width:"120",align:"center"}}),e("el-table-column",{attrs:{prop:"dosageForm",label:"剂型",width:"120",align:"center"}}),e("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",align:"center",width:"170"}}),e("el-table-column",{attrs:{label:"最小单位价(元)",width:"130",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                "+t._s(e.row.specsPrice)+"\n              ")]}}],null,!1,1222558662)}),e("el-table-column",{attrs:{prop:"unitPrice",label:"商品价(元)",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v("\n                "+t._s(a.row.unitPrice)+"\n                "),"0"==a.row.systemContrast?e("el-popover",{attrs:{placement:"top-start",title:"非系统推荐  ",width:"200",trigger:"hover",content:"原因："+a.row.reason}},[e("el-tag",{attrs:{slot:"reference",type:"danger"},slot:"reference"},[t._v("预警")])],1):t._e()]}}],null,!1,1602789233)}),e("el-table-column",{attrs:{prop:"amount",label:"采购数量",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-form-item",{attrs:{prop:"orderItem."+a.$index+".amount",rules:t.rules.amount}},[e("el-input",{attrs:{type:"number",placeholder:"请输入数量"},model:{value:a.row.amount,callback:function(e){t.$set(a.row,"amount",t._n(e))},expression:"scope.row.amount"}})],1)]}}],null,!1,786113987)}),e("el-table-column",{attrs:{label:"操作",align:"right",width:"170"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"warning",size:"small"},on:{click:function(e){return t.delOrder(a.$index,t.orderData.orderItem)}}},[t._v("\n                  删除\n                ")]),e("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.showDrugDetail(a.row)}}},[t._v("查看详情\n                ")])]}}],null,!1,2638859659)})],1)],1)],1),e("div",{staticClass:"orderBox-dialog-footer flex-row"},[e("span"),e("span",[e("el-button",{on:{click:function(e){t.drugBox=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"success"},on:{click:t.validOrderData}},[t._v("生成推荐单")])],1)])]):t._e(),t.orderBox?e("el-dialog",{attrs:{title:"推荐单明细",visible:t.orderBox,"close-on-click-modal":!1,"close-on-press-escape":!1,width:"90%"},on:{"update:visible":function(e){t.orderBox=e}}},[e("div",{staticClass:"orderBox-dialog-content",attrs:{id:"printContent"}},[e("el-form",{ref:"confirmOrderDataForm",staticClass:"item-form",attrs:{id:"confirmOrderData",model:t.confirmOrderData,"label-width":"90px"}},t._l(t.confirmOrderData.source,function(a,r,o){return e("div",{staticClass:"orderBox-content"},[e("div",{staticStyle:{"font-size":"16px",margin:"5px"}},[t._v("\n              【"),e("span",[t._v(" "+t._s(t.getDictItemName(r)))]),t._v("】订单 :\n            ")]),e("div",{staticClass:"header-table"},[e("el-table",{staticStyle:{width:"100%"},attrs:{id:"orderTable",data:t.confirmOrderData.source[r].orderItem,border:"","highlight-current-row":""}},[e("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                    "+t._s(t.getDictItemName(e.row.source))+"\n                  ")]}}],null,!0)}),e("el-table-column",{attrs:{prop:"busiCode",label:"采购平台平台编码",width:"150"}}),e("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"200"}}),e("el-table-column",{attrs:{prop:"category",label:"类别"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.category?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("中药")])],1):t._e(),"2"==a.row.category?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("西药")])],1):t._e(),"1"!=a.row.category&&"2"!=a.row.category?e("div",[e("el-tag",{attrs:{type:"info"}},[t._v("其它")])],1):t._e()]}}],null,!0)}),e("el-table-column",{attrs:{prop:"specs",label:"规格"}}),e("el-table-column",{attrs:{prop:"packingSpecs",label:"包装规格"}}),e("el-table-column",{attrs:{prop:"dosageForm",label:"剂型"}}),e("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"200"}}),e("el-table-column",{attrs:{prop:"unitPrice",label:"最小单位价(元)"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                    "+t._s(e.row.specsPrice)+"\n                  ")]}}],null,!0)}),e("el-table-column",{attrs:{prop:"unitPrice",label:"商品价(元)"}}),e("el-table-column",{attrs:{prop:"amount",label:"采购数量",width:"100"}})],1)],1),e("div",{staticClass:"fromBox",staticStyle:{"margin-top":"10px"}},[e("el-row",[e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"医疗机构"}},[t._v("\n                    "+t._s(t.hospital.name)+"\n                  ")])],1),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"价格合计",prop:"totalPrice"}},[t._v("\n                    "+t._s(t.confirmOrderData.source[r].totalPrice)+"元\n                  ")])],1),e("el-col",{attrs:{span:8}},[e("el-button",{staticClass:"no-print",staticStyle:{"margin-right":"10px","margin-top":"6px",float:"right"},attrs:{type:"primary"},on:{click:function(e){return t.goToPlace(r)}}},[t._v("前往采购")])],1)],1)],1)])}),0)],1),e("div",{staticClass:"orderBox-dialog-footer flex-row"},[e("span",[t._v("注：同一采购平台的药品生成一个订单。")]),e("span",[e("el-button",{attrs:{type:"success"},on:{click:t.printOrderData}},[t._v("打 印")]),e("el-button",{on:{click:function(e){t.orderBox=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.saveOrderData}},[t._v("确 定")])],1)])]):t._e(),e("el-dialog",{attrs:{title:"选择原因",visible:t.showOtherDrugReasonBox,width:"20%"},on:{"update:visible":function(e){t.showOtherDrugReasonBox=e}}},[e("el-select",{attrs:{placeholder:"请选择原因"},on:{change:t.selectChange},model:{value:t.otherDrug.reasonType,callback:function(e){t.$set(t.otherDrug,"reasonType",e)},expression:"otherDrug.reasonType"}},[e("el-option",{attrs:{label:"患者习惯用药",value:"患者习惯用药"}}),e("el-option",{attrs:{label:"原研药",value:"原研药"}}),e("el-option",{attrs:{label:"最低价药品缺货",value:"最低价药品缺货"}}),e("el-option",{attrs:{label:"议价后价格更低",value:"4"}}),e("el-option",{attrs:{label:"其他",value:"3"}})],1),3==t.otherDrug.reasonType||4==t.otherDrug.reasonType?e("el-input",{staticStyle:{"margin-top":"10px"},attrs:{type:"textarea",rows:2,placeholder:t.reasonText},model:{value:t.otherDrug.reason,callback:function(e){t.$set(t.otherDrug,"reason",e)},expression:"otherDrug.reason"}}):t._e(),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.showOtherDrugReasonBox=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.editOrder}},[t._v("确 定")])],1)],1)],1)},e.staticRenderFns=[]},"H/cx":function(t,e,a){"use strict";a.r(e);var r=a("ZCZr"),o=a("MrUf");for(var s in o)["default"].indexOf(s)<0&&function(t){a.d(e,t,function(){return o[t]})}(s);a("eTT4");var l=a("gp09"),i=Object(l.a)(o.default,r.render,r.staticRenderFns,!1,null,"7bbc4d98",null);e.default=i.exports},Mkr2:function(t,e,a){},MrUf:function(t,e,a){"use strict";a.r(e);var r=a("TQGQ"),o=a.n(r);for(var s in r)["default"].indexOf(s)<0&&function(t){a.d(e,t,function(){return r[t]})}(s);e.default=o.a},TQGQ:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var r=i(a("Q9c5")),o=i(a("DWNM")),s=i(a("FWu7")),l=i(a("Ux1M"));function i(t){return t&&t.__esModule?t:{default:t}}e.default={name:"createOrder",mixins:[o.default],data:function(){return{adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1,areaRegionAdmin:!1,rules:{amount:[{validator:function(t,e,a){0==/^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/.test(e)?a(new Error("请输入大于零的数量")):a()},trigger:"change"},{validator:function(t,e,a){0==/^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/.test(e)?a(new Error("请输入大于零的数量")):a()},trigger:"blur"},{type:"number",required:!0,message:"采购数量不能为空",trigger:"blur"}]},hospital:{},confirmOrderData:{},orderBox:!1,orderData:{orderItem:[],totalPrice:0},multipleSelection:[],showOtherDrugReasonBox:!1,drugSourceList:[],brandFold:!1,params:{source:"123",category:"",packingSpecs:"",specs:"",dosageForm:"",catalogName:"",page:1,limit:10,records:0,attribute:"",country:"",medicalInsuranceCode:"",drugCompanyName:""},drugDetail:{version:"",code:"",drugCompanyName:"",category:"",id:"",source:"",goodsName:"",catalogName:"",catalogId:"",country:"0",countryType:"",electionScope:"",dosageForm:"",specs:"",packingSpecs:"",packing:"",unit:"",qualityLevel:"",company:"",approvalNumber:"",standardCode:"",medicalInsurance:"",attribute:"",adjuvant:"0",net:"0",coefficient:"",remark:"",status:""},showDrugDetailBox:!1,tableHeight:100,dataList:[],spanArr:[],position:"",drugBox:!1,otherDrug:{list:[],params:{page:1,limit:10,records:0},detail:{detailId:"",version:"",code:"",drugCompanyName:"",category:"",id:"",source:"",goodsName:"",catalogName:"",catalogId:"",country:"0",countryType:"",electionScope:"",dosageForm:"",specs:"",packingSpecs:"",packing:"",unit:"",qualityLevel:"",company:"",approvalNumber:"",standardCode:"",medicalInsurance:"",attribute:"",adjuvant:"0",net:"0",coefficient:"",remark:"",status:""},reasonType:"3",reason:"",reasonText:"",curSelectDrug:{}}}},props:{},created:function(){this.reasonText="该药品不是最低价，请输入选择原因"},computed:{},watch:{},mounted:function(){var t=this;this.initData(),this.initRole(),this.getHospital(),this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-150}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-150}},methods:{initRole:function(){var t=this.$store.getters.curUser.roleCode;-1!=t.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=t.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=t.indexOf("DELIVERY_ADMIN")?this.areaRegionAdmin=!0:-1!=t.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=t.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)},toggleSelection:function(t){var e=this;t?t.forEach(function(t){e.$refs.multipleTable.toggleRowSelection(t)}):this.$refs.multipleTable.clearSelection()},handleSelectionChange:function(t){this.multipleSelection=t},selectChange:function(t){"3"==t?(this.otherDrug.reason="",this.reasonText="该药品不是最低价，请输入选择原因"):"4"==t?(this.otherDrug.reason="",this.reasonText="请输入议价后价格"):this.otherDrug.reason=t},printOrderData:function(){var t=document.getElementById("printContent"),e=t.cloneNode(!0);e.style.width=$(t).find("#orderTable").find("table")[0].style.width,e.style.height=t.scrollHeight+20+"px",e.style.zIndex="-1",$(e).find(".no-print").remove(),document.body.appendChild(e),setTimeout(function(){(0,s.default)(e,{backgroundColor:null,useCORS:!0}).then(function(t){var a=t.toDataURL("image/png").replace("image/png","image/octet-stream");(0,l.default)({printable:a,type:"image",documentTitle:"药品采购推荐单"}),document.body.removeChild(e)})},100)},getPixelRatio:function(t){var e=t.backingStorePixelRatio||t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1;return(window.devicePixelRatio||1)/e},saveOrderData:function(){var t=this;this.$refs.confirmOrderDataForm.validate(function(e){if(e){var a=t.openLoading("订单提交中...");t.confirmOrderData.hospital=t.hospital,console.log("订单详情",t.confirmOrderData),t.$http_post(r.default.baseContext+"/supervise/supRecommendOrder/save",t.confirmOrderData,!0).then(function(e){1==e.state?(a.close(),t.orderBox=!1,t.$confirm("下单成功，前往【推荐订单】查看订单详情",{confirmButtonText:"确定",showCancelButton:!1,type:"success"}).then(function(){t.$router.push({path:"recommendList"})}).catch(function(e){t.confirmOrderData={},t.orderData={orderItem:[],totalPrice:0},t.multipleSelection=[],t.otherDrug={},t.drugBox=!1,t.orderBox=!1,t.onQuery(),t.openLoading().close(),console.log(e)})):(a.close(),t.$alert(e.message))})}})},goToPlace:function(t){console.log(111,t);var e=[],a=[],o=[];this.confirmOrderData.source[t].orderItem.forEach(function(t){e.push(t.busiCode),o.push(t.amount),a.push(t.contractId)});var s=r.default.SZCartProLink+e.toString()+"&num="+o.toString();3==t&&(this.hospital.address||(this.hospital.address="无"),s=r.default.GZCartProLink+a.toString()+"&num="+o.toString()+"&address="+this.hospital.address),2==t&&(s=r.default.GDCartProLink),window.open(s,"_blank")},getHospital:function(){var t=this,e=this.openLoading(),a=this.$store.getters.curUser.id;this.adminRole||this.medicalAdmin||this.areaRegionAdmin||this.socialAdmin||this.$http_post(r.default.baseContext+"/supervise/supHospitalUser/userHospital/"+a,{}).then(function(a){1==a.state?(t.hospital=a.row,console.log(t.hospital)):(t.$alert(a.message),e.close())})},validOrderData:function(){var t=this;if(null==this.multipleSelection||0==this.multipleSelection.length)return this.$alert("请先选择药品"),!1;var e={source:{},hospital:this.hospital};for(var a in this.multipleSelection.forEach(function(t,a){t.itemPrice=t.amount*t.unitPrice;var r=t.source;e.source[r]||(e.source[r]={source:r,orderItem:[],totalPrice:0}),e.source[r].orderItem.push(t)}),e.source){var r=0;e.source[a].orderItem.forEach(function(t){r+=t.itemPrice,t.itemPrice=t.itemPrice.toFixed(4)}),e.source[a].totalPrice=r.toFixed(4)}this.confirmOrderData=e,this.$refs.form.validate(function(a){a&&(t.orderBox=!0,console.log("通过"),console.log(e))})},delOrder:function(t,e){e.splice(t,1),this.onQuery(),this.openLoading().close()},editOrder:function(){if(null==this.otherDrug.reason||0==this.otherDrug.reason.length)return this.$alert("原因不能为空"),!1;"4"==this.otherDrug.reasonType&&(this.otherDrug.reason="议价后价格更低："+this.otherDrug.reason);var t=this.otherDrug.curSelectDrug.row;t.isSelected=!0;var e=this.otherDrug.curSelectDrug.index;this.$set(this.dataList,e,t),this.otherDrug.detail.reason=this.otherDrug.reason,this.otherDrug.detail.systemContrast="0",this.otherDrug.reasonType="3",this.otherDrug.reason="",this.orderData.orderItem.push(this.otherDrug.detail),this.showOtherDrugReasonBox=!1},chooseDrug:function(t,e){this.otherDrug.curSelectDrug.row=e,this.otherDrug.curSelectDrug.index=t,this.otherDrug.detail=e,this.otherDrug.detail.detailId=e.id,"1"!=e.systemContrast?this.showOtherDrugReasonBox=!0:(this.orderData.orderItem.push(this.otherDrug.detail),e.isSelected=!0,this.$set(this.dataList,t,e),this.$message.success("添加购物车成功"))},goToCart:function(){var t=this;this.drugBox=!0,this.$nextTick(function(){t.toggleSelection(t.orderData.orderItem)})},getPrice:function(t,e){if(t){var a=t.match(/\d+/g);return e?(e/a.join("")).toFixed(3):""}return""},getDictItemName:function(t){for(var e=0;e<this.drugSourceList.length;e++)if(this.drugSourceList[e].value==t)return this.drugSourceList[e].label},getDictItem:function(t){var e=this;this.$http_post(r.default.baseContext+"/supervise/supDictItem/getItemList",{code:t}).then(function(a){var r=a.rows;1==a.state?"SOURCE"==t&&(e.drugSourceList=r):e.$message.error(a.message)})},showDrugDetail:function(t){this.drugDetail=t,this.showDrugDetailBox=!0},initData:function(){this.onQuery(),this.getDictItem("SOURCE")},onHideSearchCon:function(t){for(var e=0;e<t.path.length;e++){var a=t.path[e];if(a==this.$refs.searchCon||a==this.$refs.foldBtn)return}this.brandFold=!1},onSearch:function(t){"reset"==t?(this.params={packingSpecs:"",code:"",specs:"",page:1,limit:10,records:0,standardCode:"",source:"123",goodsName:"",catalogId:"",country:"",dosageForm:"",company:"",approvalNumber:"",attribute:"",medicalInsuranceCode:"",drugCompanyName:""},this.initData()):(this.params.page=1,this.initData()),this.brandFold=!1},onPageClick:function(t){this.params.page=t,this.initData()},onQuery:function(){var t=this,e=this.params,a=this.openLoading("系统比价推荐中..."),o=r.default.baseContext+"/supervise/supDrugDetail/getSupDrugList";this.$http_post(o,e).then(function(e){1==e.state?(t.orderData.orderItem&&t.orderData.orderItem.length>0&&t.orderData.orderItem.forEach(function(t){e.rows.forEach(function(e){t.id==e.id&&t.busiCode==e.busiCode&&t.source==e.source&&t.code==e.code&&(e.isSelected=!0)})}),t.dataList=e.rows,t.params.records=e.records,t.rowspan(),a.close()):(a.close(),t.$alert(e.message))})},objectSpanMethod:function(t){t.row,t.column;var e=t.rowIndex;if(0===t.columnIndex){var a=this.spanArr[e];return{rowspan:a,colspan:a>0?1:0}}},rowspan:function(){var t=this;this.spanArr=[],this.position=0,this.dataList.forEach(function(e,a){0===a?(t.spanArr.push(1),t.position=0,e.sequence=a+1):t.dataList[a].catalogNameGroup===t.dataList[a-1].catalogNameGroup&&t.dataList[a].qualityLevelGroup===t.dataList[a-1].qualityLevelGroup&&t.dataList[a].specsGroup===t.dataList[a-1].specsGroup&&t.dataList[a].dosageFormGroup===t.dataList[a-1].dosageFormGroup?(t.spanArr[t.position]+=1,t.spanArr.push(0),t.dataList[a].sequence=t.dataList[a-1].sequence):(t.spanArr.push(1),t.position=a,t.dataList[a].sequence=t.dataList[a-1].sequence+1)})},changeFoldState:function(){this.brandFold=!this.brandFold}},beforeDestroy:function(){window.onresize=null},filters:{formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}}}},ZCZr:function(t,e,a){"use strict";var r=a("9CDZ");a.o(r,"render")&&a.d(e,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return r.staticRenderFns})},eTT4:function(t,e,a){"use strict";a("Mkr2")}}]);