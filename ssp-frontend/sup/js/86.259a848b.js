(window.webpackJsonp=window.webpackJsonp||[]).push([[86],{"0WkF":function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var i=l(a("Q9c5")),s=l(a("DWNM"));l(a("cH4l"));function l(e){return e&&e.__esModule?e:{default:e}}t.default={name:"deliveryApply-list",mixins:[s.default],data:function(){return{deliveryAdmin:!1,pickerOptions:{shortcuts:[{text:"上个月",onClick:function(e){var t=new Date,a=t.getFullYear(),i=t.getMonth();0==i&&(i=12,a-=1),i<10&&(i="0"+i);var s=new Date(a+"-"+i+"-01").format("yyyy-MM-dd HH:mm:ss"),l=new Date(a,i,0),o=new Date(a+"-"+i+"-"+l.getDate()).format("yyyy-MM-dd HH:mm:ss");e.$emit("pick",[s,o])}},{text:"今年至上个月",onClick:function(e){var t=new Date,a=t.getFullYear(),i=t.getMonth();0==i&&(i=12,a-=1),i<10&&(i="0"+i);var s=new Date(a+"-01-01").format("yyyy-MM-dd HH:mm:ss"),l=new Date(a,i,0),o=new Date(a+"-"+i+"-"+l.getDate()).format("yyyy-MM-dd HH:mm:ss");e.$emit("pick",[s,o])}}]},drugSourceList:[],multipleSelection:[],invoiceItemDataList:[],hospitalList:[],invoiceParams:{source:"",overFlag:"1",invoiceDate:"",page:1,limit:10,records:0,hospitalName:"",deliveryName:"",country:"1",catalogName:"",orderCode:"",deliveryCode:"",itemNo:""},show:!1,title:"医保基金支付",dialogVisible:!1,listLoading:!1,tableHeight:100,confirmDataList:[],showBox:!1}},mounted:function(){var e=this;-1!=this.$store.getters.curUser.roleCode.indexOf("DELIVERY_ADMIN")&&(this.deliveryAdmin=!0,this.invoiceParams.deliveryName=this.$store.getters.curUser.name),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-150}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-150},this.getDictItem("SOURCE"),this.onInvoiceQuery(),this.getHospitalList()},computed:{uploadUrl:function(){return i.default.baseContext+"/file/upload"}},watch:{$route:{handler:function(){},deep:!0}},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+"")}},updated:function(){this.toggleSelection(this.invoiceItemDataList)},methods:{getHospitalList:function(){var e=this,t=i.default.baseContext+"/supervise/supHospital/all";this.$http_post(t,{}).then(function(t){1==t.state?e.hospitalList=t.row:e.$alert(t.message)})},getDictItem:function(e){var t=this,a=this.openLoading();this.$http_post(i.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(i){var s=i.rows;1==i.state?("SOURCE"==e&&(t.drugSourceList=s),a.close()):(a.close(),t.$message.error(i.message))})},getDictItemName:function(e){for(var t=0;t<this.drugSourceList.length;t++)if(this.drugSourceList[t].value==e)return this.drugSourceList[t].label},confirmSettlement:function(){var e=this,t=this.openLoading("提交中...");this.$http_post(i.default.baseContext+"/supervise/supSettlement/save",this.confirmDataList,!0).then(function(a){1==a.state?(t.close(),e.showBox=!1,e.onInvoiceQuery()):(t.close(),e.$alert(a.message))})},toggleSelection:function(e){},handleSelectionChange:function(e){this.multipleSelection=e},createSettlement:function(){console.log("..",this.multipleSelection),0==this.multipleSelection.length?this.$message({type:"error",message:"所选内容不能为空！"}):(this.confirmDataList=this.multipleSelection,this.showBox=!0)},close:function(){this.show=!1},phoneNotice:function(e){var t=this,a=this.$options.filters.formatTime(e.orderCreationTime),s=e.hospitalName;this.$confirm("是否向"+s+"发出短信提醒？（该医疗机构需配置手机号）","短信提醒",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var l=t.openLoading(),o={busiId:e.id,hospitalId:e.hospitalId,hospitalName:s,orderCreationTime:a,sourceName:"深圳市药品交易平台",orderNum:e.orderCode,invoiceItemNo:e.itemNo,drugCatalogName:e.catalogName,noticeNum:null==e.noticeNum?1:e.noticeNum},n=i.default.baseContext+"/supervise/supOfficial/sendSmsTemplate/insurance_sms_notice";t.$http_post(n,o,!0).then(function(e){1==e.state?(t.$message({type:"success",message:"发送成功"}),t.onInvoiceQuery(),l.close()):(l.close(),t.$alert(e.message))})})},officialNotice:function(e){var t=this,a=this.$options.filters.formatTime(e.orderCreationTime),s=e.hospitalName;this.$confirm("是否向"+s+"发出公文提醒？","公文通知",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var l=t.openLoading(),o={busiId:e.id,hospitalId:e.hospitalId,hospitalName:s,orderCreationTime:a,sourceName:e.source,orderNum:e.orderCode,invoiceItemNo:e.itemNo,drugCatalogName:e.catalogName,noticeNum:null==e.noticeNum?1:Number(e.noticeNum+1)},n=i.default.baseContext+"/supervise/supOfficial/sendTemplate/insurance_fund_notice";t.$http_post(n,o,!0).then(function(e){1==e.state?(t.$message({type:"success",message:"通知成功！"}),t.onInvoiceQuery(),l.close()):(l.close(),t.$alert(e.message))})})},onInvoicePageClick:function(e){this.invoiceParams.page=e,this.onInvoiceQuery()},onInvoiceQuery:function(){var e=this,t=this.invoiceParams,a=this.openLoading(),s=i.default.baseContext+"/supervise/supInvoiceItem/getInvoiceItemList";this.$http_post(s,t).then(function(t){1==t.state?(e.invoiceItemDataList=t.rows,e.invoiceParams.records=t.records,a.close()):(a.close(),e.$alert(t.message))})},onInvoiceSearch:function(e){"reset"==e?(this.invoiceParams.deliveryName="",this.invoiceParams.hospitalName="",this.invoiceParams.invoiceDate="",this.invoiceParams.source="",this.onInvoiceQuery()):(this.invoiceParams.page=1,this.onInvoiceQuery())},getSummaries:function(e){var t=this,a=e.columns,i=e.data,s=[];return a.forEach(function(e,a){if(0===a)s[a]="汇总";else if(9===a||7===a){var l=i.map(function(t){return Number(t[e.property])});l.every(function(e){return isNaN(e)})||(s[a]=l.reduce(function(e,a){return isNaN(a)?e:t.numAdd(e,a)},0))}}),s},numAdd:function(e,t){var a,i,s,l;try{a=e.toString().split(".")[1].length}catch(e){a=0}try{i=t.toString().split(".")[1].length}catch(e){i=0}if(l=Math.abs(a-i),s=Math.pow(10,Math.max(a,i)),l>0){var o=Math.pow(10,l);a>i?(e=Number(e.toString().replace(".","")),t=Number(t.toString().replace(".",""))*o):(e=Number(e.toString().replace(".",""))*o,t=Number(t.toString().replace(".","")))}else e=Number(e.toString().replace(".","")),t=Number(t.toString().replace(".",""));return(e+t)/s}}}},KfOo:function(e,t,a){"use strict";a.r(t);var i=a("nGer"),s=a("XkTz");for(var l in s)["default"].indexOf(l)<0&&function(e){a.d(t,e,function(){return s[e]})}(l);a("Yxq0");var o=a("gp09"),n=Object(o.a)(s.default,i.render,i.staticRenderFns,!1,null,"5903723a",null);t.default=n.exports},XkTz:function(e,t,a){"use strict";a.r(t);var i=a("0WkF"),s=a.n(i);for(var l in i)["default"].indexOf(l)<0&&function(e){a.d(t,e,function(){return i[e]})}(l);t.default=s.a},Yxq0:function(e,t,a){"use strict";a("ixqA")},f90m:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.staticRenderFns=t.render=void 0;var i=function(e){return e&&e.__esModule?e:{default:e}}(a("/umX"));t.render=function(){var e,t=this,a=t._self._c;return a("div",{ref:"tableH",staticClass:"content"},[a("div",{staticClass:"search-header flex-row"},[a("div",{staticClass:"left"},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("通用名")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入通用名"},model:{value:t.invoiceParams.catalogName,callback:function(e){t.$set(t.invoiceParams,"catalogName",e)},expression:"invoiceParams.catalogName"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("配送企业")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",disabled:this.deliveryAdmin,placeholder:"请输入医疗机构"},model:{value:t.invoiceParams.deliveryName,callback:function(e){t.$set(t.invoiceParams,"deliveryName",e)},expression:"invoiceParams.deliveryName"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("医疗机构")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:(e={size:"small",clearable:""},(0,i.default)(e,"clearable",""),(0,i.default)(e,"filterable",""),(0,i.default)(e,"placeholder","请选择医疗机构"),e),model:{value:t.invoiceParams.hospitalName,callback:function(e){t.$set(t.invoiceParams,"hospitalName",e)},expression:"invoiceParams.hospitalName"}},t._l(t.hospitalList,function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}),1)],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("采购平台")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择平台"},model:{value:t.invoiceParams.source,callback:function(e){t.$set(t.invoiceParams,"source",e)},expression:"invoiceParams.source"}},[a("el-option",{key:"1",attrs:{label:"深圳市药品交易平台",value:"1"}}),a("el-option",{key:"2",attrs:{label:"广东省药品交易平台",value:"2"}}),a("el-option",{key:"3",attrs:{label:"广州市药品交易平台",value:"3"}})],1)],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("是否集采")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择"},model:{value:t.invoiceParams.country,callback:function(e){t.$set(t.invoiceParams,"country",e)},expression:"invoiceParams.country"}},[a("el-option",{key:"1",attrs:{label:"是",value:"1"}}),a("el-option",{key:"2",attrs:{label:"否",value:"0"}})],1)],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("订单编号")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入订单编号"},model:{value:t.invoiceParams.orderCode,callback:function(e){t.$set(t.invoiceParams,"orderCode",e)},expression:"invoiceParams.orderCode"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("配送单号")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入配送单号"},model:{value:t.invoiceParams.deliveryCode,callback:function(e){t.$set(t.invoiceParams,"deliveryCode",e)},expression:"invoiceParams.deliveryCode"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("发票号")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入发票号"},model:{value:t.invoiceParams.itemNo,callback:function(e){t.$set(t.invoiceParams,"itemNo",e)},expression:"invoiceParams.itemNo"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[t._v("开票时间")]),a("div",{staticClass:"searchInput"},[a("el-date-picker",{attrs:{size:"small","value-format":"yyyy-MM-dd HH:mm:ss",type:"daterange","picker-options":t.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:t.invoiceParams.invoiceDate,callback:function(e){t.$set(t.invoiceParams,"invoiceDate",e)},expression:"invoiceParams.invoiceDate"}})],1)])])],1)],1),a("div",{staticClass:"right"},[a("div",{staticStyle:{"margin-bottom":"5px"}},[t.$route.query.type?t._e():a("el-button",{attrs:{size:"small",type:"warning"},on:{click:t.createSettlement}},[t._v("生成直接结算单\n                ")])],1),a("div",[a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:t.onInvoiceSearch}},[t._v("查询")]),a("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(e){return t.onInvoiceSearch("reset")}}},[t._v("重置")])],1)])]),a("el-row",{staticClass:"con"},[a("div",{staticClass:"con-left"},[a("el-table",{ref:"multipleTable",staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{"row-key":function(e){return e.id},data:t.invoiceItemDataList,height:t.tableHeight,border:""},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{"reserve-selection":!0,type:"selection",width:"50"}}),a("el-table-column",{attrs:{type:"index",label:"序号",width:"50"}}),a("el-table-column",{attrs:{prop:"code",label:"发票代码",width:"120"}}),a("el-table-column",{attrs:{prop:"itemNo",label:"发票号",width:"120"}}),a("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"180",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",[t._v("\n                            "+t._s(t.getDictItemName(e.row.source))+"\n                        ")])]}}])}),a("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单号",width:"120"}}),a("el-table-column",{attrs:{prop:"deliveryName",label:"配送企业",width:"180"}}),a("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"150"}}),a("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150"}}),a("el-table-column",{attrs:{prop:"num",label:"开票数量"}}),a("el-table-column",{attrs:{prop:"unitPrice",label:"单价"}}),a("el-table-column",{attrs:{prop:"taxesAmount",label:"金额"}}),a("el-table-column",{attrs:{prop:"orderCode",label:"订单编号",width:"120"}}),a("el-table-column",{attrs:{prop:"invoiceDate",label:"入库时间",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("formatTime")(e.row.stockInTime)))])]}}])}),a("el-table-column",{attrs:{prop:"country",label:"来源",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return["1"==e.row.country?a("div",[a("el-tag",{attrs:{type:"success"}},[t._v("国家集采")])],1):t._e(),"0"==e.row.country?a("div",[a("el-tag",{attrs:{type:"warning"}},[t._v("非国家集采")])],1):t._e(),"2"==e.row.country?a("div",[a("el-tag",{attrs:{type:"primary"}},[t._v("线下采购")])],1):t._e()]}}])}),a("el-table-column",{attrs:{prop:"payStatus",label:"回款状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return["1"==e.row.payStatus?a("div",[a("el-tag",{attrs:{type:"success"}},[t._v("已回款")])],1):t._e(),"2"==e.row.payStatus?a("div",[a("el-tag",{attrs:{type:"warning"}},[t._v("部分回款")])],1):t._e(),"0"!=e.row.payStatus&&e.row.payStatus?t._e():a("div",[a("el-tag",{attrs:{type:"danger"}},[t._v("未回款")])],1)]}}])}),a("el-table-column",{attrs:{label:"超时天数",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                        "+t._s(null==e.row.overDay?30:-e.row.overDay)+"\n                    ")]}}])}),a("el-table-column",{attrs:{label:"已通知次数",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                        "+t._s(null==e.row.noticeNum?0:e.row.noticeNum)+"\n                    ")]}}])}),t.$route.query.type?t._e():a("el-table-column",{attrs:{label:"操作",align:"center",width:"160",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.officialNotice(e.row)}}},[t._v("公文通知")]),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.phoneNotice(e.row)}}},[t._v("短信提醒")])]}}],null,!1,2141717331)})],1),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{total:t.invoiceParams.records,"page-size":t.invoiceParams.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onInvoicePageClick}})],1)],1)]),a("el-dialog",{attrs:{title:"确认直接结算单",visible:t.showBox,width:"90%"},on:{close:function(e){t.showBox=!1}}},[a("div",{staticClass:"dialog-content"},[a("el-table",{ref:"confirmTable",staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{"show-summary":"","summary-method":t.getSummaries,data:t.confirmDataList,border:""}},[a("el-table-column",{attrs:{type:"index",label:"序号",width:"50"}}),a("el-table-column",{attrs:{prop:"code",label:"发票代码",width:"120"}}),a("el-table-column",{attrs:{prop:"itemNo",label:"发票号",width:"120"}}),a("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"180",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",[t._v("\n                            "+t._s(t.getDictItemName(e.row.source))+"\n                        ")])]}}])}),a("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单号",width:"120"}}),a("el-table-column",{attrs:{prop:"deliveryName",label:"配送企业",width:"180"}}),a("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"150"}}),a("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150"}}),a("el-table-column",{attrs:{prop:"num",label:"开票数量"}}),a("el-table-column",{attrs:{prop:"unitPrice",label:"单价"}}),a("el-table-column",{attrs:{prop:"taxesAmount",label:"金额"}}),a("el-table-column",{attrs:{prop:"orderCode",label:"订单编号",width:"120"}}),a("el-table-column",{attrs:{prop:"invoiceDate",label:"入库时间",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("formatTime")(e.row.stockInTime)))])]}}])}),a("el-table-column",{attrs:{prop:"country",label:"来源",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return["1"==e.row.country?a("div",[a("el-tag",{attrs:{type:"success"}},[t._v("国家集采")])],1):t._e(),"0"==e.row.country?a("div",[a("el-tag",{attrs:{type:"warning"}},[t._v("非国家集采")])],1):t._e(),"2"==e.row.country?a("div",[a("el-tag",{attrs:{type:"primary"}},[t._v("线下采购")])],1):t._e()]}}])}),a("el-table-column",{attrs:{prop:"payStatus",label:"回款状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return["1"==e.row.payStatus?a("div",[a("el-tag",{attrs:{type:"success"}},[t._v("已回款")])],1):t._e(),"2"==e.row.payStatus?a("div",[a("el-tag",{attrs:{type:"warning"}},[t._v("部分回款")])],1):t._e(),"0"!=e.row.payStatus&&e.row.payStatus?t._e():a("div",[a("el-tag",{attrs:{type:"danger"}},[t._v("未回款")])],1)]}}])}),a("el-table-column",{attrs:{label:"超时天数",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                        "+t._s(null==e.row.overDay?30:-e.row.overDay)+"\n                    ")]}}])}),a("el-table-column",{attrs:{label:"已通知次数",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                        "+t._s(null==e.row.noticeNum?0:e.row.noticeNum)+"\n                    ")]}}])})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.showBox=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.confirmSettlement}},[t._v("确 定")])],1)])],1)},t.staticRenderFns=[]},ixqA:function(e,t,a){},nGer:function(e,t,a){"use strict";var i=a("f90m");a.o(i,"render")&&a.d(t,"render",function(){return i.render}),a.o(i,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return i.staticRenderFns})}}]);