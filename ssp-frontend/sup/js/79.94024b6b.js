(window.webpackJsonp=window.webpackJsonp||[]).push([[79],{"4By/":function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left flex-row"},[e("span",[t._v("通用名")]),e("el-input",{attrs:{placeholder:"请输入通用名"},model:{value:t.drug.params.catalogName,callback:function(e){t.$set(t.drug.params,"catalogName",e)},expression:"drug.params.catalogName"}}),e("span",[t._v("药品规格")]),e("el-input",{attrs:{placeholder:"请输入药品规格"},model:{value:t.drug.params.specs,callback:function(e){t.$set(t.drug.params,"specs",e)},expression:"drug.params.specs"}}),e("span",[t._v("剂型")]),e("el-input",{attrs:{placeholder:"请输入药品剂型"},model:{value:t.drug.params.dosageForm,callback:function(e){t.$set(t.drug.params,"dosageForm",e)},expression:"drug.params.dosageForm"}}),e("span",[t._v("类别")]),e("el-select",{attrs:{placeholder:"请选择药品类别"},model:{value:t.drug.params.category,callback:function(e){t.$set(t.drug.params,"category",e)},expression:"drug.params.category"}},[e("el-option",{attrs:{label:"",value:""}}),e("el-option",{attrs:{label:"中药",value:"1"}}),e("el-option",{attrs:{label:"西药",value:"2"}})],1),e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.onDrugSearch}},[t._v("\n                查询\n              ")]),e("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(e){return t.onDrugSearch("reset")}}},[t._v("重置\n              ")])],1),e("el-badge",{staticClass:"item",attrs:{value:t.orderData.orderItem.length,max:99}},[e("el-button",{attrs:{type:"success",icon:"el-icon-shopping-cart-2"},on:{click:t.goToCart}},[t._v("购物车")])],1)],1),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.drug.list,"highlight-current-row":"",height:t.tableHeight}},[e("el-table-column",{attrs:{prop:"catalogName",label:"通用名"}}),e("el-table-column",{attrs:{prop:"specs",label:"规格"}}),e("el-table-column",{attrs:{prop:"packingSpecs",label:"包装规格"}}),e("el-table-column",{attrs:{prop:"dosageForm",label:"剂型"}}),e("el-table-column",{attrs:{prop:"category",label:"类别",width:"200",aligh:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return["1"==r.row.category?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("中药")])],1):t._e(),"2"==r.row.category?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("西药")])],1):t._e(),"1"!=r.row.category&&"2"!=r.row.category?e("div",[e("el-tag",{attrs:{type:"info"}},[t._v("其它")])],1):t._e()]}}])}),e("el-table-column",{attrs:{label:"操作",width:"100"},scopedSlots:t._u([{key:"default",fn:function(r){return[1==r.row.isSelected?e("el-tag",{attrs:{type:"info",round:"",plain:"",size:"small"}},[t._v("已选择\n            ")]):t._e(),1!=r.row.isSelected?e("el-button",{attrs:{type:"primary",round:"",plain:"",size:"small"},on:{click:function(e){return t.addOrder(r.row)}}},[t._v("选择药品\n            ")]):t._e()]}}])})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.drug.params.records,"page-size":t.drug.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onDrugPageClick}})],1)],1)]),t.drugBox?e("el-dialog",{attrs:{title:"购物车",visible:t.drugBox,"close-on-click-modal":!1,"close-on-press-escape":!1,width:"90%"},on:{"update:visible":function(e){t.drugBox=e}}},[e("div",{staticClass:"orderBox-dialog-content"},[e("el-form",{ref:"form",staticClass:"item-form",attrs:{"label-width":"90px",id:"orderData",rules:t.rules,model:t.orderData}},[e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.orderData.orderItem,"highlight-current-row":""}},[e("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"180",align:"left"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("div",[e("el-tag",{attrs:{type:"-1"==r.row.source?"warning":"success"}},[t._v(t._s(t.getDictItemName(r.row.source)))])],1)]}}],null,!1,3035575222)}),e("el-table-column",{attrs:{prop:"busiCode",label:"药品编码",width:"140",align:"center"}}),e("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"200",align:"center"}}),e("el-table-column",{attrs:{prop:"category",label:"类别",width:"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return["1"==r.row.category?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("中药")])],1):t._e(),"2"==r.row.category?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("西药")])],1):t._e(),"1"!=r.row.category&&"2"!=r.row.category?e("div",[e("el-tag",{attrs:{type:"info"}},[t._v("其它")])],1):t._e()]}}],null,!1,*********)}),e("el-table-column",{attrs:{prop:"specs",label:"规格",width:"100",align:"center"}}),e("el-table-column",{attrs:{prop:"packingSpecs",label:"包装规格",width:"130",align:"center"}}),e("el-table-column",{attrs:{prop:"dosageForm",label:"剂型",width:"120",align:"center"}}),e("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",align:"center"}}),e("el-table-column",{attrs:{label:"最小单位价(元)",width:"130",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                    "+t._s(t.getPrice(e.row.packingSpecs,e.row.unitPrice))+"\n                  ")]}}],null,!1,1313618287)}),e("el-table-column",{attrs:{prop:"unitPrice",label:"商品价(元)",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[t._v("\n                    "+t._s(r.row.unitPrice)+"\n                    "),"0"==r.row.systemContrast?e("el-popover",{attrs:{placement:"top-start",title:"非系统筛选药品   ",width:"200",trigger:"hover",content:"原因："+r.row.reason}},[e("el-tag",{attrs:{slot:"reference",type:"danger"},slot:"reference"},[t._v("预警")])],1):t._e()]}}],null,!1,*********)}),e("el-table-column",{attrs:{label:"操作",align:"right",width:"150"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-button",{attrs:{type:"warning",size:"small"},on:{click:function(e){return t.delOrder(r.$index,t.orderData.orderItem)}}},[t._v("\n                      删除\n                    ")]),e("el-popover",{staticStyle:{"margin-left":"10px"},attrs:{placement:"left",title:"更多操做",width:"290",trigger:"hover"}},[e("el-row",{staticStyle:{"margin-bottom":"5px"}},[e("el-col",{attrs:{span:12}},[e("el-button",{attrs:{type:"info",size:"small"},on:{click:function(e){return t.showDrugDetail(r.row)}}},[t._v("查看药品详情\n                          ")])],1)],1),e("el-button",{attrs:{slot:"reference",type:"primary",size:"small"},slot:"reference"},[t._v("更多")])],1)]}}],null,!1,920187118)})],1)],1)],1),e("div",{staticClass:"orderBox-dialog-footer flex-row"},[e("span"),e("span",[e("el-button",{on:{click:function(e){t.drugBox=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"success"},on:{click:t.validOrderData}},[t._v("生成推荐单")])],1)])]):t._e(),t.orderBox?e("el-dialog",{attrs:{title:"推荐单明细",visible:t.orderBox,"close-on-click-modal":!1,"close-on-press-escape":!1,width:"90%"},on:{"update:visible":function(e){t.orderBox=e}}},[e("div",{staticClass:"orderBox-dialog-content"},[e("el-form",{ref:"confirmOrderDataForm",staticClass:"item-form",attrs:{id:"confirmOrderData",rules:t.rules,model:t.confirmOrderData,"label-width":"90px"}},t._l(t.confirmOrderData.source,function(r,a,o){return e("div",{staticClass:"orderBox-content"},[e("div",{staticStyle:{"font-size":"16px",margin:"5px"}},[t._v("\n                          【"),e("span",[t._v(" "+t._s(t.getDictItemName(a)))]),t._v("】订单 :\n                      ")]),e("div",{staticClass:"header-table"},[e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.confirmOrderData.source[a].orderItem,border:"","highlight-current-row":""}},[e("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                                      "+t._s(t.getDictItemName(e.row.source))+"\n                                  ")]}}],null,!0)}),e("el-table-column",{attrs:{prop:"busiCode",label:"采购平台平台编码",width:"150"}}),e("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"200"}}),e("el-table-column",{attrs:{prop:"category",label:"类别"},scopedSlots:t._u([{key:"default",fn:function(r){return["1"==r.row.category?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("中药")])],1):t._e(),"2"==r.row.category?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("西药")])],1):t._e(),"1"!=r.row.category&&"2"!=r.row.category?e("div",[e("el-tag",{attrs:{type:"info"}},[t._v("其它")])],1):t._e()]}}],null,!0)}),e("el-table-column",{attrs:{prop:"specs",label:"规格"}}),e("el-table-column",{attrs:{prop:"packingSpecs",label:"包装规格"}}),e("el-table-column",{attrs:{prop:"dosageForm",label:"剂型"}}),e("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"200"}}),e("el-table-column",{attrs:{prop:"unitPrice",label:"最小单位价(元)"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                                    "+t._s(t.getPrice(e.row.packingSpecs,e.row.unitPrice))+"\n                                  ")]}}],null,!0)}),e("el-table-column",{attrs:{prop:"unitPrice",label:"商品价(元)"}})],1)],1),e("div",{staticClass:"fromBox",staticStyle:{"margin-top":"10px"}},[e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"医疗机构"}},[t._v("\n                                      "+t._s(t.hospital.name)+"\n                                  ")])],1),e("el-col",{attrs:{span:12}},[e("el-button",{staticStyle:{"margin-right":"10px","margin-top":"6px",float:"right"},attrs:{type:"primary"},on:{click:function(e){return t.goToPlace(a)}}},[t._v("前往下单")])],1)],1)],1)])}),0)],1),e("div",{staticClass:"orderBox-dialog-footer flex-row"},[e("span",[t._v("注：同一采购平台的药品生成一个订单。")]),e("span",[e("el-button",{on:{click:function(e){t.orderBox=!1}}},[t._v("取 消")])],1)])]):t._e(),e("el-dialog",{attrs:{title:"【"+t.drugDetail.catalogName+"】药品详情",visible:t.showDrugDetailBox,width:"45%"},on:{"update:visible":function(e){t.showDrugDetailBox=e}}},[e("div",[e("el-form",{ref:"showDrugDetailForm",staticClass:"item-form",attrs:{model:t.drugDetail,"label-width":"110px"}},[e("div",{staticClass:"fromBox"},[e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"采购平台"}},[t._v("\n                                  "+t._s(t.getDictItemName(t.drugDetail.source))+"\n                              ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"药品本位码"}},[t._v("\n                                  "+t._s(t.drugDetail.standardCode)+"\n                              ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"通用名"}},[t._v("\n                                  "+t._s(t.drugDetail.catalogName)+"\n                              ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"商品名"}},[t._v("\n                                  "+t._s(t.drugDetail.goodsName)+"\n                              ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"商品价格"}},[t._v("\n                                  "+t._s(t.drugDetail.unitPrice+"元")+"\n                              ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"最小单位价"}},[t._v("\n                                  "+t._s(t.getPrice(t.drugDetail.packingSpecs,t.drugDetail.unitPrice)+"元")+"\n                              ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"药品编码"}},[t._v("\n                                  "+t._s(t.drugDetail.code)+"\n                              ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"药品类别"}},["1"==t.drugDetail.category?e("span",[t._v("中药")]):t._e(),"2"==t.drugDetail.category?e("span",[t._v("西药")]):t._e(),"1"!=t.drugDetail.category&&"2"!=t.drugDetail.category?e("span",[t._v("其它")]):t._e()])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"剂型"}},[t._v("\n                                  "+t._s(t.drugDetail.dosageForm)+"\n                              ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"规格"}},[t._v("\n                                  "+t._s(t.drugDetail.specs)+"\n                              ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"包装规格"}},[t._v("\n                                  "+t._s(t.drugDetail.packingSpecs)+"\n                              ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"单位"}},[t._v("\n                                  "+t._s(t.drugDetail.unit)+"\n                              ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"医保目录"}},["1"==t.drugDetail.medicalInsurance?e("span",[t._v("甲类")]):t._e(),"2"==t.drugDetail.medicalInsurance?e("span",[t._v("乙类")]):t._e()])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"国家集中集采"}},["1"==t.drugDetail.country?e("span",[t._v("是")]):t._e(),"0"==t.drugDetail.country?e("span",[t._v("否")]):t._e()])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"生产企业",prop:"company"}},[t._v("\n                                  "+t._s(t.drugDetail.drugCompanyName)+"\n                              ")])],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"批准文号"}},[t._v("\n                                  "+t._s(t.drugDetail.approvalNumber)+"\n                              ")])],1)],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{staticClass:"col-left",attrs:{label:"备注"}},[t._v("\n                                 "+t._s(t.drugDetail.remark)+"\n                              ")])],1)],1)],1)])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.showDrugDetailBox=!1,t.drugDetail={}}}},[t._v("关 闭")])],1)]),e("el-dialog",{attrs:{title:"重选药品",visible:t.showOtherDrugBox,width:"90%"},on:{"update:visible":function(e){t.showOtherDrugBox=e}}},[e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.otherDrug.list,"highlight-current-row":"",height:.75*t.tableHeight}},[e("el-table-column",{attrs:{prop:"source",align:"left",label:"采购平台",width:"180"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("div",[e("el-tag",{attrs:{type:"-1"==r.row.source?"warning":"success"}},[t._v(t._s(t.getDictItemName(r.row.source)))])],1)]}}])}),e("el-table-column",{attrs:{prop:"busiCode",align:"center",label:"药品编码",width:"100"}}),e("el-table-column",{attrs:{prop:"catalogName",align:"center",label:"通用名",width:"180"}}),e("el-table-column",{attrs:{align:"center",prop:"country",label:"是否国家集中集采"},scopedSlots:t._u([{key:"default",fn:function(r){return["1"==r.row.country?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("是")])],1):e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("否")])],1)]}}])}),e("el-table-column",{attrs:{prop:"category",label:"类别",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return["1"==r.row.category?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("中药")])],1):t._e(),"2"==r.row.category?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("西药")])],1):t._e(),"1"!=r.row.category&&"2"!=r.row.category?e("div",[e("el-tag",{attrs:{type:"info"}},[t._v("其它")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"specs",label:"规格",align:"center"}}),e("el-table-column",{attrs:{prop:"packingSpecs",label:"包装规格",align:"center"}}),e("el-table-column",{attrs:{prop:"dosageForm",label:"剂型",align:"center"}}),e("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"180",align:"center"}}),e("el-table-column",{attrs:{prop:"unitPrice",label:"最小单位价(元)",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                      "+t._s(t.getPrice(e.row.packingSpecs,e.row.unitPrice))+"\n                  ")]}}])}),e("el-table-column",{attrs:{prop:"unitPrice",label:"商品价(元)",align:"center"}}),e("el-table-column",{attrs:{label:"操作",width:"100"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-button",{attrs:{type:"success",round:"",plain:"",size:"small"},on:{click:function(e){return t.showOtherDrugReason(r.row)}}},[t._v("选择药品\n                      ")])]}}])})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.drug.params.records,"page-size":t.drug.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onDrugPageClick}})],1)],1)]),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.showOtherDrugBox=!1}}},[t._v("关 闭")])],1)],1),e("el-dialog",{attrs:{title:"原因",visible:t.showOtherDrugReasonBox,width:"20%"},on:{"update:visible":function(e){t.showOtherDrugReasonBox=e}}},[e("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请输入原因"},model:{value:t.otherDrug.reason,callback:function(e){t.$set(t.otherDrug,"reason",e)},expression:"otherDrug.reason"}}),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.showOtherDrugReasonBox=!1}}},[t._v("关 闭")]),e("el-button",{attrs:{type:"primary"},on:{click:t.editOrder}},[t._v("确 定")])],1)],1)],1)},e.staticRenderFns=[]},"8E4C":function(t,e,r){"use strict";var a=r("4By/");r.o(a,"render")&&r.d(e,"render",function(){return a.render}),r.o(a,"staticRenderFns")&&r.d(e,"staticRenderFns",function(){return a.staticRenderFns})},"K+bz":function(t,e,r){Object.defineProperty(e,"__esModule",{value:!0});var a=s(r("Q9c5")),o=s(r("DWNM"));function s(t){return t&&t.__esModule?t:{default:t}}e.default={name:"place-order",mixins:[o.default],data:function(){return{drugSourceList:[],tableHeight:100,drugTableHeight:100,drugBox:!1,orderBox:!1,showDrugDetailBox:!1,showOtherDrugBox:!1,showOtherDrugReasonBox:!1,orderData:{orderItem:[],totalPrice:0},confirmOrderData:{},hospital:{},orderParams:{page:1,limit:10,records:0},drug:{list:[],params:{page:1,limit:10,records:0,country:"0",catalogName:"",specs:"",dosageForm:"",category:"",hospitalId:""},catalogNameList:[]},otherDrug:{list:[],params:{page:1,limit:10,records:0},detail:{},reason:""},drugDetail:{},rules:{amount:[{validator:function(t,e,r){0==/^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/.test(e)?r(new Error("请输入大于零的数量")):r()},trigger:"change"},{validator:function(t,e,r){0==/^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/.test(e)?r(new Error("请输入大于零的数量")):r()},trigger:"blur"},{type:"number",required:!0,message:"采购数量不能为空",trigger:"blur"}]}}},mounted:function(){var t=this;this.getDictItem("SOURCE"),this.getHospital(),this.onDrugQuery(),this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}},methods:{goToCart:function(){this.drugBox=!0},goToPlace:function(t){var e="https://tradejm.qywgpo.com/";2==t&&(e="http://yp.gdyjs.cn:9020/"),window.open(e,"_blank")},getPrice:function(t,e){if(t){var r=t.match(/\d+/g);return e?(e/r.join("")).toFixed(3):""}return""},getDictItemName:function(t){for(var e=0;e<this.drugSourceList.length;e++)if(this.drugSourceList[e].value==t)return this.drugSourceList[e].label},getDictItem:function(t){var e=this,r=this.openLoading();this.$http_post(a.default.baseContext+"/supervise/supDictItem/getItemList",{code:t}).then(function(a){var o=a.rows;1==a.state?("SOURCE"==t&&(e.drugSourceList=o),r.close()):(r.close(),e.$message.error(a.message))})},addOrder:function(t){var e=this,r=this.openLoading("系统比价中..."),o={catalogId:t.catalogId,dosageFormGroup:t.dosageFormGroup};this.$http_post(a.default.baseContext+"/supervise/supOrder/contrastPrice",o,!0).then(function(t){if(1==t.state){var a=e;clearTimeout(o),o=setTimeout(function(){a.orderData.orderItem.push(t.row),a.onDrugQuery(),r.close(),a.$message.success("添加购物车成功")},1e3)}else{var o;a=e;clearTimeout(o),o=setTimeout(function(){a.$alert(t.message),r.close()},500)}})},delOrder:function(t,e){e.splice(t,1)},showDrugDetail:function(t){this.drugDetail=t,this.showDrugDetailBox=!0},showOtherDrugReason:function(t){var e=this;if(null==this.otherDrug.orderItemIndex)return this.$alert("下单失败，请重新选择药品"),!1;var r={catalogId:t.catalogId,dosageFormGroup:t.dosageFormGroup},o=this;this.$http_post(a.default.baseContext+"/supervise/supOrder/contrastPrice",r,!0).then(function(r){if(1==r.state)if(o.otherDrug.detail=t,r.row.unitPrice<t.unitPrice)e.showOtherDrugReasonBox=!0;else{var a=o.otherDrug.orderItemIndex;o.otherDrug.detail.reason=o.otherDrug.reason,o.otherDrug.reason="",o.otherDrug.detail.systemContrast="1",o.orderData.orderItem.splice(a,1,o.otherDrug.detail),o.showOtherDrugReasonBox=!1,o.showOtherDrugBox=!1}else e.$alert(r.message),rLoading.close()})},editOrder:function(){if(null==this.otherDrug.reason||0==this.otherDrug.reason.length)return this.$alert("原因不能为空"),!1;var t=this.otherDrug.orderItemIndex;this.otherDrug.detail.reason=this.otherDrug.reason,this.otherDrug.reason="",this.otherDrug.detail.systemContrast="0",this.orderData.orderItem.splice(t,1,this.otherDrug.detail),this.showOtherDrugReasonBox=!1,this.showOtherDrugBox=!1},selectOtherDrug:function(t,e){var r=this;this.otherDrug.params.country=e.country,this.otherDrug.params.catalogName=e.catalogName,this.otherDrug.params.specs=e.specs,this.otherDrug.params.dosageForm=e.dosageForm,this.otherDrug.params.category=e.category;var o=a.default.baseContext+"/supervise/supOrder/selectOtherDrug?page="+this.otherDrug.params.page+"&limit="+this.otherDrug.params.limit;this.$http_post(o,this.otherDrug.params,!0).then(function(e){1==e.state?(r.showOtherDrugBox=!0,r.otherDrug.list=e.rows,r.otherDrug.orderItemIndex=t,r.otherDrug.orderItemIndex.reason="",r.otherDrug.params.records=e.records):(r.$alert(e.message),rLoading.close())})},validOrderData:function(){var t=this;if(null==this.orderData.orderItem||0==this.orderData.orderItem.length)return this.$alert("请先选择药品"),!1;var e={source:{},hospital:this.hospital};for(var r in this.orderData.orderItem.forEach(function(t,r){t.itemPrice=t.amount*t.unitPrice;var a=t.source;e.source[a]||(e.source[a]={orderItem:[],totalPrice:0}),e.source[a].orderItem.push(t)}),e.source){var a=0;e.source[r].orderItem.forEach(function(t){a+=t.itemPrice}),e.source[r].totalPrice=a}this.confirmOrderData=e,this.$refs.form.validate(function(r){r&&(t.orderBox=!0,console.log("通过"),console.log(e))})},saveOrderData:function(){var t=this;this.$refs.confirmOrderDataForm.validate(function(e){if(e){var r=t.openLoading("订单提交中...");t.confirmOrderData.hospital=t.hospital,console.log(t.confirmOrderData),t.$http_post(a.default.baseContext+"/supervise/supOrder/save",t.confirmOrderData,!0).then(function(e){1==e.state?(r.close(),t.orderBox=!1,t.$confirm("下单成功，前往【我的订单】查看订单详情",{confirmButtonText:"确定",showCancelButton:!1,type:"success"}).then(function(){t.$router.push({path:"list"})}).catch(function(t){console.log(t)})):(r.close(),t.$alert(e.message))})}})},onDrugSearch:function(t){"reset"==t?(this.drug.params.country="0",this.drug.params.catalogName="",this.drug.params.specs="",this.drug.params.dosageForm="",this.drug.params.category="",this.onDrugQuery()):(this.drug.params.page=1,this.onDrugQuery())},onDrugQuery:function(){var t=this,e=this.drug.params;e.hospitalId=this.hospital.id;var r=a.default.baseContext+"/supervise/supOrder/drugList",o=this.openLoading();this.$http_post(r,e).then(function(e){1==e.state?(null!=t.orderData.orderItem&&t.orderData.orderItem.length>0&&t.orderData.orderItem.forEach(function(t){e.rows.forEach(function(e){t.catalogName==e.catalogName&&t.specs==e.specs&&t.dosageForm==e.dosageForm&&t.category==e.category&&t.packingSpecs==e.packingSpecs&&(e.isSelected=!0)})}),t.drug.list=e.rows,t.drug.params.records=e.records):t.$alert(e.message),o.close()})},onDrugPageClick:function(t){this.drug.params.page=t,this.onDrugQuery()},onOtherDrugPageClick:function(t){this.otherDrug.params.page=t,this.selectOtherDrug()},getHospital:function(){var t=this,e=this.$store.getters.curUser.id;this.$http_post(a.default.baseContext+"/supervise/supHospitalUser/userHospital/"+e,{}).then(function(e){1==e.state?(t.hospital=e.row,console.log(t.hospital)):(t.$alert(e.message),rLoading.close())})}},beforeDestroy:function(){window.onresize=null}}},NJB6:function(t,e,r){},fUNp:function(t,e,r){"use strict";r("NJB6")},gPy1:function(t,e,r){"use strict";r.r(e);var a=r("8E4C"),o=r("ho88");for(var s in o)["default"].indexOf(s)<0&&function(t){r.d(e,t,function(){return o[t]})}(s);r("fUNp");var l=r("gp09"),n=Object(l.a)(o.default,a.render,a.staticRenderFns,!1,null,"69adff8e",null);e.default=n.exports},ho88:function(t,e,r){"use strict";r.r(e);var a=r("K+bz"),o=r.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){r.d(e,t,function(){return a[t]})}(s);e.default=o.a}}]);