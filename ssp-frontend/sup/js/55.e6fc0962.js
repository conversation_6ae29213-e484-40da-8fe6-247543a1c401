(window.webpackJsonp=window.webpackJsonp||[]).push([[55],{NQYF:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"},[t("span",[e._v("配送企业名称")]),t("el-input",{attrs:{placeholder:"请输入配送企业名称"},model:{value:e.params.name,callback:function(t){e.$set(e.params,"name",t)},expression:"params.name"}}),t("span",[e._v("配送企业编码")]),t("el-input",{attrs:{placeholder:"请输入配送企业编码"},model:{value:e.params.code,callback:function(t){e.$set(e.params,"code",t)},expression:"params.code"}}),t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1),t("el-button",{attrs:{icon:"el-icon-plus",type:"primary"},on:{click:function(t){return e.add("add")}}},[e._v("新增")])],1),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.dataList,"highlight-current-row":"",height:e.tableHeight},on:{"current-change":e.handleCurrentChange}},[t("el-table-column",{attrs:{prop:"name",label:"配送企业名称"}}),t("el-table-column",{attrs:{prop:"code",label:"配送企业编码"}}),t("el-table-column",{attrs:{prop:"bankName",label:"银行账户"}}),t("el-table-column",{attrs:{prop:"bankCard",label:"银行卡号"}}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"200"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.add("edit",a.row)}}},[e._v("编辑")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.del(a.row)}}},[e._v("删除")])]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)]),t("el-dialog",{attrs:{title:"配送企业",visible:e.dialogVisible,width:"45%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",{staticClass:"dialog-content"},[t("el-form",{ref:"form",staticClass:"item-form",attrs:{model:e.formData,"label-width":"120px",rules:e.rules,"label-position":"left"}},[t("div",{staticClass:"fromBox"},[t("el-row",{attrs:{type:"flex"}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"配送企业名称",prop:"name"}},[t("el-input",{attrs:{placeholder:"配送企业名称不能为空"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"配送企业编码",prop:"code"}},[t("el-input",{attrs:{placeholder:"配送企业编码不能为空"},model:{value:e.formData.code,callback:function(t){e.$set(e.formData,"code",t)},expression:"formData.code"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{staticClass:"col-left",attrs:{label:"银行卡号",prop:"bankCard"}},[t("el-input",{attrs:{placeholder:"配送企业银行卡号不能为空"},on:{input:function(t){return e.changeBankCard()}},model:{value:e.formData.bankCard,callback:function(t){e.$set(e.formData,"bankCard",t)},expression:"formData.bankCard"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{staticClass:"col-left",attrs:{label:"银行账户",prop:"bankName"}},[t("el-input",{attrs:{readonly:"",placeholder:"根据银行卡号自动回填"},model:{value:e.formData.bankName,callback:function(t){e.$set(e.formData,"bankName",t)},expression:"formData.bankName"}})],1)],1)],1),t("el-row",{attrs:{type:"flex"}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[t("el-input",{attrs:{placeholder:"联系电话不能为空"},model:{value:e.formData.phone,callback:function(t){e.$set(e.formData,"phone",t)},expression:"formData.phone"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"排序"}},[t("el-input-number",{attrs:{min:1,max:100},model:{value:e.formData.sortOrder,callback:function(t){e.$set(e.formData,"sortOrder",t)},expression:"formData.sortOrder"}})],1)],1)],1)],1)])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确 定")])],1)])],1)},t.staticRenderFns=[]},W5tm:function(e,t,a){"use strict";a.r(t);var r=a("q41Y"),n=a.n(r);for(var s in r)["default"].indexOf(s)<0&&function(e){a.d(t,e,function(){return r[e]})}(s);t.default=n.a},oQkO:function(e,t,a){"use strict";a.r(t);var r=a("tEGf"),n=a("W5tm");for(var s in n)["default"].indexOf(s)<0&&function(e){a.d(t,e,function(){return n[e]})}(s);a("qK+w");var o=a("gp09"),l=Object(o.a)(n.default,r.render,r.staticRenderFns,!1,null,"2f9c9a58",null);t.default=l.exports},oSMY:function(e,t,a){},q41Y:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=o(a("cH4l")),n=o(a("Q9c5")),s=o(a("DWNM"));function o(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[s.default],data:function(){return{dataList:[],params:{page:1,limit:10,records:0,code:"",name:""},formData:{id:"",phone:"",bankName:"",code:"",name:"",sortOrder:1},dialogVisible:!1,tableHeight:100,rules:{name:[{required:!0,message:"配送企业名称不能为空",trigger:"blur"}],code:[{required:!0,message:"配送企业编码不能为空",trigger:"blur"}],bankCard:[{required:!0,message:"配送企业银行卡号不能为空",trigger:"blur"}],bankName:[{required:!0,message:"根据银行卡号自动回填",trigger:"blur"}],phone:[{required:!0,message:"配送企业电话不能为空",trigger:"blur"}]}}},props:{},computed:{},watch:{},methods:{changeBankCard:function(){var e=r.default.getBankName(this.formData.bankCard);this.formData.bankName=e.bankName},changeStatus:function(e,t){var a=this,r={id:e,status:t},s=this.openLoading(),o=n.default.baseContext+"/supervise/supDeliveryCompany/updateStatus";this.$http_post(o,r).then(function(e){1==e.state?(a.$message.success("操作成功"),a.onQuery()):a.$alert(e.message),s.close()})},add:function(e,t){var a=this;if("add"==e&&(this.formData={id:"",phone:"",bankName:"",bankCard:"",code:"",name:"",sortOrder:1},this.dialogVisible=!0),"edit"==e){var r=this.openLoading();this.$http_post(n.default.baseContext+"/supervise/supDeliveryCompany/info/"+t.id,{}).then(function(e){1==e.state?null!=e.row?(a.formData={id:e.row.id,phone:e.row.phone,bankName:e.row.bankName,bankCard:e.row.bankCard,code:e.row.code,name:e.row.name,sortOrder:e.row.sortOrder},a.dialogVisible=!0):a.$message.error("系统异常"):null!=e.message?a.$message.error(e.message):a.$message.error("系统异常"),r.close()})}},save:function(){var e=this;this.$refs.form.validate(function(t){if(!t)return!1;var a="";a=null!=e.formData.id&&""!=e.formData.id?n.default.baseContext+"/supervise/supDeliveryCompany/update":n.default.baseContext+"/supervise/supDeliveryCompany/save";var r=e.openLoading();console.log(111,e.formData.bankName),e.$http_post(a,e.formData).then(function(t){1==t.state?(-1!=a.indexOf("update")?e.$message.success("修改成功"):e.$message.success("添加成功"),e.onQuery(),e.dialogVisible=!1):e.$alert(t.message),r.close()})})},del:function(e){var t=this;this.$alert("确定删除【"+e.name+"】数据吗","确认信息",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,type:"warning"}).then(function(){var a=t.openLoading();t.$http_post(n.default.baseContext+"/supervise/supDeliveryCompany/delete/"+e.id,null).then(function(e){1==e.state?(t.$message.success("删除成功"),t.onQuery(),a.close()):(a.close(),t.$message.error("删除失败，请稍后再试"))})}).catch(function(e){console.log(e)})},handleCurrentChange:function(e){},onSearch:function(e){"reset"==e?(this.params.code="",this.params.name="",this.onQuery()):""!=this.params.code||""!=this.params.name?(this.params.page=1,this.onQuery()):this.$message.warning("请输入配送企业名称或编码查询")},onPageClick:function(e){this.params.page=e,this.onQuery()},onQuery:function(){var e=this,t=this.params;this.isLink&&(t.isLink="0");var a=this.openLoading(),r=n.default.baseContext+"/supervise/supDeliveryCompany/list";this.$http_post(r,t).then(function(t){if(1==t.state){var r=t.rows;e.dataList=r,e.params.records=t.records,a.close()}else a.close(),e.$alert(t.message)})}},mounted:function(){var e=this;this.onQuery(),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null}}},"qK+w":function(e,t,a){"use strict";a("oSMY")},tEGf:function(e,t,a){"use strict";var r=a("NQYF");a.o(r,"render")&&a.d(t,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return r.staticRenderFns})}}]);