(window.webpackJsonp=window.webpackJsonp||[]).push([[20],{"03sJ":function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0});var n=r(o("Q9c5")),a=r(o("M6Pp")),s=r(o("tbSJ"));r(o("6Cps"));function r(e){return e&&e.__esModule?e:{default:e}}t.default={name:"user-info",components:{OrganizationTree:a.default,RoleConfig:s.default},data:function(){return{addBox:!1,mode:"",form:{id:"",account:"",name:"",phone:"",email:"",deptCode:"",deptName:"",status:0},rules:{account:[{required:!0,message:"请输入账号",trigger:"blur"}],name:[{required:!0,message:"请输入名称",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],deptCode:[{required:!0,message:"请输入机构编码",trigger:"blur"}],status:[{required:!0,message:"状态有误",trigger:"change"}]},tableData:[],selectData:[],tableHeight:100,pagination:{page:1,size:10,total:0},deptName:"",deptCode:"",inputType:"text",roleList:[],roleAddBox:!1}},watch:{},computed:{},mounted:function(){var e=this;this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-40}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-40},this.getList(),this.getSelectList()},methods:{getRoleResult:function(){var e=this,t=this.$refs.roleConfig.getResult();if(t.length>0){var o=t.join(","),a=this.openLoading(),s={userId:this.form.id,roleIds:o};this.$http_post(n.default.baseContext+"/bsp/pubUser/saveUserRole",s).then(function(t){1==t.state?(e.roleAddBox=!1,e.handleClick("role",""),e.$message.success("添加成功")):null!=t.message?e.$message.error(t.message):e.$message.error("系统异常"),a.close()}).catch(function(e){a.close(),console.log(e)})}else this.$message.error("请先选择角色")},removeRole:function(e){var t=this;this.$confirm("确认删除该角色?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var o=t.openLoading(),a={userRoleId:e.userRoleId};t.$http_post(n.default.baseContext+"/bsp/pubUser/cancelUserRole",a).then(function(e){1==e.state?(t.handleClick("role",""),t.$message.success("删除成功")):null!=e.message?t.$message.error(e.message):t.$message.error("系统异常"),o.close()}).catch(function(e){o.close(),console.log(e)})}).catch(function(e){console.log(e)})},openAdd:function(){""!=this.parentId?(this.form={id:"",account:"",name:"",phone:"",email:"",deptCode:this.deptCode,deptName:this.deptName,status:0},this.mode="add",this.addBox=!0):this.$message.error("请选择左侧行政组织机构树")},handleClick:function(e,t){var o=this;"role"==e&&this.$refs.form.validate(function(e){if(!e)throw new Error("请完善基本信息！");if(""!=o.form.id){var t=o.openLoading();o.$http_post(n.default.baseContext+"/bsp/pubUser/roleListByUser",{userId:o.form.id}).then(function(e){1==e.state?null!=e.rows&&(o.roleList=e.rows):null!=e.message?o.$message.error(e.message):o.$message.error("系统异常"),t.close()}).catch(function(e){t.close(),console.log(e)})}else o.addEdit("add",{},function(){var e=o.openLoading();o.$http_post(n.default.baseContext+"/bsp/pubUser/roleListByUser",{userId:o.form.id}).then(function(t){1==t.state?null!=t.rows&&(o.roleList=t.rows):null!=t.message?o.$message.error(t.message):o.$message.error("系统异常"),e.close()}).catch(function(t){e.close(),console.log(t)})})})},getList:function(){var e=this,t={deptCode:this.deptCode,account:this.form.name,name:"",page:this.pagination.page,limit:this.pagination.size},o=this.openLoading();this.$http_post(n.default.baseContext+"/bsp/pubUser/query",t).then(function(t){1==t.state?(e.pagination.total=t.records,null!=t.rows?e.tableData=t.rows:e.$message.error("系统异常")):null!=t.message?e.$message.error(t.message):e.$message.error("系统异常"),o.close()}).catch(function(e){o.close(),console.log(e)})},getSelectList:function(){var e=this,t={deptCode:this.deptCode,account:"",name:"",page:1,limit:300},o=this.openLoading();this.$http_post(n.default.baseContext+"/bsp/pubUser/query",t).then(function(t){1==t.state?null!=t.rows?e.selectData=t.rows:e.$message.error("系统异常"):null!=t.message?e.$message.error(t.message):e.$message.error("系统异常"),o.close()}).catch(function(e){o.close(),console.log(e)})},onSelect:function(){this.getList()},addEdit:function(e,t,o){var a=this;if("add"==e&&this.$refs.form.validate(function(e){if(!e)return!1;var t={account:a.form.account,name:a.form.name,phone:a.form.phone,email:a.form.email,deptCode:a.form.deptCode,status:a.form.status},s="/bsp/pubUser/save";null!=a.form.id&&""!=a.form.id&&(t.id=a.form.id,s="/bsp/pubUser/update");var r=a.openLoading();a.$http_post(n.default.baseContext+s,t).then(function(e){1==e.state?(void 0!=e.row&&null!=e.row&&(a.form.id=e.row.id),-1!=s.indexOf("save")?(a.$message.success("添加成功"),a.$alert("账号："+e.row.account+"初始密码："+e.row.password,"添加用户成功，请提醒用户以下信息并及时修改",{confirmButtonText:"确定",callback:function(e){a.$message({type:"success",message:"记得给用户分配角色!"})}})):a.$message.success("修改成功"),a.getList(),void 0!=o?o():a.addBox=!1):null!=e.message?a.$message.error(e.message):a.$message.error("系统异常"),r.close()}).catch(function(e){r.close(),console.log(e)})}),"edit"==e){var s=this.openLoading();this.$http_post(n.default.baseContext+"/bsp/pubUser/show",{id:t.id}).then(function(e){if(1==e.state)if(null!=e.row){var o=e.row;a.form={id:o.id,account:o.account,name:o.name,phone:o.phone,email:o.email,deptCode:o.deptCode,deptName:t.deptName,status:o.status},a.mode="edit",a.addBox=!0}else a.$message.error("系统异常");else null!=e.message?a.$message.error(e.message):a.$message.error("系统异常");s.close()}).catch(function(e){s.close(),console.log(e)})}},resetPwd:function(e){var t=this;this.$confirm("正在重置【"+e.name+"】的密码，是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var o=t.openLoading("重置中...");t.$http_post(n.default.baseContext+"/bsp/pubUser/resetPassword",{id:e.id}).then(function(e){1==e.state?(t.modifyPwd=!1,t.$message.success("重置成功")):null!=e.message?t.$message.error(e.message):t.$message.error("系统异常"),o.close()}).catch(function(e){o.close(),console.log(e)})}).catch(function(e){console.log(e)})},remove:function(e,t){var o=this;this.$confirm("确认删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var t=o.openLoading("删除中...");o.$http_post(n.default.baseContext+"/bsp/pubUser/delete",{id:e.id}).then(function(e){1==e.state?(o.$message.success("删除成功"),o.getList()):null!=e.message?o.$message.error(e.message):o.$message.error("系统异常"),t.close()}).catch(function(e){t.close(),console.log(e)})}).catch(function(e){console.log(e)})},onTree:function(e,t,o){this.deptCode=e.code,this.deptName=e.name,this.getList()},handleCurrentChange:function(e){this.pagination.page=e,this.getList()}},beforeDestroy:function(){window.onresize=null}}},"0UZL":function(e,t,o){"use strict";o("VfQU")},"3neP":function(e,t,o){"use strict";o.r(t);var n=o("cyEj"),a=o("PLXo");for(var s in a)["default"].indexOf(s)<0&&function(e){o.d(t,e,function(){return a[e]})}(s);o("0UZL");var r=o("gp09"),l=Object(r.a)(a.default,n.render,n.staticRenderFns,!1,null,"99d031f8",null);t.default=l.exports},"55Nw":function(e,t,o){"use strict";o("tI18")},"7b8W":function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"flex-row config-role"},[t("el-input",{attrs:{placeholder:"请输入角色编码"},model:{value:e.roleCode,callback:function(t){e.roleCode=t},expression:"roleCode"}}),t("el-input",{attrs:{placeholder:"请输入角色名称"},model:{value:e.roleName,callback:function(t){e.roleName=t},expression:"roleName"}}),t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")]),t("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:e.reset}},[e._v("重置")])],1),t("el-table",{ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:e.roleAddList,"tooltip-effect":"dark"},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"55"}}),t("el-table-column",{attrs:{label:"角色名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.roleName))]}}])}),t("el-table-column",{attrs:{prop:"roleCode",label:"角色编码"}})],1),t("el-pagination",{attrs:{"page-size":e.pagination.size,layout:"total, prev, pager, next, jumper",total:e.pagination.total},on:{"current-change":e.handleCurrentChange}})],1)},t.staticRenderFns=[]},AfVr:function(e,t,o){"use strict";var n=o("b9UE");o.o(n,"render")&&o.d(t,"render",function(){return n.render}),o.o(n,"staticRenderFns")&&o.d(t,"staticRenderFns",function(){return n.staticRenderFns})},BzFU:function(e,t,o){"use strict";o.r(t);var n=o("XhNf"),a=o.n(n);for(var s in n)["default"].indexOf(s)<0&&function(e){o.d(t,e,function(){return n[e]})}(s);t.default=a.a},G0ez:function(e,t,o){"use strict";o.r(t);var n=o("qkvS"),a=o.n(n);for(var s in n)["default"].indexOf(s)<0&&function(e){o.d(t,e,function(){return n[e]})}(s);t.default=a.a},M6Pp:function(e,t,o){"use strict";o.r(t);var n=o("AfVr"),a=o("BzFU");for(var s in a)["default"].indexOf(s)<0&&function(e){o.d(t,e,function(){return a[e]})}(s);o("NKlQ");var r=o("gp09"),l=Object(r.a)(a.default,n.render,n.staticRenderFns,!1,null,"7e154d66",null);t.default=l.exports},NKlQ:function(e,t,o){"use strict";o("pzka")},PLXo:function(e,t,o){"use strict";o.r(t);var n=o("03sJ"),a=o.n(n);for(var s in n)["default"].indexOf(s)<0&&function(e){o.d(t,e,function(){return n[e]})}(s);t.default=a.a},VfQU:function(e,t,o){},XhNf:function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0});var n=function(e){return e&&e.__esModule?e:{default:e}}(o("Q9c5"));t.default={name:"organization-tree",components:{},props:{expandAll:{type:Boolean,default:!1},expandedKeys:{type:Array,default:function(){return[]}},accordion:{type:Boolean,default:!1},onClickNode:{type:Boolean,default:!1},highlightCurrent:{type:Boolean,default:!0},parentCode:""},data:function(){return{data:[],props:{label:"name",children:"children",isLeaf:"leaf"},node_had:[],resolve_had:[]}},watch:{},computed:{},mounted:function(){},methods:{loadNode:function(e,t){var o=this;this.node_had=e,this.resolve_had=t;var a={parentId:"#"},s=this.parentCode;if(null!=s&&void 0!=s&&(a={code:s}),void 0!=e.data.id)a.parentId=e.data.id;else for(var r=n.default.treeCompetence,l=this.$store.getters.curUser,i=this.$store.getters.curUser.roleCode.split(","),c=0,d=i.length;c<d;c++)for(var u=0,f=r.length;u<f;u++)i[c]==r[u]&&(a={code:l.deptCode});this.$http_post(n.default.baseContext+"/bsp/pubDept/list",a).then(function(e){1==e.state?null!=e.rows?t(e.rows):o.$message.error("系统异常"):null!=e.message?o.$message.error(e.message):o.$message.error("系统异常")}).catch(function(e){console.log(e)})},requestNewData:function(){this.node_had.childNodes=[],this.loadNode(this.node_had,this.resolve_had)},onClick:function(e,t,o){this.$emit("onClick",e,t,o)},getCurrentNode:function(){return this.$refs.deptTree.getCurrentNode()}}}},b9UE:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",[t("el-tree",{ref:"deptTree",attrs:{data:e.data,"node-key":"id",load:e.loadNode,lazy:"",props:e.props,"default-expand-all":e.expandAll,"highlight-current":e.highlightCurrent,"default-expanded-keys":e.expandedKeys,accordion:e.accordion,"expand-on-click-node":e.onClickNode},on:{"node-click":e.onClick},scopedSlots:e._u([{key:"default",fn:function(o){var n=o.node;return o.data,t("span",{staticClass:"custom-tree-node"},[t("span",{staticClass:"icon el-icon-house"}),t("span",[e._v(e._s(n.label))])])}}])})],1)},t.staticRenderFns=[]},cyEj:function(e,t,o){"use strict";var n=o("eRif");o.o(n,"render")&&o.d(t,"render",function(){return n.render}),o.o(n,"staticRenderFns")&&o.d(t,"staticRenderFns",function(){return n.staticRenderFns})},eRif:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"flex-row content"},[t("div",{staticClass:"left"},[t("div",{staticClass:"title flex-row"},[t("el-select",{attrs:{clearable:"",filterable:"",placeholder:"请选择用户"},on:{change:function(t){return e.onSelect()}},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}},e._l(e.selectData,function(e){return t("el-option",{key:e.account,attrs:{label:e.name,value:e.account}})}),1),t("span",{staticClass:"name"},[e._v("行政组织机构树")]),t("el-button",{attrs:{disabled:""==this.deptCode,type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.openAdd}})],1),t("div",{staticClass:"tree"},[t("organization-tree",{on:{onClick:e.onTree}})],1)]),t("div",{staticClass:"right"},[t("el-table",{staticStyle:{width:"100%"},attrs:{border:"",height:e.tableHeight,data:e.tableData}},[t("el-table-column",{attrs:{prop:"account",label:"用户账号",width:"180"}}),t("el-table-column",{attrs:{prop:"name",label:"用户姓名"}}),t("el-table-column",{attrs:{prop:"deptName",width:"150",label:"所属区划"}}),t("el-table-column",{attrs:{width:"290",align:"right",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(o){return[t("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:function(t){return e.addEdit("edit",o.row)}}},[e._v("编辑")]),t("el-button",{attrs:{type:"warning",size:"small",icon:"el-icon-refresh"},on:{click:function(t){return e.resetPwd(o.row)}}},[e._v("重置密码")]),t("el-button",{attrs:{type:"danger",size:"small",icon:"el-icon-delete"},on:{click:function(t){return e.remove(o.row,o.$index)}}},[e._v("删除")])]}}])})],1),t("el-pagination",{attrs:{"page-size":e.pagination.size,layout:"total, prev, pager, next, jumper",total:e.pagination.total},on:{"current-change":e.handleCurrentChange}})],1),t("el-dialog",{attrs:{title:"添加用户信息",visible:e.addBox,width:"50%"},on:{"update:visible":function(t){e.addBox=t}}},[e.addBox?t("el-tabs",{attrs:{value:"base","before-leave":e.handleClick}},[t("el-tab-pane",{attrs:{label:"基本信息",name:"base"}},[t("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-position":"left","label-width":"80px"}},[t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"名称",prop:"name"}},[t("el-input",{model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),t("el-col",{staticClass:"col-left",attrs:{span:12}},[t("el-form-item",{attrs:{label:"账号",prop:"account"}},[t("el-input",{model:{value:e.form.account,callback:function(t){e.$set(e.form,"account",t)},expression:"form.account"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"电话",prop:"phone"}},[t("el-input",{model:{value:e.form.phone,callback:function(t){e.$set(e.form,"phone",t)},expression:"form.phone"}})],1)],1),t("el-col",{staticClass:"col-left",attrs:{span:12}},[t("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[t("el-input",{model:{value:e.form.email,callback:function(t){e.$set(e.form,"email",t)},expression:"form.email"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"状态",prop:"status"}},[t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}})],1)],1),t("el-col",{staticClass:"col-left",attrs:{span:12}},[t("el-form-item",{attrs:{label:"机构名称",prop:"deptCode"}},[t("el-input",{attrs:{disabled:""},model:{value:e.form.deptName,callback:function(t){e.$set(e.form,"deptName",t)},expression:"form.deptName"}})],1)],1)],1)],1)],1),t("el-tab-pane",{attrs:{label:"角色分配",name:"role"}},[t("el-table",{staticStyle:{width:"100%"},attrs:{"max-height":"250",data:e.roleList}},[t("el-table-column",{attrs:{prop:"roleName",label:"角色名称",width:"200"}}),t("el-table-column",{attrs:{prop:"roleCode",label:"角色编码"}}),t("el-table-column",{attrs:{width:"120",align:"right"},scopedSlots:e._u([{key:"header",fn:function(o){return[t("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"small"},on:{click:function(t){e.roleAddBox=!0}}},[e._v("配置角色")])]}},{key:"default",fn:function(o){return[t("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(t){return e.removeRole(o.row)}}},[e._v("删除")])]}}],null,!1,1834869512)})],1),t("el-dialog",{attrs:{width:"50%",title:"配置角色",visible:e.roleAddBox,"append-to-body":""},on:{"update:visible":function(t){e.roleAddBox=t}}},[e.roleAddBox?t("role-config",{ref:"roleConfig",attrs:{id:e.form.id}}):e._e(),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.roleAddBox=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.getRoleResult}},[e._v("确 定")])],1)],1)],1)],1):e._e(),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.addBox=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addEdit("add")}}},[e._v("确 定")])],1)],1)],1)},t.staticRenderFns=[]},kb3H:function(e,t,o){"use strict";var n=o("7b8W");o.o(n,"render")&&o.d(t,"render",function(){return n.render}),o.o(n,"staticRenderFns")&&o.d(t,"staticRenderFns",function(){return n.staticRenderFns})},pzka:function(e,t,o){},qkvS:function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0});var n=function(e){return e&&e.__esModule?e:{default:e}}(o("Q9c5"));t.default={name:"role-config",components:{},props:{value:{type:Array,default:function(){return[]}},id:{type:[Array,String],default:""}},data:function(){return{roleName:"",roleCode:"",pagination:{page:1,size:10,total:0},roleAddList:[],result:[]}},watch:{},computed:{},mounted:function(){this.getList()},methods:{search:function(){""!=this.roleName||""!=this.roleCode?this.getList():this.$message.error("请输入搜索条件")},reset:function(){this.roleName="",this.roleCode="",this.getList()},getList:function(){var e=this,t=this.openLoading(),o={userId:this.id,roleName:this.roleName,roleCode:this.roleCode,page:this.pagination.page,limit:this.pagination.size};this.$http_post(n.default.baseContext+"/bsp/pubUser/selectListNotIn",o).then(function(o){1==o.state?null!=o.rows&&(e.pagination.total=o.records,e.roleAddList=o.rows):null!=o.message?e.$message.error(o.message):e.$message.error("系统异常"),t.close()}).catch(function(e){t.close(),console.log(e)})},handleSelectionChange:function(e){this.result=e},getResult:function(){for(var e=[],t=0,o=this.result.length;t<o;t++)e.push(this.result[t].id);return e},handleCurrentChange:function(e){this.pagination.page=e,this.getList()}}}},tI18:function(e,t,o){},tbSJ:function(e,t,o){"use strict";o.r(t);var n=o("kb3H"),a=o("G0ez");for(var s in a)["default"].indexOf(s)<0&&function(e){o.d(t,e,function(){return a[e]})}(s);o("55Nw");var r=o("gp09"),l=Object(r.a)(a.default,n.render,n.staticRenderFns,!1,null,"2b6e1fe2",null);t.default=l.exports}}]);