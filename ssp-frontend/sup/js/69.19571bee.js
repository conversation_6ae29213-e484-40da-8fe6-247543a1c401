(window.webpackJsonp=window.webpackJsonp||[]).push([[69],{"+w4g":function(t,e,a){"use strict";a.r(e);var o=a("ETQm"),s=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return o[t]})}(r);e.default=s.a},"01/d":function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left flex-row"},[e("span",[t._v("任务名称")]),e("el-input",{attrs:{placeholder:"请输入任务名称"},model:{value:t.params.name,callback:function(e){t.$set(t.params,"name",e)},expression:"params.name"}}),e("span",[t._v("任务分组")]),e("el-input",{attrs:{placeholder:"请输入定时任务分组"},model:{value:t.params.jobGroup,callback:function(e){t.$set(t.params,"jobGroup",e)},expression:"params.jobGroup"}}),e("span",[t._v("任务状态")]),e("el-select",{attrs:{placeholder:"请选择任务状态"},model:{value:t.params.status,callback:function(e){t.$set(t.params,"status",e)},expression:"params.status"}},[e("el-option",{attrs:{label:"运行中",value:"1"}}),e("el-option",{attrs:{label:"未运行",value:"0"}})],1),e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1),e("div",[e("el-button",{attrs:{icon:"el-icon-link",type:"success"},on:{click:function(e){return t.goToCronLink()}}},[t._v("在线 cron 表达式")]),e("el-button",{attrs:{icon:"el-icon-plus",type:"primary"},on:{click:function(e){return t.add("add")}}},[t._v("新增")])],1)]),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.dataList,"highlight-current-row":"",height:t.tableHeight},on:{"current-change":t.handleCurrentChange}},[e("el-table-column",{attrs:{prop:"name",label:"任务名称",width:"400px","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"action",label:"执行类",width:"400px","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"method",label:"执行方法",width:"220px","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"cron",label:"执行时间",width:"150px","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"jobGroup",label:"任务分组","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"status",label:"任务状态","show-tooltip-when-overflow":""},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.status?e("el-tag",{attrs:{type:"success"}},[t._v("运行中")]):e("el-tag",{attrs:{type:"danger"}},[t._v("未运行")])]}}])}),e("el-table-column",{attrs:{label:"操作",align:"right",width:"300"},scopedSlots:t._u([{key:"default",fn:function(a){return["0"==a.row.status?e("el-button",{attrs:{type:"text",size:"small",icon:"el-icon-video-play"},on:{click:function(e){return t.changeStatus(a.row.id)}}},[t._v("运行")]):t._e(),"1"==a.row.status?e("el-button",{attrs:{type:"text",size:"small",icon:"el-icon-video-pause"},on:{click:function(e){return t.changeStatus(a.row.id)}}},[t._v("关闭")]):t._e(),e("el-button",{attrs:{type:"text",size:"small",icon:"el-icon-edit"},on:{click:function(e){return t.add("edit",a.row)}}},[t._v("编辑")]),e("el-button",{attrs:{type:"text",size:"small",icon:"el-icon-delete"},on:{click:function(e){return t.del(a.row)}}},[t._v("删除")]),e("el-button",{attrs:{type:"text",size:"small",icon:"el-icon-copy-document"},on:{click:function(e){return t.add("copy",a.row)}}},[t._v("复制")])]}}])})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)]),e("el-dialog",{attrs:{title:"定时任务",visible:t.dialogVisible,width:"60%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("div",{staticClass:"dialog-content"},[e("el-form",{ref:"form",staticClass:"item-form",attrs:{model:t.formData,"label-width":"90px",rules:t.rules}},[e("div",{staticClass:"fromBox"},[e("el-row",{attrs:{type:"flex"}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"任务名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"请输入定时任务名称"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"任务分组",prop:"jobGroup"}},[e("el-input",{attrs:{placeholder:"请输入定时任务分组"},model:{value:t.formData.jobGroup,callback:function(e){t.$set(t.formData,"jobGroup",e)},expression:"formData.jobGroup"}})],1)],1)],1),e("el-row",{attrs:{type:"flex"}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"执行类",prop:"action"}},[e("el-input",{attrs:{placeholder:"请输入执行类"},model:{value:t.formData.action,callback:function(e){t.$set(t.formData,"action",e)},expression:"formData.action"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"执行时间",prop:"cron"}},[e("el-input",{attrs:{placeholder:"请输入cron表达式"},model:{value:t.formData.cron,callback:function(e){t.$set(t.formData,"cron",e)},expression:"formData.cron"}})],1)],1)],1),e("el-row",{attrs:{type:"flex"}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"执行方法",prop:"method"}},[e("el-input",{attrs:{placeholder:"请输入执行方法"},model:{value:t.formData.method,callback:function(e){t.$set(t.formData,"method",e)},expression:"formData.method"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"任务状态",prop:"status"}},[e("el-switch",{attrs:{"active-value":"1","inactive-value":"0","active-color":"#13ce66","inactive-color":"#AAAAAA"},model:{value:t.formData.status,callback:function(e){t.$set(t.formData,"status",e)},expression:"formData.status"}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{staticClass:"col-left",attrs:{label:"执行参数",prop:"param"}},[e("el-input",{attrs:{type:"textarea",rows:"3",placeholder:"请输入执行参数"},model:{value:t.formData.param,callback:function(e){t.$set(t.formData,"param",e)},expression:"formData.param"}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{staticClass:"col-left",attrs:{label:"任务描述",prop:"description"}},[e("el-input",{attrs:{type:"textarea",rows:"3",placeholder:"请填写任务描述"},model:{value:t.formData.description,callback:function(e){t.$set(t.formData,"description",e)},expression:"formData.description"}})],1)],1)],1)],1)])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("确 定")])],1)])],1)},e.staticRenderFns=[]},"8o4H":function(t,e,a){"use strict";a("FpZa")},ETQm:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var o=r(a("Q9c5")),s=r(a("DWNM"));function r(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[s.default],data:function(){return{dataList:[],params:{page:1,limit:10,records:0,jobGroup:"",name:"",status:""},formData:{id:"",name:"",jobGroup:"",action:"",method:"",cron:"",status:"",param:"",description:""},dialogVisible:!1,tableHeight:100,rules:{name:[{required:!0,message:"请输入任务名称",trigger:"blur"}],jobGroup:[{required:!0,message:"请输入任务分组",trigger:"blur"}],method:[{required:!0,message:"请输入执行方法",trigger:"blur"}],action:[{required:!0,message:"请输入执行类",trigger:"blur"}],cron:[{required:!0,message:"请输入执行时间cron表达式",trigger:"blur"}]}}},components:{},props:{},computed:{},watch:{},methods:{goToCronLink:function(){window.open("http://www.pppet.net/")},changeStatus:function(t,e){var a=this,s=this.openLoading(),r=o.default.baseContext+"/supervise/supJob/updateStatus/"+t;this.$http_post(r,{}).then(function(t){s.close(),1==t.state?(a.$message.success("操作成功"),a.onQuery()):a.$alert(t.message)})},add:function(t,e){var a=this;if("add"==t&&(this.formData={id:"",name:"",jobGroup:"SUP",action:"",method:"",cron:"",status:"0",param:"",description:""},this.dialogVisible=!0),"edit"==t){var s=this.openLoading();this.$http_post(o.default.baseContext+"/supervise/supJob/info/"+e.id,{}).then(function(t){1==t.state?null!=t.row?(a.$set(a,"formData",t.row),a.dialogVisible=!0):a.$message.error(t.message):null!=t.message?a.$message.error(t.message):a.$message.error("系统异常"),s.close()})}"copy"==t&&(this.formData={id:"",name:e.name,jobGroup:e.jobGroup,action:e.action,method:e.method,cron:e.cron,status:"0",param:e.param,description:e.description},this.dialogVisible=!0)},save:function(){var t=this;this.$refs.form.validate(function(e){if(!e)return!1;var a="";a=null!=t.formData.id&&""!=t.formData.id?o.default.baseContext+"/supervise/supJob/edit":o.default.baseContext+"/supervise/supJob/save";var s=t.openLoading();t.$http_post(a,t.formData).then(function(e){1==e.state?(-1!=a.indexOf("edit")?t.$message.success("修改成功"):t.$message.success("添加成功"),t.onQuery(),t.dialogVisible=!1):t.$alert(e.message),s.close()})})},del:function(t){var e=this;this.$alert("确定删除【"+t.name+"】数据吗","确认信息",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,type:"warning"}).then(function(){var a=e.openLoading();e.$http_post(o.default.baseContext+"/supervise/supJob/deleteById/"+t.id,null).then(function(t){1==t.state?(e.$message.success("删除成功"),e.onQuery(),a.close()):(a.close(),e.$message.error("删除失败，请稍后再试"))})}).catch(function(t){console.log(t)})},handleCurrentChange:function(t){},onSearch:function(t){"reset"==t?(this.params.jobGroup="",this.params.name="",this.params.status="",this.params.page=1,this.onQuery()):(this.params.page=1,this.onQuery())},onPageClick:function(t){this.params.page=t,this.onQuery()},onQuery:function(){var t=this,e=this.params;this.isLink&&(e.isLink="0");var a=this.openLoading(),s=o.default.baseContext+"/supervise/supJob/getJobList";this.$http_post(s,e).then(function(e){if(1==e.state){var o=e.rows;t.dataList=o,t.params.records=e.records,a.close()}else a.close(),t.$alert(e.message)})}},mounted:function(){var t=this;this.onQuery(),this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null}}},FpZa:function(t,e,a){},M5Fo:function(t,e,a){"use strict";var o=a("01/d");a.o(o,"render")&&a.d(e,"render",function(){return o.render}),a.o(o,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return o.staticRenderFns})},NLxE:function(t,e,a){"use strict";a.r(e);var o=a("M5Fo"),s=a("+w4g");for(var r in s)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return s[t]})}(r);a("8o4H");var n=a("gp09"),l=Object(n.a)(s.default,o.render,o.staticRenderFns,!1,null,"714423ee",null);e.default=l.exports}}]);