(window.webpackJsonp=window.webpackJsonp||[]).push([[31],{"/Qod":function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var s=o(a("Q9c5")),r=o(a("DWNM"));function o(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[r.default],name:"hospitalUser-list",data:function(){return{flag:1,relationList:[],relationParams:{page:1,limit:10,total:0,name:"",flag:1,hospitalId:""},select:[],activeIndex:"1"}},props:{show:{type:Boolean,default:!1},hospitalId:{type:String,require:!1}},watch:{show:function(t){if(t){this.relationParams.hospitalId=this.hospitalId;var e=this.relationParams;this.onQuery(e)}}},methods:{reset:function(){this.relationParams.flag=this.flag,this.relationParams.hospitalId=this.hospitalId,this.relationParams.name="",this.onQuery(this.relationParams)},formatter:function(t,e){var a=new Date(t.creationTime),s=a.getFullYear(),r=a.getMonth()+1,o=a.getDate();return s+"-"+(r<10?"0"+r:r)+"-"+(o<10?"0"+o:o)},onQuery:function(t){var e=this;this.$http_post(s.default.baseContext+"/supervise/supHospitalUser/queryHospitalUser",t).then(function(t){if(e.relationList=[],1!=t.state)return!1;var a=t.rows;e.$set(e,"relationList",a),e.relationParams.total=t.records})},search:function(){this.reload()},reload:function(){this.relationParams.flag=this.flag,this.relationParams.hospitalId=this.hospitalId,this.onQuery(this.relationParams)},handleSelectionChange:function(t){this.select=t},relation:function(){for(var t=this,e="",a=0;a<this.select.length;a++)a==this.select.length-1?e+=this.select[a].id:e+=this.select[a].id+",";if(""!=e){var r=s.default.baseContext+"/supervise/supHospitalUser/relateItem";this.$http_post(r,{hospitalId:this.hospitalId,ids:e}).then(function(e){1==e.state?(t.$message.success(e.message),t.reload()):t.$message.error(e.message)})}else this.$message.error("请选择一个要关联的用户！")},dissociated:function(){for(var t=this,e="",a=0;a<this.select.length;a++)a==this.select.length-1?e+=this.select[a].id:e+=this.select[a].id+",";if(""!=e){var r=s.default.baseContext+"/supervise/supHospitalUser/dissociatedItem";this.$http_post(r,{hospitalId:this.hospitalId,ids:e}).then(function(e){1==e.state?(t.$message.success(e.message),t.reload()):t.$message.error(e.message)})}else this.$message.error("请至少选择一个要解除关联的用户！")},handleCurrentChange:function(t){this.relationParams.page=t,this.relationParams.hospitalId=this.hospitalId,this.onQuery(this.relationParams)},relationItem:function(t){"0"==t&&(this.flag=1),"1"==t&&(this.flag=0),this.reload()},close:function(){this.$emit("close")}}}},"0o3R":function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var s=n(a("Q9c5")),r=n(a("DWNM")),o=n(a("9npM"));function n(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[r.default],data:function(){return{chooseHospital:"",relationDialog:!1,dataList:[],params:{page:1,limit:10,records:0,code:"",name:""},hospitalList:[],formData:{id:"",phone:"",address:"",code:"",name:"",sortOrder:1},dialogVisible:!1,tableHeight:100,rules:{name:[{required:!0,message:"请输入医疗机构",trigger:"blur"}],code:[{required:!0,message:"请输入医院编码",trigger:"blur"}],address:[{required:!0,message:"请输入医院地址",trigger:"blur"}],phone:[{required:!0,message:"请输入医院电话",trigger:"blur"}]}}},components:{relationItem:o.default},props:{},computed:{},watch:{},methods:{getHospitalList:function(){var t=this,e=s.default.baseContext+"/supervise/supHospital/all";this.$http_post(e,{}).then(function(e){1==e.state?t.hospitalList=e.row:t.$alert(e.message)})},close:function(){this.relationDialog=!1},getUser:function(t){this.chooseHospital=t,this.relationDialog=!0},changeStatus:function(t,e){var a=this,r={id:t,status:e},o=this.openLoading(),n=s.default.baseContext+"/supervise/supHospital/updateStatus";this.$http_post(n,r).then(function(t){1==t.state?(a.$message.success("操作成功"),a.onQuery()):a.$alert(t.message),o.close()})},add:function(t,e){var a=this;if("add"==t&&(this.formData={id:"",phone:"",address:"",code:"",name:"",sortOrder:1},this.dialogVisible=!0),"edit"==t){var r=this.openLoading();this.$http_post(s.default.baseContext+"/supervise/supHospital/info/"+e.id,{}).then(function(t){1==t.state?null!=t.row?(a.formData={id:t.row.id,phone:t.row.phone,address:t.row.address,code:t.row.code,name:t.row.name,sortOrder:t.row.sortOrder},a.dialogVisible=!0):a.$message.error("系统异常"):null!=t.message?a.$message.error(t.message):a.$message.error("系统异常"),r.close()})}},save:function(){var t=this;this.$refs.form.validate(function(e){if(!e)return!1;var a="";a=null!=t.formData.id&&""!=t.formData.id?s.default.baseContext+"/supervise/supHospital/update":s.default.baseContext+"/supervise/supHospital/save";var r=t.openLoading();t.$http_post(a,t.formData).then(function(e){1==e.state?(-1!=a.indexOf("update")?t.$message.success("修改成功"):t.$message.success("添加成功"),t.onQuery(),t.dialogVisible=!1):t.$alert(e.message),r.close()})})},del:function(t){var e=this;this.$alert("确定删除【"+t.name+"】数据吗","确认信息",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,type:"warning"}).then(function(){var a=e.openLoading();e.$http_post(s.default.baseContext+"/supervise/supHospital/delete/"+t.id,null).then(function(t){1==t.state?(e.$message.success("删除成功"),e.onQuery(),a.close()):(a.close(),e.$message.error("删除失败，请稍后再试"))})}).catch(function(t){console.log(t)})},handleCurrentChange:function(t){},onSearch:function(t){"reset"==t?(this.params.code="",this.params.name="",this.onQuery()):""!=this.params.code||""!=this.params.name?(this.params.page=1,this.onQuery()):this.$message.warning("请输入医疗机构或编码查询")},onPageClick:function(t){this.params.page=t,this.onQuery()},onQuery:function(){var t=this,e=this.params;this.isLink&&(e.isLink="0");var a=this.openLoading(),r=s.default.baseContext+"/supervise/supHospital/list";this.$http_post(r,e).then(function(e){if(1==e.state){var s=e.rows;t.dataList=s,t.params.records=e.records,a.close()}else a.close(),t.$alert(e.message)})}},mounted:function(){var t=this;this.onQuery(),this.getHospitalList(),this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null}}},"4ZqS":function(t,e,a){"use strict";var s=a("6g7j");a.o(s,"render")&&a.d(e,"render",function(){return s.render}),a.o(s,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return s.staticRenderFns})},"6g7j":function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("el-dialog",{staticClass:"cms-dialog-padding",attrs:{title:"关联用户",visible:t.show,width:"60%"},on:{close:t.close}},[e("div",{staticClass:"dialog-content"},[e("el-menu",{staticClass:"el-menu-demo",attrs:{"default-active":t.activeIndex,mode:"horizontal"}},[e("el-menu-item",{attrs:{index:"1"},on:{click:function(e){return t.relationItem("0")}}},[t._v("已关联")]),e("el-menu-item",{attrs:{index:"2"},on:{click:function(e){return t.relationItem("1")}}},[t._v("未关联")])],1),e("el-form",{attrs:{model:t.relationParams,size:"small","label-width":"80px"}},[e("el-row",[e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"用户名称"}},[e("el-input",{attrs:{placeholder:"请输入用户名称"},model:{value:t.relationParams.name,callback:function(e){t.$set(t.relationParams,"name",e)},expression:"relationParams.name"}})],1)],1),e("el-col",{attrs:{span:8}},[e("el-form-item",{staticClass:"programme-btn"},[e("el-button",{attrs:{icon:"el-icon-delete"},on:{click:t.reset}},[t._v("重置")]),e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.search}},[t._v("查询")])],1)],1)],1)],1),e("el-table",{staticClass:"cms-table-hide",staticStyle:{width:"100%"},attrs:{data:t.relationList,"header-cell-style":{background:"#f5f7fa"},"max-height":"400"},on:{"selection-change":t.handleSelectionChange}},[e("el-table-column",{attrs:{type:"selection",width:"45"}}),e("el-table-column",{attrs:{label:"用户名称",prop:"name","show-overflow-tooltip":""}}),e("el-table-column",{attrs:{prop:"creationTime",label:"创建时间",formatter:t.formatter,"show-overflow-tooltip":"",width:"150"}})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{"current-page":t.relationParams.page,"page-size":t.relationParams.limit,layout:"total,prev, pager, next, jumper",total:t.relationParams.total},on:{"current-change":t.handleCurrentChange,"update:currentPage":function(e){return t.$set(t.relationParams,"page",e)},"update:current-page":function(e){return t.$set(t.relationParams,"page",e)}}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[1==t.flag?[e("el-button",{attrs:{type:"primary",plain:""},on:{click:t.dissociated}},[t._v("取消关联")]),e("el-button",{on:{click:t.close}},[t._v("取消")])]:t._e(),0==t.flag?[e("el-button",{attrs:{type:"primary"},on:{click:t.relation}},[t._v("关联")]),e("el-button",{on:{click:t.close}},[t._v("取消")])]:t._e()],2)])},e.staticRenderFns=[]},"9npM":function(t,e,a){"use strict";a.r(e);var s=a("4ZqS"),r=a("LJGd");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,function(){return r[t]})}(o);a("xIc6");var n=a("gp09"),i=Object(n.a)(r.default,s.render,s.staticRenderFns,!1,null,"ecbd1c30",null);e.default=i.exports},BuIa:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left flex-row"},[e("span",[t._v("医疗机构")]),e("el-select",{staticStyle:{width:"30%","margin-right":"10px"},attrs:{clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:t.params.name,callback:function(e){t.$set(t.params,"name",e)},expression:"params.name"}},t._l(t.hospitalList,function(t){return e("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})}),1),e("span",[t._v("医院编码")]),e("el-input",{attrs:{placeholder:"请输入医院编码"},model:{value:t.params.code,callback:function(e){t.$set(t.params,"code",e)},expression:"params.code"}}),e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1),e("el-button",{attrs:{icon:"el-icon-plus",type:"primary"},on:{click:function(e){return t.add("add")}}},[t._v("新增")])],1),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.dataList,"highlight-current-row":"",height:t.tableHeight},on:{"current-change":t.handleCurrentChange}},[e("el-table-column",{attrs:{prop:"name",label:"医疗机构"}}),e("el-table-column",{attrs:{prop:"code",label:"医院编码"}}),e("el-table-column",{attrs:{label:"状态",prop:"status"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-switch",{attrs:{"active-value":"1","inactive-value":"0","active-color":"#13ce66","inactive-color":"#ff4949"},on:{change:function(e){return t.changeStatus(a.row.id,a.row.status)}},model:{value:a.row.status,callback:function(e){t.$set(a.row,"status",e)},expression:"scope.row.status"}})]}}])}),e("el-table-column",{attrs:{label:"操作",align:"right",width:"200"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.getUser(a.row.id)}}},[t._v("关联用户")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.add("edit",a.row)}}},[t._v("编辑")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.del(a.row)}}},[t._v("删除")])]}}])})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)]),e("el-dialog",{attrs:{title:"医院",visible:t.dialogVisible,width:"45%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("div",{staticClass:"dialog-content"},[e("el-form",{ref:"form",staticClass:"item-form",attrs:{model:t.formData,"label-width":"90px",rules:t.rules}},[e("div",{staticClass:"fromBox"},[e("el-row",{attrs:{type:"flex"}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"医疗机构",prop:"name"}},[e("el-input",{attrs:{placeholder:"请输入医疗机构"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"医院编码",prop:"code"}},[e("el-input",{attrs:{placeholder:"请输入医院编码"},model:{value:t.formData.code,callback:function(e){t.$set(t.formData,"code",e)},expression:"formData.code"}})],1)],1)],1),e("el-row",{attrs:{type:"flex"}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[e("el-input",{attrs:{placeholder:"请输入联系电话"},model:{value:t.formData.phone,callback:function(e){t.$set(t.formData,"phone",e)},expression:"formData.phone"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"排序"}},[e("el-input-number",{attrs:{min:1,max:100},model:{value:t.formData.sortOrder,callback:function(e){t.$set(t.formData,"sortOrder",e)},expression:"formData.sortOrder"}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{staticClass:"col-left",attrs:{label:"医院地址",prop:"address"}},[e("el-input",{attrs:{type:"textarea",placeholder:"请填写医院地址"},model:{value:t.formData.address,callback:function(e){t.$set(t.formData,"address",e)},expression:"formData.address"}})],1)],1)],1)],1)])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("确 定")])],1)]),e("relation-item",{ref:"relationItem",attrs:{show:t.relationDialog,hospitalId:t.chooseHospital},on:{close:t.close}})],1)},e.staticRenderFns=[]},C2Xz:function(t,e,a){"use strict";var s=a("BuIa");a.o(s,"render")&&a.d(e,"render",function(){return s.render}),a.o(s,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return s.staticRenderFns})},Kraz:function(t,e,a){"use strict";a.r(e);var s=a("C2Xz"),r=a("WQBD");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,function(){return r[t]})}(o);a("UIyT");var n=a("gp09"),i=Object(n.a)(r.default,s.render,s.staticRenderFns,!1,null,"94728638",null);e.default=i.exports},LJGd:function(t,e,a){"use strict";a.r(e);var s=a("/Qod"),r=a.n(s);for(var o in s)["default"].indexOf(o)<0&&function(t){a.d(e,t,function(){return s[t]})}(o);e.default=r.a},UIyT:function(t,e,a){"use strict";a("jdP8")},WQBD:function(t,e,a){"use strict";a.r(e);var s=a("0o3R"),r=a.n(s);for(var o in s)["default"].indexOf(o)<0&&function(t){a.d(e,t,function(){return s[t]})}(o);e.default=r.a},gAIr:function(t,e,a){},jdP8:function(t,e,a){},xIc6:function(t,e,a){"use strict";a("gAIr")}}]);