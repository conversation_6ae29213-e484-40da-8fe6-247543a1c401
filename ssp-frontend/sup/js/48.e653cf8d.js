(window.webpackJsonp=window.webpackJsonp||[]).push([[48],{Cr6W:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=n(a("G/zQ")),l=n(a("Q9c5")),i=n(a("DWNM"));function n(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[i.default],name:"delivery-detail",data:function(){return{activeName:"first",curTab:"0",dialogVisible:!1,deliveryData:{data:{},deliveryItem:[]},listLoading:!1,tableHeight:100}},components:{deliveryItemList:r.default},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ")+(t.getHours()<10?"0"+t.getHours()+":":t.getHours()+":")+(t.getMinutes()<10?"0"+t.getMinutes()+":":t.getMinutes()+":")+(t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds())}},watch:{},mounted:function(){this.$route.query.deliveryCode&&this.getDeliveryDetail()},methods:{goBack:function(){this.$router.go(-1)},handleClick:function(e,t){this.curTab=e.index},getDeliveryDetail:function(){var e=this,t=this.openLoading("查询中"),a=l.default.baseContext+"/supervise/conMaterialDelivery/show/"+this.$route.query.deliveryCode;this.$http_post(a,{}).then(function(a){1==a.state?(e.deliveryData=a.row,t.close()):(t.close(),e.$alert(a.message))})}}}},MGIM:function(e,t,a){},RGeM:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"}),t("el-button",{attrs:{type:"info",icon:"el-icon-arrow-left"},on:{click:e.goBack}},[e._v("返回\n        ")])],1),t("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[t("el-tab-pane",{attrs:{label:"配送单信息",name:"first"}})],1),t("div",{staticClass:"form"},[t("el-form",{ref:"dataForm",staticClass:"item-form",attrs:{id:"deliveryData2",model:e.deliveryData,"label-position":"left","label-width":"110px"}},[t("div",{staticClass:"fromBox"},[t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"配送单号111"}},[t("el-input",{attrs:{value:e.deliveryData.data.code}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"医疗机构"}},[t("el-input",{attrs:{value:e.deliveryData.data.hospitalName}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"医院联系人"}},[t("el-input",{attrs:{value:e.deliveryData.data.hospitalPerson}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"医院联系电话"}},[t("el-input",{attrs:{value:e.deliveryData.data.hospitalPhone}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"配送企业"}},[t("el-input",{attrs:{value:e.deliveryData.data.deliveryName}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"配送联系人"}},[t("el-input",{attrs:{value:e.deliveryData.data.deliveryPerson}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"配送时间"}},[t("el-input",{attrs:{value:e._f("formatTime")(e.deliveryData.data.deliveryTime)}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"配送联系电话"}},[t("el-input",{attrs:{value:e.deliveryData.data.deliveryPhone}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"配送地址"}},[t("el-input",{attrs:{value:e.deliveryData.data.deliveryAddress}})],1)],1)],1)],1)])],1),t("div",{staticClass:"memberTab"},[t("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.curTab,callback:function(t){e.curTab=t},expression:"curTab"}},[t("el-tab-pane",{attrs:{label:"配送明细信息"}},[t("delivery-item-list",{ref:"deliveryItem",attrs:{name:"0",deliveryCode:this.$route.query.deliveryCode}})],1)],1)],1)],1)},t.staticRenderFns=[]},YdaC:function(e,t,a){"use strict";a.r(t);var r=a("Cr6W"),l=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,function(){return r[e]})}(i);t.default=l.a},aaym:function(e,t,a){"use strict";a("MGIM")},"i0/U":function(e,t,a){"use strict";a.r(t);var r=a("yAKi"),l=a("YdaC");for(var i in l)["default"].indexOf(i)<0&&function(e){a.d(t,e,function(){return l[e]})}(i);a("aaym");var n=a("gp09"),s=Object(n.a)(l.default,r.render,r.staticRenderFns,!1,null,"4d9d471b",null);t.default=s.exports},yAKi:function(e,t,a){"use strict";var r=a("RGeM");a.o(r,"render")&&a.d(t,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return r.staticRenderFns})}}]);