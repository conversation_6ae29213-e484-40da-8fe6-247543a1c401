(window.webpackJsonp=window.webpackJsonp||[]).push([[67],{"/6ks":function(e,t,i){},M1iJ:function(e,t,i){"use strict";i.r(t);var a=i("Uvh1"),o=i("lmDE");for(var s in o)["default"].indexOf(s)<0&&function(e){i.d(t,e,function(){return o[e]})}(s);i("hWw3");var n=i("gp09"),r=Object(n.a)(o.default,a.render,a.staticRenderFns,!1,null,"9b51191e",null);t.default=r.exports},Uvh1:function(e,t,i){"use strict";var a=i("k2ht");i.o(a,"render")&&i.d(t,"render",function(){return a.render}),i.o(a,"staticRenderFns")&&i.d(t,"staticRenderFns",function(){return a.staticRenderFns})},Yu5C:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0});var a=r(i("omC7")),o=r(i("XRYr")),s=r(i("Q9c5")),n=r(i("DWNM"));function r(e){return e&&e.__esModule?e:{default:e}}t.default={name:"invoiceItem-list",mixins:[n.default],data:function(){return{adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1,showBox:!1,multipleSelection:[],pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=(new Date).format("yyyy-MM-dd HH:mm:ss"),i=new Date,a=new Date(i.getTime()-6048e5).format("yyyy-MM-dd HH:mm:ss");e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd HH:mm:ss"),i=new Date,a=new Date(i.getTime()-2592e6).format("yyyy-MM-dd HH:mm:ss");e.$emit("pick",[a,t])}},{text:"最近三个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd HH:mm:ss"),i=new Date,a=new Date(i.getTime()-7776e6).format("yyyy-MM-dd HH:mm:ss");e.$emit("pick",[a,t])}}]},hospitalList:[],invoiceItemDataList:[],invoiceParams:{invoiceCode:"",orderCode:"",payStatus:"",stockDay:"",overFlag:"",country:"",deliveryItemId:"",invoiceDate:"",page:1,limit:10,records:0,hospitalName:"",deliveryName:"",payDate:"",autoPay:""},headers:{},fileList:[],show:!1,title:"",invoiceVoucher:{remarkType:"3",invoiceVouchers:[],many:!1,invoiceId:"",deliveryItemId:"",id:"",payPrice:"",payTime:"",remark:"",docInfo:"",docIdList:""},isShow:!0,previewUrl:s.default.baseContext+"/file",downloadUrl:s.default.baseContext+"/file/download",dialogVisible:!1,listLoading:!1,tableHeight:100,fileParams:{fileName:"",invoiceNo:""}}},props:{deliveryItemId:{type:String,default:"#"}},watch:{deliveryItemId:function(e){e&&this.init()},$route:function(){"0"==this.$route.query.payStatus&&(this.invoiceParams.payStatus=this.$route.query.payStatus,this.invoiceParams.overFlag="2"),"1"==this.$route.query.payStatus&&(this.invoiceParams.payStatus=this.$route.query.payStatus,this.invoiceParams.overFlag=""),this.onInvoiceQuery()}},mounted:function(){var e=this,t=o.default.doCloundRequest(s.default.app_key,s.default.app_security,"");this.headers["x-aep-appkey"]=t["x-aep-appkey"],this.headers["x-aep-signature"]=t["x-aep-signature"],this.headers["x-aep-timestamp"]=t["x-aep-timestamp"],this.headers["x-aep-nonce"]=t["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,this.initRole(),this.getHospitalList(),"#"!=this.deliveryItemId&&this.init(),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-150}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-150},this.onInvoiceQuery()},computed:{uploadUrl:function(){return s.default.baseContext+"/file/upload"}},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e),i=t.getFullYear()+"-",a=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",o=t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ";t.getHours(),t.getHours(),t.getMinutes(),t.getMinutes(),t.getSeconds(),t.getSeconds();return i+a+o},rateFormat:function(e){return e=isNaN(e)||""===e?"--":Number(100*e).toFixed(1)+"%"}},methods:{initRole:function(){var e=this.$store.getters.curUser.roleCode;-1!=e.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=e.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=e.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=e.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)},getHospitalList:function(){var e=this,t=s.default.baseContext+"/supervise/supHospital/all";this.$http_post(t,{}).then(function(t){1==t.state?e.hospitalList=t.row:e.$alert(t.message)})},showDetail:function(e){this.$router.push({name:"invoiceDetail",query:{invoiceCode:e.no}})},selectChange:function(e){this.invoiceVoucher.remark="3"==e?"请输入备注":e},isImageOrPdf:function(e){var t=e.lastIndexOf("."),i=e.substr(t+1);return-1!==["png","jpg","jpeg","bmp","gif","webp","psd","svg","tiff","pdf"].indexOf(i.toLowerCase())},handleSelectionChange:function(e){this.multipleSelection=e},createPayVoucher:function(){var e=this;if(0==this.multipleSelection.length)this.$message({type:"error",message:"所选内容不能为空！"});else{var t="";this.multipleSelection.forEach(function(e,i){t=(t=Number(t)+Number(e.taxesAmount)).toFixed(4),e.payTime=""}),this.invoiceVoucher={many:!0,invoiceVouchers:this.multipleSelection,payTime:"",payPrice:t,remark:"",docInfo:"",docIdList:[]},this.title="上传支付凭证",this.fileList=[],this.$nextTick(function(){e.show=!0})}},init:function(){this.invoiceParams.deliveryItemId=this.deliveryItemId,this.isShow=!1,this.tableHeight=300},close:function(){this.show=!1},onSuccess:function(e,t,i){if(console.log(e),"1"!=e.state)return this.$message.error(e.message),this.$refs.upload.clearFiles(),!1;t.docId=e.row.id;var a={docId:e.row.id,name:e.row.name};this.invoiceVoucher.docIdList.push(a),this.fileParams.fileName=e.row.name},onError:function(e,t,i){this.rLoading.close(),console.log(e),this.$message.error("上传失败,请重试")},handlePreview:function(e){var t=this.downloadUrl+"?docId="+encodeURI(encodeURI(e.docId))+"&fileName="+e.name;console.log(e);var i=document.createElement("a");i.href=t,document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(t)},beforeUpload:function(e){return!(e.size/1024/1024>10)||(this.$message.error("上传文件过大，请分开上传，单个文件最大为10M。"),!1)},beforeRemove:function(e,t){return!this.beforeUpload(e)||this.$confirm("确定移除 "+e.name+"？")},handleRemove:function(e,t){var i=this;this.invoiceVoucher.docIdList.some(function(t,a){if(t.docId==e.docId)return i.invoiceVoucher.docIdList.splice(a,1),!0}),console.log(e.docId,e,t)},saveInvoiceVoucher:function(){var e=this;if(!this.invoiceVoucher.docIdList||0==this.invoiceVoucher.docIdList.length)return this.$message.error("请上传支付凭证。"),!1;this.$refs.invoiceVoucher.validate(function(t){if(t){e.invoiceVoucher.docInfo=(0,a.default)(e.invoiceVoucher.docIdList);var i=e.openLoading("提交中...");e.$http_post(s.default.baseContext+"/supervise/supInvoiceItem/saveInvoicePayVoucher",e.invoiceVoucher,!0).then(function(t){if(1==t.state){e.$message.success("提交成功"),e.onInvoiceQuery(),e.show=!1,i.close();var a=e.fileParams;e.$http_post(s.default.baseContext+"/supervise/Operation/payVoucherLog",a).then(function(e){}).catch(function(e){console.log(e)})}else e.$message.error(t.message),i.close()})}})},uploadVoucher:function(e){var t=this;this.invoiceVoucher={many:!1,invoiceId:e.id,payTime:"",payPrice:e.taxesAmount,remark:"",docInfo:"",docIdList:[]},this.fileParams.invoiceNo=e.no,this.title="上传支付凭证",this.fileList=[],this.$nextTick(function(){t.show=!0})},onOnlineView:function(e,t){window.open(this.previewUrl+"/previewByDocId?docId="+encodeURI(encodeURI(e))+"&fileName="+t)},downloadFile:function(e,t){var i=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,a=document.createElement("a");a.href=i,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(i)},onInvoicePageClick:function(e){this.invoiceParams.page=e,this.onInvoiceQuery()},onInvoiceQuery:function(){var e=this;"0"==this.$route.query.payStatus&&(this.invoiceParams.payStatus=this.$route.query.payStatus,this.invoiceParams.overFlag="2"),"1"==this.$route.query.payStatus&&(this.invoiceParams.payStatus=this.$route.query.payStatus,this.invoiceParams.overFlag="");var t=this.invoiceParams,i=this.openLoading(),a=s.default.baseContext+"/supervise/supInvoice/getInvoiceList";this.$http_post(a,t).then(function(t){1==t.state?(e.invoiceItemDataList=t.rows,e.invoiceParams.records=t.records,i.close()):(i.close(),e.$alert(t.message))})},onInvoiceSearch:function(e){"reset"==e?(this.invoiceParams.deliveryName="",this.invoiceParams.stockInDate="",this.invoiceParams.catalogName="",this.invoiceParams.hospitalName="",this.invoiceParams.invoiceDate="",this.invoiceParams.source="",this.invoiceParams.stockDay="",this.invoiceParams.invoiceCode="",this.invoiceParams.orderCode="",this.invoiceParams.payDate="",this.invoiceParams.autoPay="",this.onInvoiceQuery()):""!=this.invoiceParams.orderCode||""!=this.invoiceParams.invoiceCode||""!=this.invoiceParams.hospitalName||""!=this.invoiceParams.invoiceDate||""!=this.invoiceParams.source||""!=this.invoiceParams.stockDay||""!=this.invoiceParams.stockInDate||""!=this.invoiceParams.catalogName||""!=this.invoiceParams.deliveryName||""!=this.invoiceParams.payDate||""!=this.invoiceParams.autoPay?(this.invoiceParams.page=1,this.onInvoiceQuery()):this.$message.warning("请输入查询条件")}}}},hWw3:function(e,t,i){"use strict";i("/6ks")},k2ht:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left"},[t("el-row",{attrs:{gutter:10}},[t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("发票号")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入发票号"},model:{value:e.invoiceParams.invoiceCode,callback:function(t){e.$set(e.invoiceParams,"invoiceCode",t)},expression:"invoiceParams.invoiceCode"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("医疗机构")]),t("div",{staticClass:"searchInput"},[this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:e.invoiceParams.hospitalName,callback:function(t){e.$set(e.invoiceParams,"hospitalName",t)},expression:"invoiceParams.hospitalName"}},e._l(e.hospitalList,function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}),1):e._e()],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("配送企业")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入配送企业"},model:{value:e.invoiceParams.deliveryName,callback:function(t){e.$set(e.invoiceParams,"deliveryName",t)},expression:"invoiceParams.deliveryName"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("开票时间")]),t("div",{staticClass:"searchInput"},[t("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd HH:mm:ss",type:"daterange","pickinvoiceVoucherser-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:e.invoiceParams.invoiceDate,callback:function(t){e.$set(e.invoiceParams,"invoiceDate",t)},expression:"invoiceParams.invoiceDate"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("是否集采")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",filterable:""},model:{value:e.invoiceParams.country,callback:function(t){e.$set(e.invoiceParams,"country",t)},expression:"invoiceParams.country"}},[t("el-option",{key:"1",attrs:{label:"是",value:"1"}}),t("el-option",{key:"2",attrs:{label:"否",value:"0"}})],1)],1)])])],1)],1),t("div",{staticClass:"right"},[t("div",[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:e.onInvoiceSearch}},[e._v("查询")]),t("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(t){return e.onInvoiceSearch("reset")}}},[e._v("重置")])],1)])]),t("el-row",{class:"con "+("0"!=e.$route.query.payStatus&&"checkboxHide")},[t("div",{staticClass:"con-left"},[t("el-table",{key:Math.random(),staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{"row-key":function(e){return e.id},data:e.invoiceItemDataList,height:e.tableHeight,border:""},on:{"selection-change":e.handleSelectionChange}},["0"==e.$route.query.payStatus?t("el-table-column",{attrs:{"reserve-selection":!0,type:"selection",width:"55"}}):e._e(),t("el-table-column",{attrs:{prop:"code",label:"发票代码",width:"120"}}),t("el-table-column",{attrs:{prop:"no",label:"发票号"}}),e.isShow?t("el-table-column",{attrs:{prop:"deliveryName",label:"配送企业","show-tooltip-when-overflow":""}}):e._e(),e.isShow?t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构","show-tooltip-when-overflow":""}}):e._e(),t("el-table-column",{attrs:{prop:"taxesAmount",label:"金额"}}),t("el-table-column",{attrs:{prop:"invoiceDate",label:"开票时间","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(i){return[t("span",[e._v(e._s(e._f("formatTime")(i.row.invoiceDate)))])]}}])}),t("el-table-column",{attrs:{prop:"status",label:"回款状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return["1"==i.row.status?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已回款")])],1):e._e(),"2"==i.row.status?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分回款")])],1):e._e(),"0"!=i.row.status&&i.row.status?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未回款")])],1)]}}])}),"1"==e.$route.query.payStatus?t("el-table-column",{attrs:{prop:"remark",label:"备注"}}):e._e(),t("el-table-column",{attrs:{label:"操作",align:"right"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showDetail(i.row)}}},[e._v("查看详情")])]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.invoiceParams.records,"page-size":e.invoiceParams.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onInvoicePageClick}})],1)],1)]),t("el-dialog",{attrs:{"append-to-body":"",title:this.title,visible:e.show,width:"60%"},on:{close:e.close}},[t("div",{staticClass:"dialog-content"},[t("el-form",{ref:"invoiceVoucher",staticClass:"item-form",attrs:{id:"invoiceVoucher",model:e.invoiceVoucher,"label-width":"90px"}},[t("div",{staticClass:"fromBox"},[t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"支付总金额",prop:"itemStock"}},[t("el-input",{attrs:{readonly:!0},model:{value:e.invoiceVoucher.payPrice,callback:function(t){e.$set(e.invoiceVoucher,"payPrice",e._n(t))},expression:"invoiceVoucher.payPrice"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"支付时间",prop:"stockTime"}},[t("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd HH:mm:ss",size:"small",placeholder:"请选择支付时间"},model:{value:e.invoiceVoucher.payTime,callback:function(t){e.$set(e.invoiceVoucher,"payTime",t)},expression:"invoiceVoucher.payTime"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"凭证上传"}},[t("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{headers:e.headers,action:e.uploadUrl,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"before-upload":e.beforeUpload,"before-remove":e.beforeRemove,"on-success":e.onSuccess,"on-error":e.onError,multiple:"",accept:".gif,.jpg,.jpeg,.bmp,.png,.doc,.docx,.txt,.xlsx,.xls,.pdf","file-list":e.fileList}},[t("el-button",{attrs:{size:"small"}},[e._v("点击上传")])],1)],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"备注",prop:"remark"}},[t("el-select",{attrs:{placeholder:"请选择备注"},on:{change:e.selectChange},model:{value:e.invoiceVoucher.remarkType,callback:function(t){e.$set(e.invoiceVoucher,"remarkType",t)},expression:"invoiceVoucher.remarkType"}},[t("el-option",{attrs:{label:"多笔配送单一起支付",value:"多笔配送单一起支付"}}),t("el-option",{attrs:{label:"国家集采",value:"国家集采"}}),t("el-option",{attrs:{label:"非国家集采",value:"非国家集采"}}),t("el-option",{attrs:{label:"其他",value:"3"}})],1)],1)],1)],1),"3"==e.invoiceVoucher.remarkType?t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"备注",prop:"remark"}},[t("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.invoiceVoucher.remark,callback:function(t){e.$set(e.invoiceVoucher,"remark",t)},expression:"invoiceVoucher.remark"}})],1)],1)],1):e._e()],1)])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.close}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.saveInvoiceVoucher}},[e._v("确定")])],1)])],1)},t.staticRenderFns=[]},lmDE:function(e,t,i){"use strict";i.r(t);var a=i("Yu5C"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(e){i.d(t,e,function(){return a[e]})}(s);t.default=o.a}}]);