(window.webpackJsonp=window.webpackJsonp||[]).push([[82],{"11/3":function(t,e,a){"use strict";var r=a("GaRa");a.o(r,"render")&&a.d(e,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return r.staticRenderFns})},GaRa:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left flex-row"},[e("span",[t._v("订单号")]),e("el-input",{attrs:{placeholder:"请输入订单号"},model:{value:t.params.orderNum,callback:function(e){t.$set(t.params,"orderNum",e)},expression:"params.orderNum"}}),e("span",[t._v("预警状态")]),e("el-select",{attrs:{placeholder:"请选择状态"},model:{value:t.params.warning,callback:function(e){t.$set(t.params,"warning",e)},expression:"params.warning"}},t._l(t.warningData,function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1),e("span",[t._v("入库状态")]),e("el-select",{attrs:{placeholder:"全部"},model:{value:t.params.stockStatus,callback:function(e){t.$set(t.params,"stockStatus",e)},expression:"params.stockStatus"}},[e("el-option",{key:"",attrs:{label:"全部",value:""}}),e("el-option",{key:"0",attrs:{label:"未入库",value:"0"}}),e("el-option",{key:"1",attrs:{label:"已入库",value:"1"}}),e("el-option",{key:"2",attrs:{label:"部分入库",value:"2"}})],1),e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1)]),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.dataList,"highlight-current-row":"",height:t.tableHeight}},[e("el-table-column",{attrs:{prop:"orderNum",label:"订单号",align:"center",width:"230"}}),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",align:"center"}}),e("el-table-column",{attrs:{prop:"totalPrice",label:"订单总金额(元)",align:"center"}}),e("el-table-column",{attrs:{prop:"status",label:"订单状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return["0"==a.row.status?e("span",[t._v("暂存")]):t._e(),"1"==a.row.status?e("span",[t._v("已提交")]):t._e(),"2"==a.row.status?e("span",[t._v("已完成")]):t._e()]}}])}),e("el-table-column",{attrs:{prop:"payStatus",label:"入库状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return["0"==a.row.stockStatus?e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("未入库")])],1):t._e(),"1"==a.row.stockStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已入库")])],1):t._e(),"2"==a.row.stockStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分入库")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"warning",label:"预警状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.warning?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("正常")])],1):t._e(),"1"!=a.row.warning?e("div",[e("el-popover",{attrs:{placement:"top-start",i:"",width:"250",trigger:"hover"}},[t._l(t.getWaring(a.row.warning),function(a,r){return e("div",{staticClass:"text item"},[t._v("\n                                    "+t._s(r+1+"、"+a.name)+"\n                                ")])}),e("el-tag",{attrs:{slot:"reference",type:"danger"},slot:"reference"},[t._v("查看预警")])],2)],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"submitTime",formatter:t.time,label:"采购时间",align:"center"}}),e("el-table-column",{attrs:{label:"操作",align:"right",width:"200"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.editOrderStock(a.row.id)}}},[t._v("入库凭证")]),"1"!=a.row.stockStatus?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.updateStockStatus(a.row)}}},[t._v("入库完成")]):t._e()]}}])})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)])],1)},e.staticRenderFns=[]},POZs:function(t,e,a){"use strict";a.r(e);var r=a("11/3"),n=a("hrCh");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,function(){return n[t]})}(s);a("wkwd");var o=a("gp09"),i=Object(o.a)(n.default,r.render,r.staticRenderFns,!1,null,"c7beb820",null);e.default=i.exports},gTLz:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var r=s(a("Q9c5")),n=s(a("DWNM"));function s(t){return t&&t.__esModule?t:{default:t}}e.default={name:"orderStockVoucher",mixins:[n.default],data:function(){return{warningData:[],tableHeight:100,dataList:[],params:{stockStatus:"",page:1,limit:10,records:0,orderNum:"",warning:"",status:""},orderData:{data:{},orderItem:[],hospital:{}}}},props:{},computed:{},watch:{},methods:{getWaringType:function(t){for(var e=0;e<this.warningData.length;e++)if(this.warningData[e].value==t)return this.warningData[e].label},getWaring:function(t){for(var e=[],a=t.split(","),r=0;r<a.length;r++){var n=this.getWaringType(a[r]);n&&e.push({name:n})}return e},getDictItem:function(t){var e=this,a=this.openLoading();this.$http_post(r.default.baseContext+"/supervise/supDictItem/getItemList",{code:t}).then(function(r){var n=r.rows;1==r.state?("WARNING"==t&&(e.warningData=n),a.close()):(a.close(),e.$message.error(r.message))})},updateStockStatus:function(t){var e=this,a=this.openLoading("");this.$http_post(r.default.baseContext+"/supervise/supOrder/checkStockStatus/"+t.id,{}).then(function(t){if(a.close(),1==t.state)return 0==t.row.status?e.$confirm("此订单内存在"+t.row.msg+"预警,确定要结束凭证上传吗？(确定后不可再更改!)"):e.$confirm("确定要结束凭证上传吗，(确定后不可再更改!)");e.$alert(t.message)}).then(function(n){try{if(n&&"confirm"==n)return e.$http_post(r.default.baseContext+"/supervise/supOrder/updateStockStatus/"+t.id,{})}catch(t){a.close()}}).then(function(t){t&&1==t.state?(e.$message.success("操作成功"),e.onQuery()):t&&0==t.state&&e.$message.error(t.message),a.close()})},editOrderStock:function(t){var e=this.$route.query.country;1==e?this.$router.push({name:"itemStockVoucher",query:{orderId:t,country:e}}):this.$router.push({name:"itemStockVoucher",query:{orderId:t}})},onSearch:function(t){"reset"==t?(this.params.stockStatus="",this.params.orderNum="",this.params.warning="",this.params.status="",this.onQuery()):""!=this.params.orderNum||""!=this.params.warning||""!=this.params.stockStatus?(this.params.page=1,this.onQuery()):this.$message.warning("请输入查询条件")},onPageClick:function(t){this.params.page=t,this.onQuery()},onQuery:function(){var t=this,e=this.params,a=this.openLoading(),n=r.default.baseContext+"/supervise/supOrder/list";this.$http_post(n,e).then(function(e){1==e.state?(t.dataList=e.rows,t.params.records=e.records,a.close()):(a.close(),t.$alert(e.message))})},time:function(t,e){if(void 0==t.submitTime||""==t.submitTime)return"";var a=new Date(t.submitTime);return a.getFullYear()+"-"+((a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-")+(a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ")+(a.getHours()<10?"0"+a.getHours()+":":a.getHours()+":")+(a.getMinutes()<10?"0"+a.getMinutes()+":":a.getMinutes()+":")+(a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds())}},mounted:function(){var t=this;this.onQuery(),this.getDictItem("WARNING"),this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null},filters:{formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}}}},hrCh:function(t,e,a){"use strict";a.r(e);var r=a("gTLz"),n=a.n(r);for(var s in r)["default"].indexOf(s)<0&&function(t){a.d(e,t,function(){return r[t]})}(s);e.default=n.a},jfO9:function(t,e,a){},wkwd:function(t,e,a){"use strict";a("jfO9")}}]);