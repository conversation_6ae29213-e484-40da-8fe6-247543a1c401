(window.webpackJsonp=window.webpackJsonp||[]).push([[34],{BJT2:function(e,t,a){"use strict";a.r(t);var i=a("PWmf"),s=a("kL5R");for(var l in s)["default"].indexOf(l)<0&&function(e){a.d(t,e,function(){return s[e]})}(l);a("RcP6"),a("zMj9");var o=a("gp09"),r=Object(o.a)(s.default,i.render,i.staticRenderFns,!1,null,"f2824c1e",null);t.default=r.exports},COLx:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{staticClass:"import"},[t("div",{staticClass:"trend"},[t("div",{staticClass:"overview"},[t("el-row",[t("el-col",{attrs:{span:24}},[t("div",{staticClass:"change felx flex-sb"},[t("div",{class:"detail1 flex flex-row "+(1===e.tabNow&&"active1"),on:{click:function(t){return e.switchTab(1)}}},[t("div",{staticClass:"icon flex flex-row"},[t("i",{staticClass:"iconfont icon-zongrenkou"})]),t("div",[t("p",[e._v("药品采购情况")])])]),t("div",{class:"detail2 flex flex-row  "+(2===e.tabNow&&"active2"),on:{click:function(t){return e.switchTab(2)}}},[t("div",{staticClass:"icon flex flex-row"},[t("i",{staticClass:"iconfont icon-teshurenkou"})]),t("div",[t("p",[e._v("配送情况")])])]),t("div",{class:"detail3 flex flex-row  "+(3===e.tabNow&&"active3"),on:{click:function(t){return e.switchTab(3)}}},[t("div",{staticClass:"icon flex flex-row"},[t("i",{staticClass:"iconfont icon-canbao"})]),t("div",[t("p",[e._v("回款情况")])])]),t("div",{class:"detail4 flex flex-row  "+(4===e.tabNow&&"active4"),on:{click:function(t){return e.switchTab(4)}}},[t("div",{staticClass:"icon flex flex-row"},[t("i",{staticClass:"iconfont icon-canbaoshuai"})]),t("div",[t("p",[e._v("预警单明细")])])])])])],1)],1),1==e.tabNow?t("div",[t("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[t("el-tab-pane",{attrs:{label:"医院",name:"1"}}),this.adminRole||this.medicalAdmin||this.socialAdmin||this.areaRegionAdmin?t("el-tab-pane",{attrs:{label:e.areaRegionAdmin?"全区":"全市",name:"2"}}):e._e()],1),t("div",{staticClass:"middle"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left"},[t("el-row",{attrs:{gutter:10}},["1"==e.activeName&&(this.adminRole||this.medicalAdmin||this.socialAdmin||this.areaRegionAdmin)?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("医疗机构")]),t("div",{staticClass:"searchInput"},[this.adminRole||this.medicalAdmin||this.socialAdmin||this.areaRegionAdmin?t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:e.itemParams.hospitalName,callback:function(t){e.$set(e.itemParams,"hospitalName",t)},expression:"itemParams.hospitalName"}},e._l(e.hospitalList,function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}),1):e._e()],1)])]):e._e(),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("采购时间")]),t("div",{staticClass:"searchInput"},[t("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:e.itemParams.submitTime,callback:function(t){e.$set(e.itemParams,"submitTime",t)},expression:"itemParams.submitTime"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("采购平台")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",placeholder:"请选择平台"},model:{value:e.itemParams.source,callback:function(t){e.$set(e.itemParams,"source",t)},expression:"itemParams.source"}},[t("el-option",{key:"",attrs:{label:"全部",value:""}}),t("el-option",{key:"1",attrs:{label:"深圳市药品交易平台",value:"1"}}),t("el-option",{key:"2",attrs:{label:"广东省药品交易平台",value:"2"}}),t("el-option",{key:"3",attrs:{label:"广州市药品交易平台",value:"3"}})],1)],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("基药属性")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择基药属性"},model:{value:e.itemParams.attribute,callback:function(t){e.$set(e.itemParams,"attribute",t)},expression:"itemParams.attribute"}},[t("el-option",{attrs:{label:"空",value:"0"}}),t("el-option",{attrs:{label:"国基",value:"1"}}),t("el-option",{attrs:{label:"省基",value:"2"}})],1)],1)])])],1),t("el-row",[this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("区划")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择区划"},on:{change:e.changeRegionName},model:{value:e.itemParams.regionId,callback:function(t){e.$set(e.itemParams,"regionId",t)},expression:"itemParams.regionId"}},e._l(e.regionList,function(e){return t("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})}),1)],1)])]):e._e()],1)],1),t("div",{staticClass:"right"},[t("div",[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.onQuery()}}},[e._v("查询")]),t("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:e.reset}},[e._v("重置")])],1)])])]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{key:Math.random(),staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.amountBarData,"highlight-current-row":""}},[t("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[t("table",{staticClass:"gridtable"},[t("tr",[t("td",[e._v("\n                        国家集采\n                     ")]),t("td",[e._v("\n                       "+e._s(a.row.countryCount)+"\n                     ")]),t("td",[e._v("\n                       "+e._s(a.row.countryAmount)+"\n                     ")]),t("td",[e._v("\n                       "+e._s(a.row.countryPackingAmount)+"\n                     ")]),t("td",[e._v("\n                       "+e._s(a.row.countryPrice)+"\n                     ")]),t("td",[e._v("\n                       "+e._s(e._f("rateFormat")(a.row.countryPrice/a.row.totalPrice))+"\n                     ")])]),t("tr",[t("td",[e._v("\n                          非国家集采\n                        ")]),t("td",[e._v("\n                          "+e._s(a.row.noCountryCount)+"\n                        ")]),t("td",[e._v("\n                          "+e._s(a.row.noCountryAmount)+"\n                        ")]),t("td",[e._v("\n                          "+e._s(a.row.noCountryPackingAmount)+"\n                        ")]),t("td",[e._v("\n                          "+e._s(a.row.noCountryPrice)+"\n                        ")]),t("td",[e._v("\n                          "+e._s(e._f("rateFormat")(a.row.noCountryPrice/a.row.totalPrice))+"\n                        ")])]),t("tr",[t("td",[e._v("\n                          线下采购\n                        ")]),t("td",[e._v("\n                          "+e._s(a.row.offlineCount)+"\n                        ")]),t("td",[e._v("\n                          "+e._s(a.row.offlineAmount)+"\n                        ")]),t("td",[e._v("\n                          "+e._s(a.row.offlinePackingAmount)+"\n                        ")]),t("td",[e._v("\n                          "+e._s(a.row.offlinePrice)+"\n                        ")]),t("td",[e._v("\n                          "+e._s(e._f("rateFormat")(a.row.offlinePrice/a.row.totalPrice))+"\n                        ")])])])])]}}],null,!1,1007814275)}),"1"==e.activeName?t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",align:"left",width:"300px"}}):e._e(),"2"==e.activeName?t("el-table-column",{attrs:{prop:"",label:e.areaRegionAdmin?"全区合计":"全市合计",align:"left"}},[e._v("\n                "+e._s(e.areaRegoniName?e.areaRegoniName:e.areaRegionAdmin?"全区合计":"全市合计"))]):e._e(),t("el-table-column",{attrs:{prop:"totalCount",label:"采购品种总数",align:"center"}}),t("el-table-column",{attrs:{prop:"totalAmount",label:"采购总数量",align:"center"}}),t("el-table-column",{attrs:{prop:"totalPackingAmount",label:"采购最小单位总数量",align:"center"}}),t("el-table-column",{attrs:{prop:"totalPrice",label:"采购总金额(元)",align:"center"}}),t("el-table-column",{attrs:{prop:"",label:"采购金额占比",align:"center",width:"385px"}},[e._v("100%")])],1)],1)])],1):e._e(),2==e.tabNow?t("div",[t("div",{staticClass:"middle"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left"},[t("el-row",{attrs:{gutter:10}},[this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("医疗机构")]),t("div",{staticClass:"searchInput"},[this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:e.deliveryParams.hospitalName,callback:function(t){e.$set(e.deliveryParams,"hospitalName",t)},expression:"deliveryParams.hospitalName"}},e._l(e.hospitalList,function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}),1):e._e()],1)])]):e._e(),e.adminRole?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("区划")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",placeholder:"全部"},model:{value:e.deliveryParams.regionId,callback:function(t){e.$set(e.deliveryParams,"regionId",t)},expression:"deliveryParams.regionId"}},e._l(e.regionList,function(e){return t("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})}),1)],1)])]):e._e(),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("入库状态")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",placeholder:"全部"},model:{value:e.deliveryParams.stockStatus,callback:function(t){e.$set(e.deliveryParams,"stockStatus",t)},expression:"deliveryParams.stockStatus"}},[t("el-option",{key:"",attrs:{label:"全部",value:""}}),t("el-option",{key:"0",attrs:{label:"未入库",value:"0"}}),t("el-option",{key:"1",attrs:{label:"已入库",value:"1"}}),t("el-option",{key:"2",attrs:{label:"部分入库",value:"2"}})],1)],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("配送时间")]),t("div",{staticClass:"searchInput"},[t("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:e.deliveryParams.deliveryTime,callback:function(t){e.$set(e.deliveryParams,"deliveryTime",t)},expression:"deliveryParams.deliveryTime"}})],1)])])],1)],1),t("div",{staticClass:"right"},[t("div",[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:e.onDeliverySearch}},[e._v("查询")]),t("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(t){return e.onDeliverySearch("reset")}}},[e._v("重置")])],1)])])]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{key:Math.random(),ref:"deliveryTable",staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{border:"",data:e.deliveryItemDataList,"highlight-current-row":"",height:e.tableHeight,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-key":"id","expand-row-keys":e.expandRows}},[t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构"}}),t("el-table-column",{attrs:{prop:"source",label:"采购平台",align:"left"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.source?t("div",[e._v("\n                    "+e._s(e.getDictItemName(a.row.source))+"\n                  ")]):e._e(),a.row.source?e._e():t("div",[e._v("\n                    "+e._s(e.getDictItemName(e.deliveryParams.source))+"\n                  ")])]}}],null,!1,**********)}),t("el-table-column",{attrs:{prop:"deliveryCount",label:"配送单数",align:"center"}}),t("el-table-column",{attrs:{prop:"amount",label:"配送金额(元)",align:"center"}})],1)],1)])],1):e._e(),3==e.tabNow?t("div",[t("div",{staticClass:"middle"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left"},[t("el-row",{attrs:{gutter:10}},[this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("医疗机构")]),t("div",{staticClass:"searchInput"},[this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:e.invoiceParams.hospitalName,callback:function(t){e.$set(e.invoiceParams,"hospitalName",t)},expression:"invoiceParams.hospitalName"}},e._l(e.hospitalList,function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}),1):e._e()],1)])]):e._e(),e.adminRole?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("区划")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择区划"},model:{value:e.invoiceParams.regionId,callback:function(t){e.$set(e.invoiceParams,"regionId",t)},expression:"invoiceParams.regionId"}},e._l(e.regionList,function(e){return t("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})}),1)],1)])]):e._e(),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("开票时间")]),t("div",{staticClass:"searchInput"},[t("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:e.invoiceParams.invoiceDate,callback:function(t){e.$set(e.invoiceParams,"invoiceDate",t)},expression:"invoiceParams.invoiceDate"}})],1)])])],1)],1),t("div",{staticClass:"right"},[t("div",[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:e.onInvoiceSearch}},[e._v("查询")]),t("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(t){return e.onInvoiceSearch("reset")}}},[e._v("重置")])],1)])])]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{key:Math.random(),staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.invoiceItemDataList,"highlight-current-row":"",height:e.tableHeight,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-key":"id","expand-row-keys":e.expandRows}},[t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构"}}),t("el-table-column",{attrs:{prop:"source",label:"采购平台"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.source?t("div",[e._v("\n                    "+e._s(e.getDictItemName(a.row.source))+"\n                  ")]):e._e(),a.row.source?e._e():t("div",[e._v("\n                    全部\n                  ")])]}}],null,!1,2961259546)}),t("el-table-column",{attrs:{prop:"orderCount",label:"订单数量",align:"center"}}),t("el-table-column",{attrs:{prop:"totalPrice",label:"采购金额(元)",aligh:"center"}}),t("el-table-column",{attrs:{prop:"havePay",label:"回款金额(元)",aligh:"center"}}),t("el-table-column",{attrs:{prop:"havePay",label:"回款率",formatter:e.ratePayFormat,aligh:"center"}})],1)],1)])],1):e._e(),4==e.tabNow?t("div",[t("el-tabs",{on:{"tab-click":e.overHandleClick},model:{value:e.overTimeActive,callback:function(t){e.overTimeActive=t},expression:"overTimeActive"}},[t("el-tab-pane",{attrs:{label:"超时未配送",name:"2"}}),t("el-tab-pane",{attrs:{label:"超时未入库",name:"3"}}),t("el-tab-pane",{attrs:{label:"超时未支付",name:"1"}}),t("el-tab-pane",{attrs:{label:"非系统推荐",name:"4"}})],1),t("div",{staticClass:"middle"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left"},[t("el-row",{staticStyle:{display:"flex","flex-wrap":"wrap"},attrs:{gutter:10}},[this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("医疗机构")]),t("div",{staticClass:"searchInput"},[this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:e.orderParams.hospitalName,callback:function(t){e.$set(e.orderParams,"hospitalName",t)},expression:"orderParams.hospitalName"}},e._l(e.hospitalList,function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}),1):e._e()],1)])]):e._e(),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("订单号")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入订单号"},model:{value:e.orderParams.orderCode,callback:function(t){e.$set(e.orderParams,"orderCode",t)},expression:"orderParams.orderCode"}})],1)])]),"3"==e.overTimeActive?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("配送单号")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入配送单号"},model:{value:e.orderParams.deliveryCode,callback:function(t){e.$set(e.orderParams,"deliveryCode",t)},expression:"orderParams.deliveryCode"}})],1)])]):e._e(),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},["3"==e.overTimeActive?t("div",{staticClass:"searchLabel"},[e._v("配送时间")]):e._e(),"3"!=e.overTimeActive?t("div",{staticClass:"searchLabel"},[e._v("采购时间")]):e._e(),t("div",{staticClass:"searchInput"},[t("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:e.orderParams.submitTime,callback:function(t){e.$set(e.orderParams,"submitTime",t)},expression:"orderParams.submitTime"}})],1)])]),"4"!=e.overTimeActive?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("是否集采")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",placeholder:"请选择"},model:{value:e.orderParams.country,callback:function(t){e.$set(e.orderParams,"country",t)},expression:"orderParams.country"}},[t("el-option",{key:"",attrs:{label:"全部",value:""}}),t("el-option",{key:"1",attrs:{label:"是",value:"1"}}),t("el-option",{key:"2",attrs:{label:"否",value:"0"}})],1)],1)])]):e._e(),"4"!=e.overTimeActive?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},["1"==e.overTimeActive?t("div",{staticClass:"searchLabel"},[e._v("是否支付")]):"2"==e.overTimeActive?t("div",{staticClass:"searchLabel"},[e._v(" 是否配送")]):t("div",{staticClass:"searchLabel"},[e._v(" 是否入库")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",placeholder:"请选择"},model:{value:e.orderParams.decide,callback:function(t){e.$set(e.orderParams,"decide",t)},expression:"orderParams.decide"}},[t("el-option",{key:"",attrs:{label:"全部",value:""}}),t("el-option",{key:"1",attrs:{label:"是",value:"1"}}),t("el-option",{key:"2",attrs:{label:"否",value:"0"}})],1)],1)])]):e._e(),e.adminRole?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("区划")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择区划"},model:{value:e.orderParams.regionId,callback:function(t){e.$set(e.orderParams,"regionId",t)},expression:"orderParams.regionId"}},e._l(e.regionList,function(e){return t("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})}),1)],1)])]):e._e(),"1"==e.overTimeActive?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("开票时间")]),t("div",{staticClass:"searchInput"},[t("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:e.orderParams.invoiceDate,callback:function(t){e.$set(e.orderParams,"invoiceDate",t)},expression:"orderParams.invoiceDate"}})],1)])]):e._e(),"1"==e.overTimeActive?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("入库时间")]),t("div",{staticClass:"searchInput"},[t("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:e.orderParams.stockInDate,callback:function(t){e.$set(e.orderParams,"stockInDate",t)},expression:"orderParams.stockInDate"}})],1)])]):e._e(),"1"==e.overTimeActive?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("批次")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择批次"},model:{value:e.orderParams.batch,callback:function(t){e.$set(e.orderParams,"batch",t)},expression:"orderParams.batch"}},e._l(e.batchList,function(e){return t("el-option",{key:e.code,attrs:{label:e.batchName,value:e.code}})}),1)],1)])]):e._e()],1)],1),t("div",{staticClass:"right"},[t("div",{staticStyle:{"margin-bottom":"5px"}},[t("el-button",{attrs:{size:"small",type:"success",icon:"el-icon-upload"},on:{click:function(t){return e.onSearch("export")}}},[e._v("导出")])],1),t("div",[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1)])])]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},["3"==e.overTimeActive?t("el-table",{key:Math.random(),staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.orderDataList,border:"","highlight-current-row":"",height:e.tableHeightNew}},["3"==e.overTimeActive?t("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单号",width:"150","show-tooltip-when-overflow":""}}):e._e(),t("el-table-column",{attrs:{prop:"source",width:"180",label:"采购平台"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",[e._v("\n                    "+e._s(e.getDictItemName(a.row.source))+"\n                  ")])]}}],null,!1,**********)}),t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"150","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"deliveryName",label:"配送企业",width:"150","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"单价（元）",width:"100"}}),t("el-table-column",{attrs:{prop:"num",label:"发货数量"}}),t("el-table-column",{attrs:{prop:"amount",label:"金额(元)"}}),t("el-table-column",{attrs:{prop:"orderCode",label:"订单编号",width:"150"}}),t("el-table-column",{attrs:{prop:"deliveryTime",label:"配送时间",width:"150",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e._f("formatTime")(a.row.deliveryTime)))])]}}],null,!1,2404825824)}),t("el-table-column",{attrs:{prop:"stockInTime",label:"入库时间",width:"150",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e._f("formatTime")(a.row.stockInTime)))])]}}],null,!1,*********)}),t("el-table-column",{attrs:{label:"超时天数",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                  "+e._s(null==t.row.stockSurplus?30:-t.row.stockSurplus)+"\n                ")]}}],null,!1,765577933)}),this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-table-column",{attrs:{label:"操作",align:"right",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.officialNotice(a.row)}}},[e._v("公文通知")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.phoneNotice(a.row)}}},[e._v("短信提醒")])]}}],null,!1,**********)}):e._e()],1):e._e(),"2"==e.overTimeActive?t("el-table",{key:Math.random(),staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.orderDataList,border:"","highlight-current-row":"",height:e.tableHeightNew}},[t("el-table-column",{attrs:{prop:"orderNum",label:"订单号",align:"left"}}),t("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"200"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",[e._v("\n                    "+e._s(e.getDictItemName(a.row.source))+"\n                  ")])]}}],null,!1,**********)}),t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"150","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"deliveryCompany",label:"配送企业",width:"150","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"catalogName",label:"药品通用名",align:"left",width:"150","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"单价（元）",width:"100"}}),t("el-table-column",{attrs:{prop:"amount",label:"数量"}}),t("el-table-column",{attrs:{prop:"itemPrice",label:"金额(元)"}}),t("el-table-column",{attrs:{prop:"submitTime",label:"采购时间",width:"150",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e._f("formatTime")(a.row.submitTime)))])]}}],null,!1,1149375848)}),t("el-table-column",{attrs:{prop:"deliveryTime",label:"配送时间",width:"150",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                  "+e._s(e._f("formatTime")(t.row.deliveryTime))+"\n                ")]}}],null,!1,*********)}),t("el-table-column",{attrs:{label:"超时天数",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                  "+e._s(null==t.row.deliverySurplus?30:-t.row.deliverySurplus)+"\n                ")]}}],null,!1,**********)}),this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-table-column",{attrs:{label:"操作",align:"right",width:"100",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.officialNotice(a.row)}}},[e._v("公文通知")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.companyRemind(a.row)}}},[e._v("短信提醒")])]}}],null,!1,*********)}):e._e()],1):e._e(),t("el-dialog",{staticStyle:{"font-size":"16px"},attrs:{width:"35%",title:e.title,visible:e.dialogFormCompany},on:{"update:visible":function(t){e.dialogFormCompany=t}}},[t("el-form",{ref:"forms",attrs:{model:e.SelCompanys,"label-position":"right","label-width":"100px"}},[t("el-form-item",{attrs:{label:"基础信息：",prop:"","label-width":"150px"}},[t("el-input",{staticStyle:{width:"80%"},attrs:{type:"textarea",disabled:!0,resize:"none",autosize:"true"},model:{value:e.basicStr,callback:function(t){e.basicStr=t},expression:"basicStr"}})],1),t("el-form-item",{attrs:{label:"选择通知的企业：",prop:"","label-width":"150px"}},[t("el-select",{staticStyle:{width:"80%"},attrs:{filterable:"",placeholder:"输入配送企业搜索手动选择"},on:{change:e.changeSelPhone},model:{value:e.changeSelPhoneStr,callback:function(t){e.changeSelPhoneStr=t},expression:"changeSelPhoneStr"}},e._l(e.companyContactList,function(e){return t("el-option",{key:e.phone,attrs:{label:e.companyName+"，"+e.contacts+"，"+e.phone,value:{value:e.phone,label:e.companyName+"，"+e.contacts+"，"+e.phone}}})}),1)],1),t("el-form-item",{attrs:{label:"手机号码：",prop:"phone","label-width":"150px",rules:[{required:!0,message:"请输入手机号码",trigger:"blur"}]}},[t("el-input",{staticStyle:{width:"80%"},model:{value:e.SelCompanys.phone,callback:function(t){e.$set(e.SelCompanys,"phone",t)},expression:"SelCompanys.phone"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){return e.closeForm()}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.phoneNotice("forms")}}},[e._v("确定")])],1)],1),"1"==e.overTimeActive?t("el-table",{key:Math.random(),staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.orderDataList,border:"","highlight-current-row":"",height:e.tableHeightNew}},[t("el-table-column",{attrs:{prop:"stockInCode",label:"入库号",width:"120"}}),t("el-table-column",{attrs:{prop:"itemNo",label:"入库明细号",width:"120"}}),t("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"150"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",[e._v("\n                    "+e._s(e.getDictItemName(a.row.source))+"\n                  ")])]}}],null,!1,**********)}),t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"150","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"deliveryName",label:"配送企业",width:"150","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"orderCode",label:"订单号",align:"left",width:"150","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"catalogName",label:"药品通用名",align:"left",width:"120","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"taxesAmount",label:"金额","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"no",label:"发票号"}}),t("el-table-column",{attrs:{prop:"invoiceDate",label:"开票时间","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e._f("formatTime")(a.row.invoiceDate)))])]}}],null,!1,3017003436)}),t("el-table-column",{attrs:{prop:"stockInTime",label:"入库时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e._f("formatTime")(a.row.stockInTime)))])]}}],null,!1,*********)}),t("el-table-column",{attrs:{prop:"payTime",label:"支付时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e._f("formatTime")(a.row.payTime)))])]}}],null,!1,*********)}),t("el-table-column",{attrs:{label:"超时天数",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                  "+e._s(null==t.row.overDay?30:-t.row.overDay)+"\n                ")]}}],null,!1,**********)}),this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-table-column",{attrs:{label:"操作",align:"right",width:"100",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.officialNotice(a.row)}}},[e._v("公文通知")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.phoneNotice(a.row)}}},[e._v("短信提醒")])]}}],null,!1,**********)}):e._e()],1):e._e(),"4"==e.overTimeActive?t("el-table",{key:Math.random(),staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.orderDataList,border:"","highlight-current-row":"",height:e.tableHeightNew}},[t("el-table-column",{attrs:{prop:"source",width:"180",label:"采购平台"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",[e._v("\n                    "+e._s(e.getDictItemName(a.row.source))+"\n                  ")])]}}],null,!1,**********)}),t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"150","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"200","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{prop:"specs",label:"规格",width:"150"}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"单价（元）",width:"100"}}),t("el-table-column",{attrs:{prop:"amount",label:"采购数量",width:"100"}}),t("el-table-column",{attrs:{prop:"itemPrice",label:"金额(元)",width:"150"}}),t("el-table-column",{attrs:{prop:"orderNum",label:"订单编号",width:"150"}}),t("el-table-column",{attrs:{prop:"submitTime",label:"采购时间",width:"150",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e._f("formatTime")(a.row.submitTime)))])]}}],null,!1,1149375848)}),t("el-table-column",{attrs:{prop:"reason",label:"非系统推荐原因",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-popover",{attrs:{placement:"top-start",width:"50",trigger:"hover"}},[t("div",{staticClass:"text item"},[e._v("\n                                        "+e._s(a.row.reason)+"\n                                    ")]),t("el-tag",{attrs:{slot:"reference",type:"danger"},slot:"reference"},[e._v("查看预警")])],1)]}}],null,!1,3373486860)})],1):e._e(),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.orderParams.records,"page-size":e.orderParams.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onOrderPageClick}})],1)],1)])],1):e._e()]),t("div",{staticStyle:{position:"absolute",bottom:"0px"}},[t("span",{staticStyle:{color:"red"}},[e._v("注：当前页面统计截至时间为"+e._s(new Date((new Date).getTime()-864e5).format("yyyy-MM-dd")))])])])},t.staticRenderFns=[]},DL6M:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var i=o(a("DWNM")),s=o(a("Q9c5")),l=(o(a("tGa4")),o(a("rGKd")));function o(e){return e&&e.__esModule?e:{default:e}}t.default={name:"index",mixins:[i.default],data:function(){return{batchList:[],hospitalName:"",hospitalId:"",overTimeActive:"2",activeName:"1",itemParams:{regionId:"",attribute:"",source:"",submitTime:[new Date((new Date).getTime()-2592e6).format("yyyy-MM-dd"),(new Date).format("yyyy-MM-dd")],hospitalName:""},drugSourceList:[],expandRows:[],invoiceItemDataList:[],deliveryItemDataList:[],deliveryParams:{deliveryTime:[new Date((new Date).getTime()-2592e6).format("yyyy-MM-dd"),(new Date).format("yyyy-MM-dd")],hospitalName:"",source:"",stockStatus:"",hospitalId:""},invoiceParams:{invoiceDate:[new Date((new Date).getTime()-2592e6).format("yyyy-MM-dd"),(new Date).format("yyyy-MM-dd")],page:1,limit:10,records:0,hospitalName:"",deliveryName:""},orderParams:{orderCode:"",country:"1",overFlag:"1",page:1,limit:10,records:0,type:"2",deliveryCode:"",submitTime:[new Date((new Date).getTime()-7776e6).format("yyyy-MM-dd"),(new Date).format("yyyy-MM-dd")],hospitalName:"",stockInDate:[],invoiceDate:[],decide:"0"},regionList:[{code:"",name:"全部"},{code:"440703",name:"蓬江区"},{code:"440704",name:"江海区"},{code:"440705",name:"新会区"},{code:"440781",name:"台山市"},{code:"440783",name:"开平市"},{code:"440784",name:"鹤山市"},{code:"440785",name:"恩平市"}],hospitalList:[],orderDataList:[],warningData:[],tabNow:1,reFresh:!0,adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,areaRegionAdmin:!1,medicalAdmin:!1,dialogFormCompany:!1,SelCompanys:{phone:""},changeSelPhoneStr:"",CompanysParams:{hospitalName:""},areaRegoniName:"",CompanysData:{busiId:"",hospitalId:"",hospitalName:"",orderCreationTime:"",sourceName:"",orderNum:"",drugCatalogName:"",deliveryCompany:""},companyContactList:[],basicStr:"",tableHeight:550,tableHeightNew:500,amountBarData:[],amountCityData:[],pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),a=new Date,i=new Date(a.getTime()-6048e5).format("yyyy-MM-dd");e.$emit("pick",[i,t])}},{text:"最近一个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),a=new Date,i=new Date(a.getTime()-2592e6).format("yyyy-MM-dd");e.$emit("pick",[i,t])}},{text:"最近三个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),a=new Date,i=new Date(a.getTime()-7776e6).format("yyyy-MM-dd");e.$emit("pick",[i,t])}}]}}},mounted:function(){this.initRole(this.init),l.default.getBatchList(this.setBatchList)},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e),a=t.getFullYear()+"-",i=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",s=t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ";t.getHours(),t.getHours(),t.getMinutes(),t.getMinutes(),t.getSeconds(),t.getSeconds();return a+i+s},rateFormat:function(e){return isNaN(e)||""===e?e="--":(e=Number(100*e).toFixed(2))>100?e="100%":e+="%",e}},methods:{changeRegionName:function(e){for(var t=0;t<this.regionList.length;t++)if(e==this.regionList[t].code){this.areaRegoniName="全部"==this.regionList[t].name?"全市合计":this.regionList[t].name,this.onQuery();break}},setBatchList:function(e){for(var t=e.rows,a=0;a<t.length;a++)this.batchList.push(t[a])},getHospital:function(e){var t=this,a=this.openLoading(),i=this.$store.getters.curUser.id;this.$http_post(s.default.baseContext+"/supervise/supHospitalUser/userHospital/"+i,{}).then(function(i){a.close(),1==i.state?(t.hospitalName=i.row.name,t.hospitalId=i.row.id,e&&e()):t.$alert(i.message)})},initRole:function(e){var t=this.$store.getters.curUser.roleCode;if(-1!=t.indexOf("SUPER_ADMIN"))this.adminRole=!0;else if(-1!=t.indexOf("HOSPITAL_ADMIN"))this.hospitalAdmin=!0;else if(-1!=t.indexOf("DELIVERY_ADMIN"))this.areaRegionAdmin=!0;else if(-1!=t.indexOf("SOCIAL_SECURITY_ADMIN"))this.socialAdmin=!0;else if(-1!=t.indexOf("MEDICAL_INSURANCE_ADMIN"))this.medicalAdmin=!0;else if(-1!=t.indexOf("DELIVERY_ADMIN"))return void this.$router.push({name:"settlementTodo"});this.adminRole||this.medicalAdmin||this.areaRegionAdmin?e&&e():this.getHospital(e)},overHandleClick:function(e,t){this.onOrderQuery()},handleClick:function(e,t){console.log(e,t),this.onQuery()},phoneNotice:function(e){var t=this,a=e.hospitalName,i=void 0,l=void 0;if("1"==this.overTimeActive){var o=this.$options.filters.formatTime(e.orderCreationTime);i={busiId:e.id,hospitalId:e.hospitalId,hospitalName:a,orderCreationTime:o,sourceName:this.getDictItemName(e.source),orderNum:e.orderCode,invoiceItemNo:e.itemNo,drugCatalogName:e.catalogName,noticeNum:null==e.noticeNum?1:e.noticeNum},l=s.default.baseContext+"/supervise/supOfficial/sendSmsTemplate/insurance_sms_notice"}else if("2"==this.overTimeActive){l=s.default.baseContext+"/supervise/supOfficial/sendSmsTemplate/over_delivery_notice";o=this.$options.filters.formatTime(this.CompanysData.orderCreationTime);a=this.CompanysData.deliveryCompany,i={busiId:this.CompanysData.busiId,hospitalId:this.CompanysData.hospitalId,hospitalName:this.CompanysData.hospitalName,orderCreationTime:o,sourceName:this.CompanysData.sourceName,orderNum:this.CompanysData.orderNum,drugCatalogName:this.CompanysData.drugCatalogName,deliveryCompany:this.CompanysData.deliveryCompany,phone:this.SelCompanys.phone}}else{var r=this.$options.filters.formatTime(e.deliveryTime);l=s.default.baseContext+"/supervise/supOfficial/sendSmsTemplate/over_stock_sms_notice",i={busiId:e.id,hospitalId:e.hospitalId,hospitalName:a,deliveryTime:r,sourceName:this.getDictItemName(e.source),deliveryCode:e.deliveryCode,orderNum:e.orderCode,drugCatalogName:e.catalogName}}this.$confirm("是否向"+a+"发出短信提醒？","短信提醒",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var e=t.openLoading();t.$http_post(l,i,!0).then(function(a){t.dialogFormCompany=!1,t.CompanysData.busiId="",t.CompanysData.hospitalId="",t.CompanysData.hospitalName="",t.CompanysData.orderCreationTime="",t.CompanysData.sourceName="",t.CompanysData.orderNum="",t.CompanysData.drugCatalogName="",t.CompanysData.deliveryCompany="",1==a.state?(t.$message({type:"success",message:"发送成功"}),t.onInvoiceQuery(),t.toggleSelection(t.invoiceItemDataList),e.close()):(e.close(),t.$alert(a.message))})})},officialNotice:function(e){var t=this,a=e.hospitalName;if("1"==this.overTimeActive){this.$options.filters.formatTime(e.orderCreationTime);var i={busiId:e.id,hospitalId:e.hospitalId,hospitalName:e.hospitalName,orderCreationTime:e.orderCreationTime,sourceName:this.getDictItemName(e.source),orderNum:e.orderCode,invoiceItemNo:e.code,drugCatalogName:e.catalogName,noticeNum:null==e.noticeNum?1:e.noticeNum},l=s.default.baseContext+"/supervise/supOfficial/sendTemplate/insurance_fund_notice"}else if("2"==this.overTimeActive)this.$options.filters.formatTime(e.orderCreationTime),l=s.default.baseContext+"/supervise/supOfficial/sendTemplate/over_delivery_notice",i={busiId:e.id,hospitalId:e.hospitalId,hospitalName:a,orderCreationTime:submitTime,sourceName:this.getDictItemName(e.source),orderNum:e.orderNum,drugCatalogName:e.catalogName};else{var o=this.$options.filters.formatTime(e.deliveryTime);l=s.default.baseContext+"/supervise/supOfficial/sendTemplate/over_stock_notice",i={busiId:e.id,hospitalId:e.hospitalId,hospitalName:a,deliveryTime:o,sourceName:this.getDictItemName(e.source),deliveryCode:e.deliveryCode,orderNum:e.orderCode,drugCatalogName:e.catalogName}}this.$confirm("是否向"+a+"发出公文提醒？","公文通知",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var e=t.openLoading();t.$http_post(l,i,!0).then(function(a){1==a.state?(t.$message({type:"success",message:"通知成功！"}),t.onOrderQuery(),e.close()):(e.close(),t.$alert(a.message))})})},getDictItemName:function(e){if(!e)return"全部";for(var t=0;t<this.drugSourceList.length;t++)if(this.drugSourceList[t].value==e)return this.drugSourceList[t].label},init:function(){this.getDictItem("WARNING"),this.getDictItem("SOURCE"),this.getReceiveCount(),this.getOrderStatus(),this.switchTab(1),this.getHospitalList()},onOrderPageClick:function(e){this.orderParams.page=e,this.onOrderQuery()},onInvoicePageClick:function(e){this.invoiceParams.page=e,this.onInvoiceQuery()},onInvoiceQuery:function(){var e=this,t=this.invoiceParams,a=this.openLoading(),i=s.default.baseContext+"/supervise/supOrder/getOrderInvoiceList";this.$http_post(i,t).then(function(t){1==t.state?(e.invoiceItemDataList=t.rows,a.close()):(a.close(),e.$alert(t.message))})},onOrderQuery:function(){var e,t=this;"1"==this.overTimeActive?e=s.default.baseContext+"/supervise/supInvoiceItem/getInvoiceItemList":"2"==this.overTimeActive?e=s.default.baseContext+"/supervise/supOrderItem/getOverDeliveryList":"3"==this.overTimeActive&&(e=s.default.baseContext+"/supervise/supDeliveryItem/getOverStockList"),"4"==this.overTimeActive&&(e=s.default.baseContext+"/supervise/supOrderItem/list",this.orderParams.systemContrast="0",this.orderParams.country="0");var a=this.orderParams,i=this.openLoading();this.$http_post(e,a).then(function(e){1==e.state?(t.orderDataList=e.rows,t.orderParams.records=e.records,i.close()):(i.close(),t.$alert(e.message))})},expandDeliveryRow:function(e){var t=this;setTimeout(function(){t.$nextTick(function(){t.$refs.deliveryTable.$el.querySelectorAll(".el-table__row--level-0")[e].querySelector(".el-table__expand-icon").click()})},500)},onDeliveryQuery:function(){var e=this,t=this.deliveryParams,a=this.openLoading(),i=s.default.baseContext+"/supervise/supDeliveryItem/getDeliveryListByHospital";this.$http_post(i,t).then(function(t){1==t.state?(e.deliveryItemDataList=t.rows,a.close()):(a.close(),e.$alert(t.message))})},time:function(e,t){if(void 0==e.submitTime||""==e.submitTime)return"";var a=new Date(e.submitTime);return a.getFullYear()+"-"+((a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-")+(a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ")+(a.getHours()<10?"0"+a.getHours()+":":a.getHours()+":")+(a.getMinutes()<10?"0"+a.getMinutes()+":":a.getMinutes()+":")+(a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds())},showOrder:function(e){this.$router.push({name:"orderDetail",query:{orderId:e.id}})},getWaringType:function(e){for(var t=0;t<this.warningData.length;t++)if(this.warningData[t].value==e)return this.warningData[t].label},getWaring:function(e){for(var t=[],a=e.split(","),i=0;i<a.length;i++){var s=this.getWaringType(a[i]);s&&t.push({name:s})}return t},getDictItem:function(e){var t=this,a=this.openLoading();this.$http_post(s.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(i){var s=i.rows;1==i.state?("SOURCE"==e&&(t.drugSourceList=s),"WARNING"==e&&(t.warningData=s),a.close()):(a.close(),t.$message.error(i.message))})},switchTab:function(e){1==e&&this.onQuery(),2==e&&(this.deliveryParams.deliveryTime=[new Date((new Date).getTime()-2592e6).format("yyyy-MM-dd"),(new Date).format("yyyy-MM-dd")],this.onDeliveryQuery()),3==e&&(this.invoiceParams.invoiceDate=[new Date((new Date).getTime()-2592e6).format("yyyy-MM-dd"),(new Date).format("yyyy-MM-dd")],this.onInvoiceQuery()),4==e&&(this.orderParams.submitTime=[new Date((new Date).getTime()-7776e6).format("yyyy-MM-dd"),(new Date).format("yyyy-MM-dd")],this.orderParams.country="1",this.onOrderQuery()),this.tabNow=e},getHospitalList:function(){var e=this,t=s.default.baseContext+"/supervise/supHospital/all";this.$http_post(t,{}).then(function(t){1==t.state?e.hospitalList=t.row:e.$alert(t.message)})},getOrderStatus:function(){var e=this,t=this.openLoading();this.$http_post(s.default.baseContext+"/supervise/supOrder/getOrderStatus",null).then(function(a){if(1==a.state){var i=a.row;if(i.count>0){var s=e.$createElement,l=s("div",null,[s("div",null,[s("span",null,"您有"+i.count+"笔超时未支付订单，"),s("span",{class:"color",on:{click:e.goToOrderPage}},"点击前往。")])]);e.$notify({title:"通知",type:"info",message:l,position:"bottom-right"})}t.close()}else t.close(),e.$message.error(a.message)})},getReceiveCount:function(){var e=this,t=this.openLoading();this.$http_post(s.default.baseContext+"/supervise/supOfficialReceive/getReceiveCount",null).then(function(a){if(1==a.state){var i=a.row;if(i>0){var s=e.$createElement,l=s("div",null,[s("div",null,[s("span",null,"您有"+i+"条待阅件，"),s("span",{class:"color",on:{click:e.goToReceivePage}},"点击前往。")])]);e.$notify({title:"通知",type:"info",message:l,position:"bottom-right"})}t.close()}else t.close(),e.$message.error(a.message)})},goToOrderPage:function(){this.$router.push({name:"countryOrderImport",query:{warning:"8"}})},goToReceivePage:function(){this.$router.push({name:"receiveList",query:{isSee:"0"}})},load:function(e,t,a){var i=e.hospitalName;this.getTrend(i,a)},deliveryLoad:function(e,t,a){var i=e.id;this.getDeliveryTrend(i,a)},getDeliveryTrend:function(e,t){var a=this;this.deliveryParams.hospitalId=e;var i=this.deliveryParams,l=this.openLoading(),o=s.default.baseContext+"/supervise/supDeliveryItem/getDeliveryListOnHospital";this.$http_post(o,i).then(function(e){if(1==e.state){if(null!=e.rows)if(null==t||void 0==t){var i=e.rows;a.deliveryItemDataList=i}else t(e.rows);l.close()}else l.close(),a.$alert(e.message)})},ratePayFormat:function(e){var t=e.havePay/e.totalPrice;return isNaN(t)||""===t?t=0==e.totalPurchase?"0.00%":"--":(t=Number(100*t).toFixed(2))>100?t="100%":t+="%",t},onDeliverySearch:function(e){"reset"==e?(this.deliveryParams.hospitalId="",this.deliveryParams.deliveryTime="",this.deliveryParams.source="",this.deliveryParams.stockStatus="",this.deliveryParams.page=1,this.onDeliveryQuery()):(this.deliveryParams.page=1,this.onDeliveryQuery())},onInvoiceSearch:function(e){"reset"==e?(this.invoiceParams.deliveryName="",this.invoiceParams.hospitalName="",this.invoiceParams.invoiceDate="",this.invoiceParams.page=1,this.onInvoiceQuery()):(this.invoiceParams.page=1,this.onInvoiceQuery())},exportExcel:function(e,t,a){var i=this;this.$post_blob(e,t).then(function(e){if("application/json"==e.type){var t=new FileReader;return t.addEventListener("loadend",function(e){i.$message.error(JSON.parse(e.target.result).message)}),void t.readAsText(e)}var s=URL.createObjectURL(e),l=document.createElement("a");l.href=s,l.target="_blank",l.download=a+".xls",l.click()}).catch(function(e){console.log(e)})},onSearch:function(e){var t=this;if("reset"==e)this.orderParams.country="1",this.orderParams.hospitalName="",this.orderParams.submitTime="",this.orderParams.orderCode="",this.orderParams.deliveryCode="",this.orderParams.page=1,this.onOrderQuery();else if("export"==e){var a,i;"1"==this.overTimeActive?(a=s.default.baseContext+"/supervise/supInvoiceItem/getInvoiceItemListToExcel",i="超时未支付数据"):"2"==this.overTimeActive?(a=s.default.baseContext+"/supervise/supOrderItem/getOverDeliveryListToExcel",i="超时未配送数据"):"3"==this.overTimeActive?(a=s.default.baseContext+"/supervise/supDeliveryItem/getOverStockListToExcel",i="超时未入库数据"):(a=s.default.baseContext+"/supervise/supOrderItem/exportOrderItem",this.orderParams.systemContrast="0",this.orderParams.country="0",i="非系统推荐数据"),this.$confirm("确定导出"+i+"吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.exportExcel(a,t.orderParams,i)}).catch(function(e){console.log(e)})}else this.orderParams.page=1,this.onOrderQuery()},reset:function(){this.itemParams.hospitalName=this.hospitalName,this.itemParams.submitTime="",this.itemParams.source="",this.attribute="",this.onQuery()},companyRemind:function(e){this.dialogFormCompany=!0,this.title="选择配送商或手动输入号码，点击确定发送短信",this.CompanysParams.hospitalName=e.hospitalName,this.CompanysData.busiId=e.id,this.CompanysData.hospitalId=e.hospitalId,this.CompanysData.hospitalName=e.hospitalName,this.CompanysData.orderCreationTime=e.submitTime,this.CompanysData.sourceName=this.getDictItemName(e.source),this.CompanysData.orderNum=e.orderNum,this.CompanysData.drugCatalogName=e.catalogName,this.CompanysData.deliveryCompany=e.deliveryCompany,this.basicStr="订单号："+e.orderNum+"，医疗机构："+e.hospitalName+"，采购平台："+this.CompanysData.sourceName+"，配送企业："+e.deliveryCompany,this.onCompanyContactQuery()},closeForm:function(){this.dialogFormCompany=!1},onQuery:function(){var e=this;this.hospitalAdmin&&(this.itemParams.hospitalName=this.hospitalName);var t,a=this.openLoading();t="1"==this.activeName?s.default.baseContext+"/supervise/statistics/getSourceAmountByHospital":s.default.baseContext+"/supervise/statistics/getSourceAmountByCity",this.$http_post(t,this.itemParams).then(function(t){1==t.state?null!=t.rows&&e.$set(e,"amountBarData",t.rows):null!=t.message?e.$message.error(t.message):e.$message.error("系统异常"),a.close()}).catch(function(e){a.close(),console.log(e)})},changeSelPhone:function(e){var t=e.value,a=e.label;this.changeSelPhoneStr=a,this.SelCompanys.phone=t},onCompanyContactQuery:function(){var e,t=this;e=s.default.baseContext+"/supervise/supDeliveryCompanyContact/getCompanyContact";var a=this.CompanysParams,i=this.openLoading();this.$http_post(e,a).then(function(e){1==e.state?(t.companyContactList=e.rows,i.close()):(i.close(),t.$alert(e.message))})}}}},PWmf:function(e,t,a){"use strict";var i=a("COLx");a.o(i,"render")&&a.d(t,"render",function(){return i.render}),a.o(i,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return i.staticRenderFns})},QSVU:function(e,t,a){},RcP6:function(e,t,a){"use strict";a("oI71")},kL5R:function(e,t,a){"use strict";a.r(t);var i=a("DL6M"),s=a.n(i);for(var l in i)["default"].indexOf(l)<0&&function(e){a.d(t,e,function(){return i[e]})}(l);t.default=s.a},oI71:function(e,t,a){},rGKd:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var i=l(a("Q9c5")),s=l(a("ERIh"));function l(e){return e&&e.__esModule?e:{default:e}}var o=i.default.baseContext+"/supervise/supDrugBatch/getBatchList";var r={getBatchList:function(e){s.default.$http_api("GET",o,{},{jsonContentType:!1,responseType:null,addBaseUrl:!0}).then(function(t){1==t.state?function(e){return null!=e&&void 0!==e&&"function"==typeof e}(e)&&e(t):console.warn("查询集采批次失败",t.message)}).catch(function(e){console.warn("查询集采批次失败",e.message)})}};t.default=r},zMj9:function(e,t,a){"use strict";a("QSVU")}}]);