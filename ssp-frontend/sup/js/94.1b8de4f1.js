(window.webpackJsonp=window.webpackJsonp||[]).push([[94],{"74Un":function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left flex-row"},[e("el-input",{staticStyle:{width:"150px"},attrs:{size:"small",placeholder:"请输入订单号"},model:{value:t.params.orderNum,callback:function(e){t.$set(t.params,"orderNum",e)},expression:"params.orderNum"}}),e("el-select",{staticStyle:{width:"150px"},attrs:{size:"small",placeholder:"请选择订单状态"},model:{value:t.params.orderStatus,callback:function(e){t.$set(t.params,"orderStatus",e)},expression:"params.orderStatus"}},[e("el-option",{key:"0",attrs:{label:"待确认",value:"0"}}),e("el-option",{key:"1",attrs:{label:"待发货",value:"1"}}),e("el-option",{key:"2",attrs:{label:"部分发货",value:"2"}}),e("el-option",{key:"3",attrs:{label:"已发货",value:"3"}}),e("el-option",{key:"4",attrs:{label:"已完成",value:"4"}}),e("el-option",{key:"5",attrs:{label:"已取消",value:"5"}})],1),e("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:t.params.hospitalName,callback:function(e){t.$set(t.params,"hospitalName",e)},expression:"params.hospitalName"}},t._l(t.hospitalList,function(t){return e("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})}),1),e("el-select",{attrs:{size:"small",placeholder:"请选择状态"},model:{value:t.params.warning,callback:function(e){t.$set(t.params,"warning",e)},expression:"params.warning"}},[e("el-option",{key:"0",attrs:{label:"全部",value:"0"}}),e("el-option",{key:"1",attrs:{label:"正常",value:"1"}}),e("el-option",{key:"-1",attrs:{label:"异常",value:"-1"}})],1),e("span",[t._v("下单时间")]),e("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":t.pickerOptions,clearable:!0,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.pickerChangeFn},model:{value:t.params.submitTime,callback:function(e){t.$set(t.params,"submitTime",e)},expression:"params.submitTime"}})],1),e("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-search"},on:{click:t.onQuery}},[t._v("查询")]),e("el-button",{attrs:{icon:"el-icon-search",size:"small"},on:{click:function(e){return t.onReset("reset")}}},[t._v("重置")]),e("el-button",{attrs:{size:"small",type:"success",icon:"el-icon-upload"},on:{click:function(e){return t.orderItemExportExcel("")}}},[t._v("Excel 导出")]),e("el-button",{attrs:{type:"info",size:"small",icon:"el-icon-back"},on:{click:function(e){return t.$router.push({path:"orderStatistics",query:{isCountry:t.$route.query.isCountry}})}}},[t._v("返回\n        ")])],1),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.dataList,"highlight-current-row":"",height:t.tableHeight}},[e("el-table-column",{attrs:{prop:"orderNum",label:"订单号",width:"180"}}),e("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"180",align:"left"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",[t._v("\n                    "+t._s(t.getDictItemName(a.row.source))+"\n                  ")])]}}])}),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"200"}}),e("el-table-column",{attrs:{prop:"totalPrice",label:"订单总金额(元)",width:"200"}}),e("el-table-column",{attrs:{prop:"orderStatus",label:"订单状态"},scopedSlots:t._u([{key:"default",fn:function(a){return["0"==a.row.orderStatus?e("div",[e("el-tag",{attrs:{type:"info"}},[t._v("待确认")])],1):t._e(),"1"==a.row.orderStatus?e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("待发货")])],1):t._e(),"2"==a.row.orderStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分发货")])],1):t._e(),"3"==a.row.orderStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已发货")])],1):t._e(),"4"==a.row.orderStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已完成")])],1):t._e(),"5"==a.row.orderStatus?e("div",[e("el-tag",{attrs:{type:"info"}},[t._v("已取消")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"payStatus",label:"入库状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.stockStatus&&"0"!=a.row.stockStatus?t._e():e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("未入库")])],1),"1"==a.row.stockStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已入库")])],1):t._e(),"2"==a.row.stockStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分入库")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"payStatus",label:"支付状态"},scopedSlots:t._u([{key:"default",fn:function(a){return["0"!=a.row.payStatus&&a.row.payStatus||a.row.stockStatus&&"0"!=a.row.stockStatus?t._e():e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("未支付")])],1),"0"!=a.row.payStatus&&a.row.payStatus||!a.row.stockStatus||"0"==a.row.stockStatus?t._e():e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("未支付")])],1),"1"==a.row.payStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已支付")])],1):t._e(),"2"==a.row.payStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分支付")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"warning",label:"预警状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.warning&&"1"!=a.row.warning?e("el-popover",{attrs:{placement:"top-start",width:"250",trigger:"hover"}},[t._l(t.getWaring(a.row.warning),function(a,r){return e("div",{key:a.name,staticClass:"text item"},[t._v("\n                      "+t._s(r+1+"、"+a.name)+"\n                    ")])}),e("el-tag",{attrs:{slot:"reference",type:"danger"},slot:"reference"},[t._v("查看预警")])],2):e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("正常")])],1)]}}])}),e("el-table-column",{attrs:{prop:"submitTime",formatter:t.time,label:"采购时间"}}),e("el-table-column",{attrs:{label:"操作",align:"right",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showOrder(a.row)}}},[t._v("查看详情")])]}}])})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)])],1)},e.staticRenderFns=[]},BwDK:function(t,e,a){"use strict";a.r(e);var r=a("Lzef"),s=a("ZTEW");for(var o in s)["default"].indexOf(o)<0&&function(t){a.d(e,t,function(){return s[t]})}(o);a("b6s0");var i=a("gp09"),n=Object(i.a)(s.default,r.render,r.staticRenderFns,!1,null,"34b48e40",null);e.default=n.exports},Ic6T:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var r=o(a("Q9c5")),s=o(a("DWNM"));function o(t){return t&&t.__esModule?t:{default:t}}e.default={name:"orderStatisticsList",mixins:[s.default],data:function(){return{pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,r=new Date(a.getTime()-6048e5).format("yyyy-MM-dd");t.$emit("pick",[r,e])}},{text:"最近一个月",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,r=new Date(a.getTime()-2592e6).format("yyyy-MM-dd");t.$emit("pick",[r,e])}},{text:"最近三个月",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,r=new Date(a.getTime()-7776e6).format("yyyy-MM-dd");t.$emit("pick",[r,e])}}]},drugSourceList:[],warningData:[],tableHeight:100,dataList:[],hospitalList:[],params:{orderNum:"",orderStatus:"",page:1,limit:10,records:0,type:"",submitTime:[],hospitalName:"",source:"",warning:""},orderData:{data:{},orderItem:[],hospital:{}}}},props:{},computed:{},watch:{},methods:{pickerChangeFn:function(t){this.$route.query.submitTime=t},getDictItemName:function(t){for(var e=0;e<this.drugSourceList.length;e++)if(this.drugSourceList[e].value==t)return this.drugSourceList[e].label},getWaringType:function(t){for(var e=0;e<this.warningData.length;e++)if(this.warningData[e].value==t)return this.warningData[e].label},getWaring:function(t){for(var e=[],a=t.split(","),r=0;r<a.length;r++){var s=this.getWaringType(a[r]);s&&e.push({name:s})}return e},getDictItem:function(t){var e=this,a=this.openLoading();this.$http_post(r.default.baseContext+"/supervise/supDictItem/getItemList",{code:t}).then(function(r){var s=r.rows;1==r.state?("WARNING"==t&&(e.warningData=s),"SOURCE"==t&&(e.drugSourceList=s),a.close()):(a.close(),e.$message.error(r.message))})},showOrder:function(t){this.$router.push({name:"orderDetail",query:{orderId:t.id}})},onPageClick:function(t){this.params.page=t,this.onQuery()},onReset:function(){this.params.orderNum="",this.params.orderStatus="",this.params.submitTime="",this.params.hospitalName="",this.params.warning="",this.$route.query.type="",this.$route.query.source="",this.$route.query.hospitalName="",this.$route.query.submitTime="",this.onQuery()},onQuery:function(){var t=this;""!=this.$route.query.type?(this.params.type=this.$route.query.type,this.params.warning=this.$route.query.type,2==this.$route.query.type&&(this.params.warning="-1")):(this.params.type,this.params.warning),""!=this.$route.query.source?this.params.source=this.$route.query.source:this.params.source,""!=this.$route.query.hospitalName?this.params.hospitalName=this.$route.query.hospitalName:this.params.hospitalName,""!=this.$route.query.submitTime?this.params.submitTime=this.$route.query.submitTime:this.params.submitTime;var e=this.params,a=this.openLoading(),s=r.default.baseContext+"/supervise/supOrder/list";this.$http_post(s,e).then(function(e){1==e.state?(t.dataList=e.rows,t.params.records=e.records,t.$route.query.type="",t.$route.query.source="",t.$route.query.hospitalName="",t.$route.query.submitTime="",a.close()):(a.close(),t.$alert(e.message))})},time:function(t,e){if(void 0==t.submitTime||""==t.submitTime)return"";var a=new Date(t.submitTime);return a.getFullYear()+"-"+((a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-")+(a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ")+(a.getHours()<10?"0"+a.getHours()+":":a.getHours()+":")+(a.getMinutes()<10?"0"+a.getMinutes()+":":a.getMinutes()+":")+(a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds())},getHospitalList:function(){var t=this,e=r.default.baseContext+"/supervise/supHospital/all";this.$http_post(e,{}).then(function(e){1==e.state?t.hospitalList=e.row:t.$alert(e.message)})},orderItemExportExcel:function(){var t=this,e=this.$store.getters.curUser.id,a=this.$store.getters.curUser.roleCode;e||this.$message.error("用户未登录"),this.$confirm("确定导出订单信息统计数据吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var s=r.default.baseContext+"/supervise/supOrder/exportOrder?hospitalName="+t.params.hospitalName+"&orderStatus="+t.params.orderStatus+"&orderCode="+t.params.orderCode+"&userId="+e+"&roleValue="+a+"&submitTime="+t.params.submitTime+"&warning="+t.params.warning;t.$post_blob(s).then(function(e){if("application/json"==e.type){var a=new FileReader;return a.addEventListener("loadend",function(e){t.$message.error(JSON.parse(e.target.result).message)}),void a.readAsText(e)}var r=URL.createObjectURL(e),s=document.createElement("a");s.href=r,s.target="_blank",s.download="订单信息统计数据.xls",s.click()}).catch(function(t){console.log(t)})}).catch(function(t){console.log(t)})}},mounted:function(){var t=this;this.onQuery(),this.getDictItem("WARNING"),this.getDictItem("SOURCE"),this.getHospitalList(),this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null},filters:{formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}}}},Lzef:function(t,e,a){"use strict";var r=a("74Un");a.o(r,"render")&&a.d(e,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return r.staticRenderFns})},ZTEW:function(t,e,a){"use strict";a.r(e);var r=a("Ic6T"),s=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,function(){return r[t]})}(o);e.default=s.a},b6s0:function(t,e,a){"use strict";a("rxWw")},rxWw:function(t,e,a){}}]);