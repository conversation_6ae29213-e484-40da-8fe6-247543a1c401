(window.webpackJsonp=window.webpackJsonp||[]).push([[58],{"2l5p":function(e,a){Object.defineProperty(a,"__esModule",{value:!0});a.render=function(){var e=this,a=e._self._c;return a("div",{ref:"tableH",staticClass:"content"},[a("div",{staticClass:"search-header flex-row"},[a("div",{staticClass:"left flex-row"},[a("span",[e._v("药本位码")]),a("el-input",{attrs:{placeholder:"请输入药品本位码"},model:{value:e.params.standardCode,callback:function(a){e.$set(e.params,"standardCode",a)},expression:"params.standardCode"}}),a("span",{ref:"foldBtn"},[a("span",{staticStyle:{"margin-left":"20px"},on:{click:e.changeFoldState}},[e._v(e._s(e.brandFold?"收起":"更多"))]),a("el-button",{staticStyle:{"margin-right":"20px"},attrs:{type:"text"},on:{click:e.changeFoldState}},[a("i",{class:e.brandFold?"el-icon-arrow-up":"el-icon-arrow-down"})])],1),a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),a("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(a){return e.onSearch("reset")}}},[e._v("重置")]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.brandFold,expression:"brandFold"}],ref:"searchCon",staticClass:"searchCon"},[a("el-form",{ref:"searchForm",staticClass:"item-item",attrs:{model:e.params,"label-position":"left","label-width":"150px"}},[a("div",{staticClass:"fromBox"},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"商品名",prop:"goodsName"}},[a("el-input",{attrs:{placeholder:"请输入商品名"},model:{value:e.params.goodsName,callback:function(a){e.$set(e.params,"goodsName",a)},expression:"params.goodsName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"药本位码",prop:"standardCode"}},[a("el-input",{attrs:{placeholder:"请输入药本位码"},model:{value:e.params.standardCode,callback:function(a){e.$set(e.params,"standardCode",a)},expression:"params.standardCode"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"通用名",prop:"catalogId"}},[a("el-select",{attrs:{placeholder:"请选择通用名",filterable:""},model:{value:e.params.catalogId,callback:function(a){e.$set(e.params,"catalogId",a)},expression:"params.catalogId"}},e._l(e.drugCatalogList,function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}},[a("span",{staticStyle:{float:"left"}},[e._v(e._s(t.name))]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.category))])])}),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"商品名",prop:"goodsName"}},[a("el-input",{attrs:{placeholder:"请输入商品名"},model:{value:e.params.goodsName,callback:function(a){e.$set(e.params,"goodsName",a)},expression:"params.goodsName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"剂型",prop:"dosageForm"}},[a("el-input",{attrs:{placeholder:"请输入药品剂型"},model:{value:e.params.dosageForm,callback:function(a){e.$set(e.params,"dosageForm",a)},expression:"params.dosageForm"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"生产企业",prop:"company"}},[a("el-input",{attrs:{placeholder:"请输入药品生产企业"},model:{value:e.params.company,callback:function(a){e.$set(e.params,"company",a)},expression:"params.company"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"规格",prop:"specs"}},[a("el-input",{attrs:{placeholder:"请输入药品规格"},model:{value:e.params.specs,callback:function(a){e.$set(e.params,"specs",a)},expression:"params.specs"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"药品状态",prop:"status"}},[a("el-select",{model:{value:e.params.status,callback:function(a){e.$set(e.params,"status",a)},expression:"params.status"}},[a("el-option",{attrs:{label:"--请选择--",value:""}}),a("el-option",{attrs:{label:"已上架",value:"1"}}),a("el-option",{attrs:{label:"已下架",value:"0"}})],1)],1)],1)],1)],1)]),a("span",{staticClass:"programme-btn",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-refresh-left"},on:{click:function(a){return e.onSearch("reset")}}},[e._v("重置")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")])],1)],1)],1)]),a("el-row",{staticClass:"con"},[a("div",{staticClass:"con-left"},[a("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.drugDataList,"highlight-current-row":"",height:e.tableHeight}},[a("el-table-column",{attrs:{prop:"catalogName",label:"通用名",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-popover",{attrs:{title:t.row.catalogName,placement:"right",width:"350",trigger:"hover"}},[a("el-table",{key:Math.random(),ref:"priceTable",attrs:{data:t.row.priceArray}},[a("el-table-column",{attrs:{width:"200",property:"source",label:"平台"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-link",{attrs:{underline:!1,type:"primary"}},[e._v(e._s(e.getDictItemName(t.row.source)))])]}}],null,!0)}),a("el-table-column",{attrs:{width:"80",property:"price",label:"价格(元)"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-link",{attrs:{underline:!1,type:"primary"}},[e._v(e._s(t.row.price))])]}}],null,!0)})],1),a("div",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.catalogName))])],1)]}}])}),a("el-table-column",{attrs:{prop:"goodsName",label:"商品名"}}),a("el-table-column",{attrs:{prop:"unit",width:"80",label:"单位"}}),a("el-table-column",{attrs:{prop:"dosageForm",width:"80",label:"剂型"}}),a("el-table-column",{attrs:{prop:"specs",width:"80",label:"规格"}}),a("el-table-column",{attrs:{prop:"packingSpecs",width:"80",label:"包装规格"}}),a("el-table-column",{attrs:{prop:"standardCode",width:"180",label:"药品本位码"}}),a("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业"}}),a("el-table-column",{attrs:{prop:"source",label:"推荐平台",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",["1"==t.row.source?a("el-tag",{attrs:{type:"success"}},[e._v("深圳平台")]):e._e(),"2"==t.row.source?a("el-tag",{attrs:{type:"success"}},[e._v("省平台")]):e._e(),"3"==t.row.source?a("el-tag",{attrs:{type:"success"}},[e._v("广州平台")]):e._e(),t.row.source?e._e():a("el-tag",{attrs:{type:"danger"}},[e._v("暂未设置价格")])],1)]}}])}),a("el-table-column",{attrs:{label:"操作",align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.showDrugDetail(t.row)}}},[e._v("查看详情")])]}}])})],1),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)]),a("el-dialog",{attrs:{title:"药品",visible:e.dialogVisible,width:"45%"},on:{"update:visible":function(a){e.dialogVisible=a}}},[a("div",{staticClass:"dialog-content"},[a("el-form",{ref:"form",staticClass:"item-form",attrs:{model:e.formData,"label-width":"110px"}},[a("span",{staticStyle:{color:"red"}},[e._v("提示：当药品本位码与药品规格及包装规格都相同时则视为同一种药！")]),a("div",{staticClass:"fromBox"},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"通用名",prop:"catalogId"}},[a("el-select",{attrs:{filterable:"",placeholder:"请选择通用名",disabled:e.disabled},model:{value:e.formData.catalogId,callback:function(a){e.$set(e.formData,"catalogId",a)},expression:"formData.catalogId"}},e._l(e.drugCatalogList,function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}},[a("span",{staticStyle:{float:"left"}},[e._v(e._s(t.name))]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.category))])])}),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"商品名",prop:"goodsName"}},[a("el-input",{attrs:{placeholder:"请输入商品名",readonly:e.disabled},model:{value:e.formData.goodsName,callback:function(a){e.$set(e.formData,"goodsName",a)},expression:"formData.goodsName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"剂型",prop:"dosageForm"}},[a("el-input",{attrs:{readonly:e.disabled,placeholder:"请输入药品剂型"},model:{value:e.formData.dosageForm,callback:function(a){e.$set(e.formData,"dosageForm",a)},expression:"formData.dosageForm"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"规格",prop:"specs"}},[a("el-input",{attrs:{placeholder:"请输入药品规格,如 100mg",readonly:e.disabled},model:{value:e.formData.specs,callback:function(a){e.$set(e.formData,"specs",a)},expression:"formData.specs"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包装规格",prop:"packingSpecs"}},[a("el-input",{attrs:{readonly:e.disabled,placeholder:"请输入药品包装规格"},model:{value:e.formData.packingSpecs,callback:function(a){e.$set(e.formData,"packingSpecs",a)},expression:"formData.packingSpecs"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"单位",prop:"unit"}},[a("el-select",{attrs:{placeholder:"请选择药品单位",disabled:e.disabled},model:{value:e.formData.unit,callback:function(a){e.$set(e.formData,"unit",a)},expression:"formData.unit"}},e._l(e.unitList,function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})}),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"药本位码",prop:"standardCode"}},[a("el-input",{attrs:{placeholder:"请输入药品本位码",readonly:e.disabled},model:{value:e.formData.standardCode,callback:function(a){e.$set(e.formData,"standardCode",a)},expression:"formData.standardCode"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"药品类别",prop:"category"}},[a("el-select",{attrs:{placeholder:"请选择药品类别",disabled:e.disabled},model:{value:e.formData.category,callback:function(a){e.$set(e.formData,"category",a)},expression:"formData.category"}},e._l(e.categoryList,function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})}),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"医保目录",prop:"medicalInsurance"}},[a("el-select",{attrs:{placeholder:"请选择医保目录类别",disabled:e.disabled},model:{value:e.formData.medicalInsurance,callback:function(a){e.$set(e.formData,"medicalInsurance",a)},expression:"formData.medicalInsurance"}},[a("el-option",{attrs:{label:"甲类",value:"1"}}),a("el-option",{attrs:{label:"乙类",value:"2"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"国家集中集采",prop:"country"}},[a("el-switch",{attrs:{"active-value":"1","inactive-value":"0",disabled:e.disabled},model:{value:e.formData.country,callback:function(a){e.$set(e.formData,"country",a)},expression:"formData.country"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"生产企业",prop:"company"}},[a("el-input",{attrs:{readonly:e.disabled,placeholder:"请输入药品生产企业"},model:{value:e.formData.drugCompanyName,callback:function(a){e.$set(e.formData,"drugCompanyName",a)},expression:"formData.drugCompanyName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"批准文号",prop:"approvalNumber"}},[a("el-input",{attrs:{readonly:e.disabled,placeholder:"请输入药品批准文号"},model:{value:e.formData.approvalNumber,callback:function(a){e.$set(e.formData,"approvalNumber",a)},expression:"formData.approvalNumber"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-switch",{attrs:{"active-value":"1","inactive-value":"0",disabled:e.disabled},model:{value:e.formData.status,callback:function(a){e.$set(e.formData,"status",a)},expression:"formData.status"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",readonly:e.disabled,placeholder:"请填写备注"},model:{value:e.formData.remark,callback:function(a){e.$set(e.formData,"remark",a)},expression:"formData.remark"}})],1)],1)],1)],1)])],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){e.dialogVisible=!1}}},[e._v("关闭")])],1)])],1)},a.staticRenderFns=[]},ET8H:function(e,a,t){},MpIp:function(e,a,t){"use strict";t.r(a);var o=t("ytSV"),r=t("mZka");for(var l in r)["default"].indexOf(l)<0&&function(e){t.d(a,e,function(){return r[e]})}(l);t("mwNI");var s=t("gp09"),n=Object(s.a)(r.default,o.render,o.staticRenderFns,!1,null,"7494b9da",null);a.default=n.exports},mZka:function(e,a,t){"use strict";t.r(a);var o=t("xahW"),r=t.n(o);for(var l in o)["default"].indexOf(l)<0&&function(e){t.d(a,e,function(){return o[e]})}(l);a.default=r.a},mwNI:function(e,a,t){"use strict";t("ET8H")},xahW:function(e,a,t){Object.defineProperty(a,"__esModule",{value:!0});var o=s(t("/umX")),r=s(t("Q9c5")),l=s(t("DWNM"));s(t("XRYr"));function s(e){return e&&e.__esModule?e:{default:e}}a.default={name:"drug-all-list",mixins:[l.default],data:function(){var e,a;return a={disabled:!0,drugCatalogList:[],drugSourceList:[],drugDataList:[],tableHeight:100,dialogVisible:!1,brandFold:!1,params:{code:"",specs:"",catalogName:"",page:1,limit:10,records:0,standardCode:"",goodsName:"",source:"",catalogId:"",country:"0",countryType:"",electionScope:"",dosageForm:"",company:"",approvalNumber:"",status:""},formData:(e={version:"",code:"",drugCompanyName:"",category:"",id:"",source:"",goodsName:"",catalogName:"",catalogId:"",country:"",countryType:"",electionScope:"",dosageForm:"",specs:"",packingSpecs:"",packing:"",unit:"",qualityLevel:"",company:"",approvalNumber:"",standardCode:"",medicalInsurance:"",attribute:"",adjuvant:"0",net:"0",coefficient:"",remark:""},(0,o.default)(e,"version",""),(0,o.default)(e,"status",""),e),unitList:[{name:"盒",value:"盒"},{name:"支",value:"支"},{name:"片",value:"片"},{name:"丸",value:"丸"},{name:"粒",value:"粒"},{name:"袋",value:"袋"},{name:"瓶",value:"瓶"},{name:"剂",value:"剂"}],categoryList:[{name:"中药",value:"1"},{name:"西药",value:"2"}]},(0,o.default)(a,"drugCatalogList",[]),(0,o.default)(a,"dialogVisible",!1),a},props:{},computed:{},watch:{},methods:{getDrugCatalogList:function(){var e=this;this.drugCatalogList=[];var a=r.default.baseContext+"/supervise/supDrugCatalog/getDrugCatalogList",t=this.openLoading();this.$http_post(a,null).then(function(a){if(1==a.state)for(var o=a.rows,r=0;r<o.length;r++){var l=o[r].category,s="";"1"==l&&(s="中药"),"2"==l&&(s="西药");var n={id:o[r].id,name:o[r].name,category:s};e.drugCatalogList.push(n)}else e.$alert(a.message);t.close()})},changeFoldState:function(){this.brandFold=!this.brandFold},onSearch:function(e){"reset"==e?(this.params={code:"",specs:"",page:1,limit:10,records:0,standardCode:"",source:"",goodsName:"",catalogId:"",country:"",countryType:"",electionScope:"",dosageForm:"",company:"",approvalNumber:"",status:""},this.init()):(this.params.page=1,this.init()),this.brandFold=!1},onHideSearchCon:function(e){for(var a=0;a<e.path.length;a++){var t=e.path[a];if(t==this.$refs.searchCon||t==this.$refs.foldBtn)return}this.brandFold=!1},init:function(){this.getDrugCatalogList(),this.onDrugQuery()},showDrugDetail:function(e){var a=this,t=this.openLoading();this.$http_post(r.default.baseContext+"/supervise/supDrugDetail/info/"+e.id,{}).then(function(e){1==e.state?null!=e.row?(a.formData={version:e.row.version,catalogName:e.row.catalogName,drugCompanyName:e.row.drugCompanyName,category:e.row.category,id:e.row.id,code:e.row.code,source:e.row.source,catalogId:e.row.catalogId,goodsName:e.row.goodsName,country:e.row.country,countryType:e.row.countryType,electionScope:e.row.electionScope,dosageForm:e.row.dosageForm,specs:e.row.specs,packingSpecs:e.row.packingSpecs,packing:e.row.packing,unit:e.row.unit,qualityLevel:e.row.qualityLevel,company:e.row.company,approvalNumber:e.row.approvalNumber,standardCode:e.row.standardCode,medicalInsurance:e.row.medicalInsurance,attribute:e.row.attribute,adjuvant:e.row.adjuvant,net:e.row.net,coefficient:e.row.coefficient,remark:e.row.remark,status:e.row.status},a.country=e.row.country,a.drugCode=e.row.code,a.dialogVisible=!0):a.$message.error("系统异常"):null!=e.message?a.$message.error(e.message):a.$message.error("系统异常"),t.close()})},getDictItem:function(e){var a=this,t=this.openLoading();this.$http_post(r.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(o){var r=o.rows;1==o.state?("SOURCE"==e&&(a.drugSourceList=r),t.close()):(t.close(),a.$message.error(o.message))})},getDictItemName:function(e){for(var a=0;a<this.drugSourceList.length;a++)if(this.drugSourceList[a].value==e)return this.drugSourceList[a].label},onPageClick:function(e){this.params.page=e,this.onDrugQuery()},onDrugQuery:function(){var e=this,a=this.params,t=this.openLoading(),o=r.default.baseContext+"/supervise/supDrugDetail/getDrugListWithSource";this.$http_post(o,a).then(function(a){if(1==a.state){var o=a.rows;e.drugDataList=o,e.params.records=a.records,t.close()}else t.close(),e.$alert(a.message)})}},mounted:function(){var e=this;this.getDrugCatalogList(),this.getDictItem("SOURCE"),this.onDrugQuery(),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100},document.addEventListener("click",this.onHideSearchCon)},beforeDestroy:function(){window.onresize=null,document.removeEventListener("click",this.onHideSearchCon)}}},ytSV:function(e,a,t){"use strict";var o=t("2l5p");t.o(o,"render")&&t.d(a,"render",function(){return o.render}),t.o(o,"staticRenderFns")&&t.d(a,"staticRenderFns",function(){return o.staticRenderFns})}}]);