<template>
  <router-view v-if="isRouterAlive"/>
</template>

<script>
  import Config  from "@_src/api/app.config";
  export default {
    name:"App",
      data(){
        return{
            isRouterAlive: true
        }
      },
      mounted(){
        let themeUrl = "/"+Config.context+"/static/theme/"+this.$store.getters.globalSetting.themeStyle.substr(1)+"/index.css";
          //let themeUrl = "../static/theme/"+this.$store.getters.globalSetting.headerStyle.substr(1)+"/index.css";
        this.dynamicLoadCss(themeUrl);
      },
      methods:{
          dynamicLoadCss(url) {
              var head = document.getElementsByTagName('head')[0];
              var link = document.createElement('link');
              link.type='text/css';
              link.rel = 'stylesheet';
              link.href = url;
              head.appendChild(link);
          },
          /**
           * 全局页面刷新方法
           * inject: ['reload'],//在需要使用页面注入刷新事件
           * 如下示例：
               export default {
                  name:"App",
                  inject: ['reload'], //在此处注入
                  data(){
                    return{}
                  }
               }
               this.reload()  //使用刷新
           */
          reload () {
              this.isRouterAlive = false
              this.$nextTick(function() {
                  this.isRouterAlive = true
              })
          }
      },
      provide () {
          return {
              reload: this.reload
          }
      }
  };
</script>
<style lang="less">
@import './styles/common.less';
</style>
