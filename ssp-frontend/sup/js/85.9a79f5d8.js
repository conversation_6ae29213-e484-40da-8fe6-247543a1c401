(window.webpackJsonp=window.webpackJsonp||[]).push([[85],{"/xUo":function(e,t,a){"use strict";a.r(t);var s=a("kGCU"),o=a.n(s);for(var r in s)["default"].indexOf(r)<0&&function(e){a.d(t,e,function(){return s[e]})}(r);t.default=o.a},Crvj:function(e,t,a){"use strict";a("dp6R")},PKiM:function(e,t,a){"use strict";a.r(t);var s=a("e+O+"),o=a("/xUo");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,function(){return o[e]})}(r);a("Crvj");var i=a("gp09"),l=Object(i.a)(o.default,s.render,s.staticRenderFns,!1,null,"4c72e76f",null);t.default=l.exports},ZlsC:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.staticRenderFns=t.render=void 0;var s=function(e){return e&&e.__esModule?e:{default:e}}(a("/umX"));t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left"},[t("el-row",{attrs:{gutter:10}},[t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("订单号")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入订单号"},model:{value:e.params.orderNum,callback:function(t){e.$set(e.params,"orderNum",t)},expression:"params.orderNum"}})],1)])]),this.adminRole||this.medicalAdmin||this.socialAdmin||this.areaRegionAdmin?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("医疗机构")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:e.params.hospitalName,callback:function(t){e.$set(e.params,"hospitalName",t)},expression:"params.hospitalName"}},e._l(e.hospitalList,function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}),1)],1)])]):e._e(),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("预警状态")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",placeholder:"请选择状态"},model:{value:e.params.warning,callback:function(t){e.$set(e.params,"warning",t)},expression:"params.warning"}},[t("el-option",{key:"",attrs:{label:"全部",value:"0"}}),e._l(e.warningData,function(e){return t("el-option",{key:e.label,attrs:{value:e.value,label:e.label}})})],2)],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("下单时间")]),t("div",{staticClass:"searchInput"},[t("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:e.params.submitTime,callback:function(t){e.$set(e.params,"submitTime",t)},expression:"params.submitTime"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("采购平台")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",placeholder:"请选择平台"},model:{value:e.params.source,callback:function(t){e.$set(e.params,"source",t)},expression:"params.source"}},[t("el-option",{key:"",attrs:{label:"全部",value:""}}),t("el-option",{key:"1",attrs:{label:"深圳市药品交易平台",value:"1"}}),t("el-option",{key:"2",attrs:{label:"广东省药品交易平台",value:"2"}}),t("el-option",{key:"3",attrs:{label:"广州市药品交易平台",value:"3"}}),t("el-option",{key:"4",attrs:{label:"线下采购",value:"4"}})],1)],1)])]),this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("区域划分")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",placeholder:"请选择区划"},model:{value:e.params.regionId,callback:function(t){e.$set(e.params,"regionId",t)},expression:"params.regionId"}},e._l(e.regionList,function(e){return t("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})}),1)],1)])]):e._e(),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("配送状态")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",placeholder:"请选择配送状态"},model:{value:e.params.orderStatus,callback:function(t){e.$set(e.params,"orderStatus",t)},expression:"params.orderStatus"}},[t("el-option",{key:"",attrs:{label:"全部",value:""}}),t("el-option",{key:"0",attrs:{label:"待确认",value:"0"}}),t("el-option",{key:"1",attrs:{label:"待发货",value:"1"}}),t("el-option",{key:"2",attrs:{label:"部分发货",value:"2"}}),t("el-option",{key:"3",attrs:{label:"已发货",value:"3"}}),t("el-option",{key:"4",attrs:{label:"已完成",value:"4"}}),t("el-option",{key:"5",attrs:{label:"已取消",value:"5"}})],1)],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("入库状态")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",placeholder:"请选择入库状态"},model:{value:e.params.stockStatus,callback:function(t){e.$set(e.params,"stockStatus",t)},expression:"params.stockStatus"}},[t("el-option",{key:"",attrs:{label:"全部",value:""}}),t("el-option",{key:"0",attrs:{label:"未入库",value:"0"}}),t("el-option",{key:"1",attrs:{label:"已入库",value:"1"}}),t("el-option",{key:"2",attrs:{label:"部分入库",value:"2"}})],1)],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("支付状态")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",placeholder:"请选择支付状态"},model:{value:e.params.payStatus,callback:function(t){e.$set(e.params,"payStatus",t)},expression:"params.payStatus"}},[t("el-option",{key:"",attrs:{label:"全部",value:""}}),t("el-option",{key:"0",attrs:{label:"未支付",value:"0"}}),t("el-option",{key:"1",attrs:{label:"已支付",value:"1"}}),t("el-option",{key:"2",attrs:{label:"部分支付",value:"2"}})],1)],1)])])],1)],1),t("div",{staticClass:"right"},[t("div",{staticStyle:{"margin-bottom":"5px"}},[t("el-button",{attrs:{size:"small",type:"warning",icon:"el-icon-download"},on:{click:e.orderImport}},[e._v("导入订单")]),t("el-button",{attrs:{size:"small",type:"warning",icon:"el-icon-download"},on:{click:e.orderBackImport}},[e._v("导入退回订单")])],1),t("div",[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1)])]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.dataList,"highlight-current-row":"",height:e.tableHeight,border:""}},[t("el-table-column",{attrs:{prop:"orderNum",label:"订单号",width:"180"}}),t("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"180",align:"left"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("div",[e._v("\n                    "+e._s(e.getDictItemName(a.row.source))+"\n                  ")])]}}])}),t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"200"}}),t("el-table-column",{attrs:{prop:"totalPrice",label:"订单总金额(元)",width:"200"}}),t("el-table-column",{attrs:{prop:"orderStatus",label:"订单状态"},scopedSlots:e._u([{key:"default",fn:function(a){return["0"==a.row.orderStatus?t("div",[t("el-tag",{attrs:{type:"info"}},[e._v("待确认")])],1):e._e(),"1"==a.row.orderStatus?t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("待发货")])],1):e._e(),"2"==a.row.orderStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分发货")])],1):e._e(),"3"==a.row.orderStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已发货")])],1):e._e(),"4"==a.row.orderStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已完成")])],1):e._e(),"5"==a.row.orderStatus?t("div",[t("el-tag",{attrs:{type:"info"}},[e._v("已取消")])],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"payStatus",label:"入库状态"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.stockStatus&&"0"!=a.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未入库")])],1),"1"==a.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已入库")])],1):e._e(),"2"==a.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分入库")])],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"payStatus",label:"支付状态"},scopedSlots:e._u([{key:"default",fn:function(a){return["0"!=a.row.payStatus&&a.row.payStatus||a.row.stockStatus&&"0"!=a.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("未支付")])],1),"0"!=a.row.payStatus&&a.row.payStatus||!a.row.stockStatus||"0"==a.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未支付")])],1),"1"==a.row.payStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已支付")])],1):e._e(),"2"==a.row.payStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分支付")])],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"warning",label:"预警状态"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.warning&&"1"!=a.row.warning?t("el-popover",{attrs:{placement:"top-start",width:"250",trigger:"hover"}},[e._l(e.getWaring(a.row.warning),function(a,s){return t("div",{key:a.name,staticClass:"text item"},[e._v("\n                                    "+e._s(s+1+"、"+a.name)+"\n                                ")])}),t("el-tag",{attrs:{slot:"reference",type:"danger"},slot:"reference"},[e._v("查看预警")])],2):t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("正常")])],1)]}}])}),t("el-table-column",{attrs:{prop:"submitTime",formatter:e.time,label:"采购时间"}}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showOrder(a.row)}}},[e._v("查看详情")])]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)]),t("el-dialog",{staticClass:"upload-box",attrs:{title:"药品订单明细导入",visible:e.importDialogVisible,"append-to-body":"",width:"40%"},on:{"update:visible":function(t){e.importDialogVisible=t}}},[t("el-form",{ref:"importDataForm",attrs:{"label-position":"left",model:e.uploadData}},[t("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:(0,s.default)({drag:"",limit:1,headers:e.headers,action:e.uploadUrl,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"on-exceed":e.handleExceed,"before-upload":e.beforeUpload,"before-remove":e.beforeRemove,"on-success":e.onSuccess,"on-error":e.onError,multiple:"",accept:".xls,.xlsx","file-list":e.fileList},"multiple",!1)},[t("i",{staticClass:"el-icon-upload"}),t("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),t("em",[e._v("点击上传")])]),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("只能上传【 .xlsx / .xls 】文件")])])],1),t("span",{staticStyle:{color:"red"}},[e._v("提示：请下载 Excel 模板，按照格式进行导入！")]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitUpload("")}}},[e._v("确 定")]),t("el-button",{attrs:{type:"warning"},on:{click:function(t){return e.downloadTemplate("")}}},[e._v("下载模板")]),t("el-button",{on:{click:e.cancel}},[e._v("取消")])],1)],1),t("el-dialog",{staticClass:"upload-box",attrs:{title:"退货订单明细导入",visible:e.importBackDialogVisible,"append-to-body":"",width:"40%"},on:{"update:visible":function(t){e.importBackDialogVisible=t}}},[t("el-form",{ref:"importDataForm",attrs:{"label-position":"left",model:e.uploadData}},[t("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:(0,s.default)({drag:"",limit:1,headers:e.headers,action:e.uploadUrl,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"on-exceed":e.handleExceed,"before-upload":e.beforeUpload,"before-remove":e.beforeRemove,"on-success":e.onSuccess,"on-error":e.onError,multiple:"",accept:".xls,.xlsx","file-list":e.fileList},"multiple",!1)},[t("i",{staticClass:"el-icon-upload"}),t("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),t("em",[e._v("点击上传")])]),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("只能上传【 .xlsx / .xls 】文件")])])],1),t("span",{staticStyle:{color:"red"}},[e._v("提示：请下载 Excel 模板，按照格式进行导入！")]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitUploadOne("")}}},[e._v("确 定")]),t("el-button",{attrs:{type:"warning"},on:{click:function(t){return e.downloadTemplate1("")}}},[e._v("下载模板")]),t("el-button",{on:{click:e.cancel}},[e._v("取消")])],1)],1)],1)},t.staticRenderFns=[]},dp6R:function(e,t,a){},"e+O+":function(e,t,a){"use strict";var s=a("ZlsC");a.o(s,"render")&&a.d(t,"render",function(){return s.render}),a.o(s,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return s.staticRenderFns})},kGCU:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var s=i(a("Q9c5")),o=i(a("DWNM")),r=i(a("XRYr"));function i(e){return e&&e.__esModule?e:{default:e}}t.default={name:"countryOrderImport",mixins:[o.default],data:function(){return{pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),a=new Date,s=new Date(a.getTime()-6048e5).format("yyyy-MM-dd");e.$emit("pick",[s,t])}},{text:"最近一个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),a=new Date,s=new Date(a.getTime()-2592e6).format("yyyy-MM-dd");e.$emit("pick",[s,t])}},{text:"最近三个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),a=new Date,s=new Date(a.getTime()-7776e6).format("yyyy-MM-dd");e.$emit("pick",[s,t])}}]},regionList:[{code:"",name:"全部"},{code:"440703",name:"蓬江区"},{code:"440704",name:"江海区"},{code:"440705",name:"新会区"},{code:"440781",name:"台山市"},{code:"440783",name:"开平市"},{code:"440784",name:"鹤山市"},{code:"440785",name:"恩平市"}],adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1,areaRegionAdmin:!1,drugSourceList:[],rLoading:{},fileList:[],headers:{},uploadData:{},importDialogVisible:!1,importBackDialogVisible:!1,warningData:[],tableHeight:100,dialogVisible:!1,dataList:[],params:{submitTime:"",hospitalId:"",source:"",orderStatus:"",page:1,limit:10,records:0,orderNum:"",warning:"",status:"",regionId:"",stockStatus:"",payStatus:""},orderData:{data:{},orderItem:[],hospital:{}},hospitalList:[]}},props:{},computed:{uploadUrl:function(){return s.default.baseContext+"/file/upload"}},watch:{},methods:{initRole:function(){var e=this.$store.getters.curUser.roleCode;-1!=e.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=e.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=e.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=e.indexOf("DELIVERY_ADMIN")?this.areaRegionAdmin=!0:-1!=e.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)},getDictItemName:function(e){for(var t=0;t<this.drugSourceList.length;t++)if(this.drugSourceList[t].value==e)return this.drugSourceList[t].label},getHospitalList:function(){var e=this,t=s.default.baseContext+"/supervise/supHospital/all";this.$http_post(t,{}).then(function(t){1==t.state?e.hospitalList=t.row:e.$alert(t.message)})},cancel:function(){this.importDialogVisible=!1,this.onQuery()},downloadTemplate:function(){var e=s.default.baseContext+"/file/downloadOrderImportTemplate",t=document.createElement("a");t.href=e,document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(e)},downloadTemplate1:function(){var e=s.default.baseContext+"/file/downloadBackOrderImportTemplate",t=document.createElement("a");t.href=e,document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(e)},submitUpload:function(){var e=this;this.rLoading=this.openLoading("导入中,请耐心等待...");var t=this.uploadData.docId;this.$http_post(s.default.baseContext+"/supervise/supOrder/importCountryOrder?docId="+t,{},null,{timeout:1e5}).then(function(t){1==t.state?(e.onQuery(),e.rLoading.close(),e.importDialogVisible=!1,e.$alert("导入成功，请前往【订单批次日志】查看导入结果")):(e.onQuery(),e.rLoading.close(),e.importDialogVisible=!1,e.$message.error(t.message))})},submitUploadOne:function(){var e=this;this.rLoading=this.openLoading("导入中,请耐心等待...");var t=this.uploadData.docId;this.$http_post(s.default.baseContext+"/supervise/supOrder/importExitCountryOrder?docId="+t,{},null,{timeout:1e5}).then(function(t){1==t.state?(e.onQuery(),e.rLoading.close(),e.importDialogVisible=!1,e.$alert("导入成功，请前往【订单批次日志】查看导入结果")):(e.onQuery(),e.rLoading.close(),e.importDialogVisible=!1,e.$message.error(t.message))})},onSuccess:function(e,t,a){if("1"!=e.state)return this.$refs.upload.clearFiles(),this.$alert(e.message),!1;t.docId=e.row.id,this.uploadData={docId:e.row.id,name:e.row.name}},onError:function(e,t,a){this.rLoading.close(),console.log(e),this.$message.error("上传失败,请重试")},handlePreview:function(e){var t=this.downloadUrl+"?docId="+encodeURI(encodeURI(e.docId))+"&fileName="+e.name;console.log(e);var a=document.createElement("a");a.href=t,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(t)},handleExceed:function(e,t){this.$message.warning("一次只能上传一个Excel文件")},beforeUpload:function(e){if(e.size/1024/1024>20)return this.$message.error("上传文件过大，请分开上传，单个文件最大为20M。"),!1;var t=e.name;return"xlsx"==t.substring(t.length-4)||"xls"==t.substring(t.length-3)||(this.$message.error("上传文件格式只能为【.xlsx】或【.xls】"),!1)},beforeRemove:function(e,t){return!this.beforeUpload(e)||this.$confirm("确定移除 "+e.name+"？")},handleRemove:function(e,t){return this.uploadData={},console.log(e.docId,e,t),!0},orderImport:function(){this.importDialogVisible=!0},orderBackImport:function(){this.importBackDialogVisible=!0},getWaringType:function(e){for(var t=0;t<this.warningData.length;t++)if(this.warningData[t].value==e)return this.warningData[t].label},getWaring:function(e){for(var t=[],a=e.split(","),s=0;s<a.length;s++){var o=this.getWaringType(a[s]);o&&t.push({name:o})}return t},getDictItem:function(e){var t=this,a=this.openLoading();this.$http_post(s.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(s){var o=s.rows;1==s.state?("SOURCE"==e&&(t.drugSourceList=o),"WARNING"==e&&(t.warningData=o),a.close()):(a.close(),t.$message.error(s.message))})},showOrder:function(e){this.$router.push({name:"orderDetail",query:{orderId:e.id}})},onSearch:function(e){"reset"==e?(this.params.orderNum="",this.params.warning="",this.params.submitTime="",this.params.source="",this.params.hospitalId="",this.onQuery()):(this.params.page=1,this.onQuery())},onPageClick:function(e){this.params.page=e,this.onQuery()},onQuery:function(){var e=this,t=this.params,a=this.openLoading(),o=s.default.baseContext+"/supervise/supOrder/list";this.$http_post(o,t).then(function(t){1==t.state?(e.dataList=t.rows,e.params.records=t.records,a.close()):(a.close(),e.$alert(t.message))})},time:function(e,t){if(void 0==e.submitTime||""==e.submitTime)return"";var a=new Date(e.submitTime),s=a.getFullYear()+"-",o=(a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-",r=a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ";a.getHours(),a.getHours(),a.getMinutes(),a.getMinutes(),a.getSeconds(),a.getSeconds();return s+o+r},init:function(){}},mounted:function(){var e=this,t=r.default.doCloundRequest(s.default.app_key,s.default.app_security,"");this.headers["x-aep-appkey"]=t["x-aep-appkey"],this.headers["x-aep-signature"]=t["x-aep-signature"],this.headers["x-aep-timestamp"]=t["x-aep-timestamp"],this.headers["x-aep-nonce"]=t["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,this.$route.query.warning&&(this.params.warning=this.$route.query.warning),this.initRole(),this.onQuery(),this.getHospitalList(),this.getDictItem("WARNING"),this.getDictItem("SOURCE"),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-180}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-180}},beforeDestroy:function(){window.onresize=null},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ")+(t.getHours()<10?"0"+t.getHours()+":":t.getHours()+":")+(t.getMinutes()<10?"0"+t.getMinutes()+":":t.getMinutes()+":")+(t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds())}}}}}]);