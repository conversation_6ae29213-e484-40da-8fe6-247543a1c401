(window.webpackJsonp=window.webpackJsonp||[]).push([[95],{"/OI6":function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var i=r(a("DWNM")),s=r(a("Q9c5"));function r(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[i.default],name:"orderStatistics",data:function(){return{expandRows:[],orderNumByHospital:[],drugSourceList:[],myChart:"",itemParams:{submitTime:[new Date((new Date).getTime()-2592e6).format("yyyy-MM-dd"),(new Date).format("yyyy-MM-dd")]},orderNumBySource:[],title:{totalTile:"订单累计总数",normalTitle:"正常订单总数",warningTitle:"异常订单总数",barTitle:"医院订单排名"},hospitalList:[],orderCount:{totalCount:592534,warningCount:53454,normalCount:543553},pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,i=new Date(a.getTime()-6048e5).format("yyyy-MM-dd");t.$emit("pick",[i,e])}},{text:"最近一个月",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,i=new Date(a.getTime()-2592e6).format("yyyy-MM-dd");t.$emit("pick",[i,e])}},{text:"最近三个月",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,i=new Date(a.getTime()-7776e6).format("yyyy-MM-dd");t.$emit("pick",[i,e])}}]}}},watch:{},mounted:function(){this.init(),this.getNum(),this.getDictItem("SOURCE"),this.getHospitalList()},methods:{getHospitalList:function(){var t=this,e=s.default.baseContext+"/supervise/supHospital/all";this.$http_post(e,{}).then(function(e){1==e.state?t.hospitalList=e.row:t.$alert(e.message)})},reset:function(){this.itemParams.hospitalName="",this.itemParams.source="",this.getNum()},getDictItem:function(t){var e=this,a=this.openLoading();this.$http_post(s.default.baseContext+"/supervise/supDictItem/getItemList",{code:t}).then(function(i){var s=i.rows;1==i.state?("SOURCE"==t&&(e.drugSourceList=s),a.close()):(a.close(),e.$message.error(i.message))})},getDictItemName:function(t){for(var e=0;e<this.drugSourceList.length;e++)if(this.drugSourceList[e].value==t)return this.drugSourceList[e].label},getDictItemValue:function(t){for(var e=0;e<this.drugSourceList.length;e++)if(this.drugSourceList[e].label==t)return this.drugSourceList[e].value},showOrderList:function(t,e,a){this.itemParams.submitTime,this.itemParams.type=t,this.itemParams.hospitalName=e,this.itemParams.source=a,this.$router.push({path:"orderStatisticsList",query:this.itemParams})},init:function(){var t=this.$route.query.isCountry;this.itemParams={submitTime:[new Date((new Date).getTime()-2592e6).format("yyyy-MM-dd"),(new Date).format("yyyy-MM-dd")],isCountry:t}},getNum:function(){var t=this,e=this.openLoading();this.$http_post(s.default.baseContext+"/supervise/statistics/getOrderStatistics",this.itemParams).then(function(a){if(1==a.state)if(null!=a.row){var i=a.row.orderCount;t.$set(t,"orderCount",i),t.$set(t,"orderNumByHospital",a.row.orderBarData),t.expandRows.push(t.orderNumByHospital[0].id)}else t.$message.error("系统异常");else null!=a.message?t.$message.error(a.message):t.$message.error("系统异常");e.close()}).catch(function(t){e.close(),console.log(t)})},initHospitalChart:function(t){var e=this,a=[],i=[],s=[],r=[];for(var o in t){var n=t[o].totalCount,l=t[o].normalCount,c=t[o].warningCount,u=t[o].hospitalName;a.push(u),i.push(n),s.push(l),r.push(c)}var d={title:{text:" "},tooltip:{trigger:"axis"},legend:{data:["订单总数","订单正常数","订单异常数"]},toolbox:{show:!0,feature:{saveAsImage:{show:!0}}},calculable:!0,xAxis:[{axisLabel:{interval:0,rotate:45,fontSize:14},type:"category",data:a}],grid:{bottom:"28%"},yAxis:[{axisLabel:{formatter:"{value} ",fontSize:14},type:"value"}],series:[{name:"订单总数",type:"bar",data:i,itemStyle:{normal:{color:"#6AA4F6"}},barMaxWidth:50},{name:"订单正常数",type:"bar",data:s,itemStyle:{normal:{color:"#4ABFF5"}},barMaxWidth:50},{name:"订单异常数",type:"bar",data:r,itemStyle:{normal:{color:"#ff787d"}},barMaxWidth:50}]};this.myChart=this.$echarts.init(this.$refs.serviceEcharts),this.myChart.setOption(d),this.myChart.on("click",function(t){e.itemParams.hospitalName=t.name,e.itemParams.type=t.seriesIndex,e.$router.push({path:"orderStatisticsList",query:e.itemParams})}),window.onresize=function(){e.myChart.resize()}},initSourceChart:function(t){var e=this,a=[],i=[],s=[],r=[];for(var o in t){var n=t[o].totalCount,l=t[o].normalCount,c=t[o].warningCount,u=this.getDictItemName(t[o].source);a.push(u),i.push(n),s.push(l),r.push(c)}var d={title:{text:" "},tooltip:{trigger:"axis"},legend:{data:["订单总数","订单正常数","订单异常数"]},toolbox:{show:!0,feature:{saveAsImage:{show:!0}}},calculable:!0,xAxis:[{axisLabel:{interval:0,rotate:45,fontSize:14},type:"category",data:a}],grid:{bottom:"28%"},yAxis:[{axisLabel:{formatter:"{value} ",fontSize:14},type:"value"}],series:[{name:"订单总数",type:"bar",data:i,itemStyle:{normal:{color:"#6AA4F6"}},barMaxWidth:50},{name:"订单正常数",type:"bar",data:s,itemStyle:{normal:{color:"#4ABFF5"}},barMaxWidth:50},{name:"订单异常数",type:"bar",data:r,itemStyle:{normal:{color:"#ff787d"}},barMaxWidth:50}]};this.myChart=this.$echarts.init(this.$refs.orderSourceEcharts),this.myChart.setOption(d),this.myChart.on("click",function(t){console.log(t),e.itemParams.source=e.getDictItemValue(t.name),e.itemParams.type=t.seriesIndex,e.$router.push({path:"orderStatisticsList",query:e.itemParams})}),window.onresize=function(){e.myChart.resize()}},created:function(){window.addEventListener("resize",this.chartsHeight)},chartsHeight:function(){void 0!=this.myChart&&""!=this.myChart&&this.myChart.resize()}}}},"1/XT":function(t,e,a){"use strict";a("yH4h")},"9aTi":function(t,e,a){"use strict";var i=a("yzU1");a.o(i,"render")&&a.d(e,"render",function(){return i.render}),a.o(i,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return i.staticRenderFns})},NqTL:function(t,e,a){"use strict";a.r(e);var i=a("9aTi"),s=a("mB4A");for(var r in s)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return s[t]})}(r);a("1/XT");var o=a("gp09"),n=Object(o.a)(s.default,i.render,i.staticRenderFns,!1,null,"59d47d86",null);e.default=n.exports},mB4A:function(t,e,a){"use strict";a.r(e);var i=a("/OI6"),s=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return i[t]})}(r);e.default=s.a},yH4h:function(t,e,a){},yzU1:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0;var i=function(t){return t&&t.__esModule?t:{default:t}}(a("/umX"));e.render=function(){var t,e=this,a=e._self._c;return a("div",{staticClass:"singleSite"},[a("div",{staticClass:"overview"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"change felx flex-sb"},[a("div",{staticClass:"detail1",on:{click:function(t){return e.showOrderList("0")}}},[a("div",{staticClass:"title flex flex-row"},[a("div",{staticClass:"icon flex flex-row"},[a("i",{staticClass:"iconfont icon-zongrenkou"})]),a("span",[e._v(e._s(e.title.totalTile))])]),a("div",{staticClass:"dataView"},[a("ul",{staticClass:"flex flex-row"},[a("li",[a("div",[a("p",[e._v(e._s(e.orderCount.totalCount))])])])])]),a("div",{staticClass:"bg"},[a("div",{staticClass:"bg1"}),a("div",{staticClass:"bg2"})])]),a("div",{staticClass:"detail2",on:{click:function(t){return e.showOrderList("1")}}},[a("div",{staticClass:"title flex flex-row"},[a("div",{staticClass:"icon flex flex-row"},[a("i",{staticClass:"iconfont icon-zengjia1"})]),a("span",[e._v(e._s(e.title.normalTitle))])]),a("div",{staticClass:"dataView"},[a("ul",{staticClass:"flex flex-row"},[a("li",[a("div",[a("p",[e._v(e._s(e.orderCount.normalCount))])])])])]),a("div",{staticClass:"bg"},[a("div",{staticClass:"bg1"}),a("div",{staticClass:"bg2"})])]),a("div",{staticClass:"detail3",on:{click:function(t){return e.showOrderList("2")}}},[a("div",{staticClass:"title flex flex-row"},[a("div",{staticClass:"icon flex flex-row"},[a("i",{staticClass:"iconfont icon-cunkuan1"})]),a("span",[e._v(e._s(e.title.warningTitle))])]),a("div",{staticClass:"dataView"},[a("ul",{staticClass:"flex flex-row"},[a("li",[a("div",[a("p",[e._v(e._s(e.orderCount.warningCount))])])])])]),a("div",{staticClass:"bg"},[a("div",{staticClass:"bg1"}),a("div",{staticClass:"bg2"})])])])])],1)],1),a("div",{staticClass:"dataview"},[a("div",{staticClass:"search-header flex-row"},[a("div",{staticClass:"left flex-row"},[a("span",[e._v("订单时间")]),a("el-date-picker",{staticStyle:{width:"20%","margin-right":"10px"},attrs:{"unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:e.itemParams.submitTime,callback:function(t){e.$set(e.itemParams,"submitTime",t)},expression:"itemParams.submitTime"}}),a("span",[e._v("医疗机构")]),a("el-select",{staticStyle:{width:"15%"},attrs:(t={clearable:""},(0,i.default)(t,"clearable",""),(0,i.default)(t,"filterable",""),(0,i.default)(t,"placeholder","请选择医疗机构"),t),model:{value:e.itemParams.hospitalName,callback:function(t){e.$set(e.itemParams,"hospitalName",t)},expression:"itemParams.hospitalName"}},e._l(e.hospitalList,function(t){return a("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})}),1),a("span",[e._v("采购平台")]),a("el-select",{attrs:{clearable:"",placeholder:"请选择平台"},model:{value:e.itemParams.source,callback:function(t){e.$set(e.itemParams,"source",t)},expression:"itemParams.source"}},[a("el-option",{key:"",attrs:{label:"全部",value:""}}),a("el-option",{key:"1",attrs:{label:"深圳市药品交易平台",value:"1"}}),a("el-option",{key:"2",attrs:{label:"广东省药品交易平台",value:"2"}}),a("el-option",{key:"3",attrs:{label:"广州市药品交易平台",value:"3"}})],1),a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.getNum()}}},[e._v("查询")]),a("el-button",{attrs:{icon:"el-icon-search"},on:{click:e.reset}},[e._v("重置")])],1)]),a("el-row",{staticClass:"con"},[a("div",{staticClass:"con-left"},[a("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.orderNumByHospital,"highlight-current-row":"","tree-props":{children:"children",hasChildren:"hasChildren"},"row-key":"id","expand-row-keys":e.expandRows}},[a("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",align:"left"}}),a("el-table-column",{attrs:{prop:"source",label:"采购平台",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[e._v("\n                  "+e._s(e.getDictItemName(t.row.source))+"\n                ")])]}}])}),a("el-table-column",{attrs:{prop:"totalCount",label:"订单总数",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.totalCount?a("el-button",{attrs:{type:"primary",plain:""},on:{click:function(a){return e.showOrderList("0",t.row.hospitalName,t.row.source)}}},[e._v("\n                  "+e._s(t.row.totalCount)+"\n                ")]):e._e()]}}])}),a("el-table-column",{attrs:{prop:"normalCount",label:"订单正常数",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.normalCount?a("el-button",{attrs:{type:"success",plain:""},on:{click:function(a){return e.showOrderList("1",t.row.hospitalName,t.row.source)}}},[e._v("\n                  "+e._s(t.row.normalCount)+"\n                ")]):e._e()]}}])}),a("el-table-column",{attrs:{prop:"warningCount",label:"订单异常数",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.warningCount?a("el-button",{attrs:{type:"danger",plain:""},on:{click:function(a){return e.showOrderList("2",t.row.hospitalName,t.row.source)}}},[e._v("\n                  "+e._s(t.row.warningCount)+"\n                ")]):e._e()]}}])})],1)],1)])],1)])},e.staticRenderFns=[]}}]);