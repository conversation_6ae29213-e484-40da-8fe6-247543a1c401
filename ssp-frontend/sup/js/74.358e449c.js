(window.webpackJsonp=window.webpackJsonp||[]).push([[74],{"03Nt":function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=n(a("Q9c5")),o=n(a("DWNM")),s=n(a("XRYr"));function n(e){return e&&e.__esModule?e:{default:e}}t.default={name:"generateOrder",mixins:[o.default],data:function(){return{rLoading:{},fileList:[],headers:{},uploadData:{},importDialogVisible:!1,warningData:[],tableHeight:100,dialogVisible:!1,dataList:[],params:{orderStatus:"",page:1,limit:10,records:0,orderNum:"",warning:"",status:"",source:"4"},orderData:{data:{},orderItem:[],hospital:{}}}},props:{},computed:{uploadUrl:function(){return r.default.baseContext+"/file/upload"}},watch:{},methods:{addOrder:function(){this.$router.push({name:"generateOrderItem"})},cancel:function(){this.importDialogVisible=!1,this.onQuery()},downloadTemplate:function(){var e=r.default.baseContext+"/file/downloadOfflineOrderTemplate",t=document.createElement("a");t.href=e,document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(e)},submitUpload:function(){var e=this;this.rLoading=this.openLoading("导入中,请耐心等待...");var t=this.uploadData.docId;this.$http_post(r.default.baseContext+"/supervise/supOrder/importOfflineOrder?docId="+t,{},null,{timeout:1e5}).then(function(t){1==t.state?(e.onQuery(),e.rLoading.close(),e.importDialogVisible=!1,e.$alert("导入成功，请前往【线下订单批次日志】查看导入结果")):(e.onQuery(),e.rLoading.close(),e.importDialogVisible=!1,e.$message.error(t.message))})},onSuccess:function(e,t,a){if("1"!=e.state)return this.$refs.upload.clearFiles(),this.$alert(e.message),!1;t.docId=e.row.id,this.uploadData={docId:e.row.id,name:e.row.name}},onError:function(e,t,a){this.rLoading.close(),console.log(e),this.$message.error("上传失败,请重试")},handlePreview:function(e){var t=this.downloadUrl+"?docId="+encodeURI(encodeURI(e.docId))+"&fileName="+e.name;console.log(e);var a=document.createElement("a");a.href=t,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(t)},handleExceed:function(e,t){this.$message.warning("一次只能上传一个Excel文件")},beforeUpload:function(e){if(e.size/1024/1024>20)return this.$message.error("上传文件过大，请分开上传，单个文件最大为20M。"),!1;var t=e.name;return"xlsx"==t.substring(t.length-4)||"xls"==t.substring(t.length-3)||(this.$message.error("上传文件格式只能为【.xlsx】或【.xls】"),!1)},beforeRemove:function(e,t){return!this.beforeUpload(e)||this.$confirm("确定移除 "+e.name+"？")},handleRemove:function(e,t){return this.uploadData={},console.log(e.docId,e,t),!0},orderImport:function(){this.importDialogVisible=!0},getWaringType:function(e){for(var t=0;t<this.warningData.length;t++)if(this.warningData[t].value==e)return this.warningData[t].label},getWaring:function(e){for(var t=[],a=e.split(","),r=0;r<a.length;r++){var o=this.getWaringType(a[r]);o&&t.push({name:o})}return t},getDictItem:function(e){var t=this,a=this.openLoading();this.$http_post(r.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(r){var o=r.rows;1==r.state?("WARNING"==e&&(t.warningData=o),a.close()):(a.close(),t.$message.error(r.message))})},showOrder:function(e){this.$router.push({name:"orderDetail",query:{orderId:e.id}})},onSearch:function(e){"reset"==e?(this.params.orderNum="",this.params.warning="",this.params.orderStatus="",this.onQuery()):""!=this.params.orderNum||""!=this.params.warning||""!=this.params.orderStatus?(this.params.page=1,this.onQuery()):this.$message.warning("请输入查询条件")},onPageClick:function(e){this.params.page=e,this.onQuery()},onQuery:function(){var e=this,t=this.params,a=this.openLoading(),o=r.default.baseContext+"/supervise/supOrder/list";this.$http_post(o,t).then(function(t){1==t.state?(e.dataList=t.rows,e.params.records=t.records,a.close()):(a.close(),e.$alert(t.message))})},time:function(e,t){if(void 0==e.submitTime||""==e.submitTime)return"";var a=new Date(e.submitTime);return a.getFullYear()+"-"+((a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-")+(a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ")},init:function(){}},mounted:function(){var e=this,t=s.default.doCloundRequest(r.default.app_key,r.default.app_security,"");this.headers["x-aep-appkey"]=t["x-aep-appkey"],this.headers["x-aep-signature"]=t["x-aep-signature"],this.headers["x-aep-timestamp"]=t["x-aep-timestamp"],this.headers["x-aep-nonce"]=t["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,this.$route.query.warning&&(this.params.warning=this.$route.query.warning),this.onQuery(),this.getDictItem("WARNING"),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ")+(t.getHours()<10?"0"+t.getHours()+":":t.getHours()+":")+(t.getMinutes()<10?"0"+t.getMinutes()+":":t.getMinutes()+":")+(t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds())}}}},"464F":function(e,t,a){},b8J4:function(e,t,a){"use strict";a("464F")},cwC2:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.staticRenderFns=t.render=void 0;var r=function(e){return e&&e.__esModule?e:{default:e}}(a("/umX"));t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"},[t("span",[e._v("订单号")]),t("el-input",{attrs:{placeholder:"请输入订单号"},model:{value:e.params.orderNum,callback:function(t){e.$set(e.params,"orderNum",t)},expression:"params.orderNum"}}),t("span",[e._v("预警状态")]),t("el-select",{attrs:{placeholder:"请选择状态"},model:{value:e.params.warning,callback:function(t){e.$set(e.params,"warning",t)},expression:"params.warning"}},e._l(e.warningData,function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1),t("span",[e._v("订单状态")]),t("el-select",{attrs:{placeholder:"全部"},model:{value:e.params.orderStatus,callback:function(t){e.$set(e.params,"orderStatus",t)},expression:"params.orderStatus"}},[t("el-option",{key:"0",attrs:{label:"待确认",value:"0"}}),t("el-option",{key:"1",attrs:{label:"待发货",value:"1"}}),t("el-option",{key:"2",attrs:{label:"部分发货",value:"2"}}),t("el-option",{key:"3",attrs:{label:"已发货",value:"3"}}),t("el-option",{key:"4",attrs:{label:"已完成",value:"4"}}),t("el-option",{key:"5",attrs:{label:"已取消",value:"5"}})],1),t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1),t("el-button",{attrs:{type:"success",icon:"el-icon-download"},on:{click:e.orderImport}},[e._v("导入线下订单")]),t("el-button",{attrs:{type:"warning",icon:"el-icon-download"},on:{click:e.addOrder}},[e._v("录入订单信息")])],1),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.dataList,"highlight-current-row":"",height:e.tableHeight,border:""}},[t("el-table-column",{attrs:{prop:"orderNum",label:"订单号",width:"180"}}),t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"200"}}),t("el-table-column",{attrs:{prop:"totalPrice",label:"订单总金额(元)",width:"200"}}),t("el-table-column",{attrs:{prop:"orderStatus",label:"订单状态"},scopedSlots:e._u([{key:"default",fn:function(a){return["0"==a.row.orderStatus?t("div",[t("el-tag",{attrs:{type:"info"}},[e._v("待确认")])],1):e._e(),"1"==a.row.orderStatus?t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("待发货")])],1):e._e(),"2"==a.row.orderStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分发货")])],1):e._e(),"3"==a.row.orderStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已发货")])],1):e._e(),"4"==a.row.orderStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已完成")])],1):e._e(),"5"==a.row.orderStatus?t("div",[t("el-tag",{attrs:{type:"info"}},[e._v("已取消")])],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"payStatus",label:"入库状态"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.stockStatus&&"0"!=a.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未入库")])],1),"1"==a.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已入库")])],1):e._e(),"2"==a.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分入库")])],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"payStatus",label:"支付状态"},scopedSlots:e._u([{key:"default",fn:function(a){return["0"!=a.row.payStatus&&a.row.payStatus||a.row.stockStatus&&"0"!=a.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("未支付")])],1),"0"!=a.row.payStatus&&a.row.payStatus||!a.row.stockStatus||"0"==a.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未支付")])],1),"1"==a.row.payStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已支付")])],1):e._e(),"2"==a.row.payStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分支付")])],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"warning",label:"预警状态"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.warning&&"1"!=a.row.warning?e._e():t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("正常")])],1),"0"==a.row.warning?t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("异常")])],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"submitTime",formatter:e.time,label:"采购时间"}}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showOrder(a.row)}}},[e._v("查看详情")])]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)]),t("el-dialog",{staticClass:"upload-box",attrs:{title:"线下药品订单明细导入",visible:e.importDialogVisible,"append-to-body":"",width:"40%"},on:{"update:visible":function(t){e.importDialogVisible=t}}},[t("el-form",{ref:"importDataForm",attrs:{"label-position":"left",model:e.uploadData}},[t("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:(0,r.default)({drag:"",limit:1,headers:e.headers,action:e.uploadUrl,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"on-exceed":e.handleExceed,"before-upload":e.beforeUpload,"before-remove":e.beforeRemove,"on-success":e.onSuccess,"on-error":e.onError,multiple:"",accept:".xls,.xlsx","file-list":e.fileList},"multiple",!1)},[t("i",{staticClass:"el-icon-upload"}),t("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),t("em",[e._v("点击上传")])]),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("只能上传【 .xlsx / .xls 】文件")])])],1),t("span",{staticStyle:{color:"red"}},[e._v("提示：请下载 Excel 模板，按照格式进行导入！")]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitUpload("")}}},[e._v("确 定")]),t("el-button",{attrs:{type:"warning"},on:{click:function(t){return e.downloadTemplate("")}}},[e._v("下载模板")]),t("el-button",{on:{click:e.cancel}},[e._v("取消")])],1)],1)],1)},t.staticRenderFns=[]},iMtA:function(e,t,a){"use strict";a.r(t);var r=a("03Nt"),o=a.n(r);for(var s in r)["default"].indexOf(s)<0&&function(e){a.d(t,e,function(){return r[e]})}(s);t.default=o.a},vAAY:function(e,t,a){"use strict";var r=a("cwC2");a.o(r,"render")&&a.d(t,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return r.staticRenderFns})},wcpS:function(e,t,a){"use strict";a.r(t);var r=a("vAAY"),o=a("iMtA");for(var s in o)["default"].indexOf(s)<0&&function(e){a.d(t,e,function(){return o[e]})}(s);a("b8J4");var n=a("gp09"),i=Object(n.a)(o.default,r.render,r.staticRenderFns,!1,null,"16ad2b54",null);t.default=i.exports}}]);