(window.webpackJsonp=window.webpackJsonp||[]).push([[52],{"+eMl":function(t,e,a){},HyTu:function(t,e,a){"use strict";a("+eMl")},J3I5:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var s=i(a("Q9c5")),r=i(a("DWNM"));i(a("XRYr"));function i(t){return t&&t.__esModule?t:{default:t}}e.default={name:"countryOrderImport",mixins:[r.default],data:function(){return{pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,s=new Date(a.getTime()-6048e5).format("yyyy-MM-dd");t.$emit("pick",[s,e])}},{text:"最近一个月",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,s=new Date(a.getTime()-2592e6).format("yyyy-MM-dd");t.$emit("pick",[s,e])}},{text:"最近三个月",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,s=new Date(a.getTime()-7776e6).format("yyyy-MM-dd");t.$emit("pick",[s,e])}}]},adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1,drugSourceList:[],rLoading:{},warningData:[],tableHeight:100,dialogVisible:!1,dataList:[],params:{submitTime:"",hospitalId:"",source:"",orderStatus:"",page:1,limit:10,records:0,orderNum:"",warning:"",status:"",stockStatus:"",payStatus:""},orderData:{data:{},orderItem:[],hospital:{}},hospitalList:[],regionList:[{code:"",name:"全部"},{code:"440703",name:"蓬江区"},{code:"440704",name:"江海区"},{code:"440705",name:"新会区"},{code:"440781",name:"台山市"},{code:"440783",name:"开平市"},{code:"440784",name:"鹤山市"},{code:"440785",name:"恩平市"}]}},props:{},computed:{},watch:{},methods:{initRole:function(){var t=this.$store.getters.curUser.roleCode;-1!=t.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=t.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=t.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=t.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)},getDictItemName:function(t){for(var e=0;e<this.drugSourceList.length;e++)if(this.drugSourceList[e].value==t)return this.drugSourceList[e].label},getHospitalList:function(){var t=this,e=s.default.baseContext+"/supervise/supHospital/all";this.$http_post(e,{}).then(function(e){1==e.state?t.hospitalList=e.row:t.$alert(e.message)})},getWaringType:function(t){for(var e=0;e<this.warningData.length;e++)if(this.warningData[e].value==t)return this.warningData[e].label},getWaring:function(t){for(var e=[],a=t.split(","),s=0;s<a.length;s++){var r=this.getWaringType(a[s]);r&&e.push({name:r})}return e},getDictItem:function(t){var e=this,a=this.openLoading();this.$http_post(s.default.baseContext+"/supervise/supDictItem/getItemList",{code:t}).then(function(s){var r=s.rows;1==s.state?("SOURCE"==t&&(e.drugSourceList=r),"WARNING"==t&&(e.warningData=r),a.close()):(a.close(),e.$message.error(s.message))})},showOrder:function(t){this.$router.push({name:"conMaterialOrderDetail",query:{orderId:t.id}})},onSearch:function(t){"reset"==t?(this.params.orderNum="",this.params.warning="",this.params.submitTime="",this.params.source="",this.params.hospitalId="",this.params.regionId="",this.onQuery()):(this.params.page=1,this.onQuery())},onPageClick:function(t){this.params.page=t,this.onQuery()},onQuery:function(){var t=this,e=this.params,a=this.openLoading(),r=s.default.baseContext+"/supervise/conMaterialOrder/list";this.$http_post(r,e).then(function(e){1==e.state?(t.dataList=e.rows,t.params.records=e.records,a.close()):(a.close(),t.$alert(e.message))})},time:function(t,e){if(void 0==t.submitTime||""==t.submitTime)return"";var a=new Date(t.submitTime),s=a.getFullYear()+"-",r=(a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-",i=a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ";a.getHours(),a.getHours(),a.getMinutes(),a.getMinutes(),a.getSeconds(),a.getSeconds();return s+r+i},init:function(){}},mounted:function(){var t=this;this.$route.query.warning&&(this.params.warning=this.$route.query.warning),this.initRole(),this.onQuery(),this.getHospitalList(),this.getDictItem("WARNING"),this.getDictItem("SOURCE"),this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-180}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-180}},beforeDestroy:function(){window.onresize=null},filters:{formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}}}},o0Yz:function(t,e,a){"use strict";a.r(e);var s=a("J3I5"),r=a.n(s);for(var i in s)["default"].indexOf(i)<0&&function(t){a.d(e,t,function(){return s[t]})}(i);e.default=r.a},sJzp:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left"},[e("el-row",{attrs:{gutter:10}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("订单号")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入订单号"},model:{value:t.params.orderNum,callback:function(e){t.$set(t.params,"orderNum",e)},expression:"params.orderNum"}})],1)])]),this.adminRole||this.medicalAdmin||this.socialAdmin?e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("医疗机构")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:t.params.hospitalName,callback:function(e){t.$set(t.params,"hospitalName",e)},expression:"params.hospitalName"}},t._l(t.hospitalList,function(t){return e("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})}),1)],1)])]):t._e(),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("下单时间")]),e("div",{staticClass:"searchInput"},[e("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":t.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:t.params.submitTime,callback:function(e){t.$set(t.params,"submitTime",e)},expression:"params.submitTime"}})],1)])]),this.adminRole||this.medicalAdmin||this.socialAdmin?e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("区域划分")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",placeholder:"请选择区划"},model:{value:t.params.regionId,callback:function(e){t.$set(t.params,"regionId",e)},expression:"params.regionId"}},t._l(t.regionList,function(t){return e("el-option",{key:t.code,attrs:{label:t.name,value:t.code}})}),1)],1)])]):t._e()],1),e("el-row",{attrs:{gutter:10}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("配送状态")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",placeholder:"请选择配送状态"},model:{value:t.params.orderStatus,callback:function(e){t.$set(t.params,"orderStatus",e)},expression:"params.orderStatus"}},[e("el-option",{key:"",attrs:{label:"全部",value:""}}),e("el-option",{key:"0",attrs:{label:"待确认",value:"0"}}),e("el-option",{key:"1",attrs:{label:"待发货",value:"1"}}),e("el-option",{key:"2",attrs:{label:"部分发货",value:"2"}}),e("el-option",{key:"3",attrs:{label:"已发货",value:"3"}}),e("el-option",{key:"4",attrs:{label:"已完成",value:"4"}}),e("el-option",{key:"5",attrs:{label:"已取消",value:"5"}})],1)],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("入库状态")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",placeholder:"请选择入库状态"},model:{value:t.params.stockStatus,callback:function(e){t.$set(t.params,"stockStatus",e)},expression:"params.stockStatus"}},[e("el-option",{key:"",attrs:{label:"全部",value:""}}),e("el-option",{key:"0",attrs:{label:"未入库",value:"0"}}),e("el-option",{key:"1",attrs:{label:"已入库",value:"1"}}),e("el-option",{key:"2",attrs:{label:"部分入库",value:"2"}})],1)],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("支付状态")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",placeholder:"请选择支付状态"},model:{value:t.params.payStatus,callback:function(e){t.$set(t.params,"payStatus",e)},expression:"params.payStatus"}},[e("el-option",{key:"",attrs:{label:"全部",value:""}}),e("el-option",{key:"0",attrs:{label:"未支付",value:"0"}}),e("el-option",{key:"1",attrs:{label:"已支付",value:"1"}}),e("el-option",{key:"2",attrs:{label:"部分支付",value:"2"}})],1)],1)])])],1)],1),e("div",{staticClass:"right"},[e("div",[e("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1)])]),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.dataList,"highlight-current-row":"",height:t.tableHeight,border:""}},[e("el-table-column",{attrs:{prop:"orderNum",label:"订单号",width:"180"}}),e("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"180",align:"left"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",[t._v("\n                            "+t._s(t.getDictItemName(a.row.source))+"\n                        ")])]}}])}),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"200"}}),e("el-table-column",{attrs:{prop:"totalPrice",label:"订单总金额(元)",width:"200"}}),e("el-table-column",{attrs:{prop:"orderStatus",label:"订单状态"},scopedSlots:t._u([{key:"default",fn:function(a){return["0"==a.row.orderStatus?e("div",[e("el-tag",{attrs:{type:"info"}},[t._v("待确认")])],1):t._e(),"1"==a.row.orderStatus?e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("待发货")])],1):t._e(),"2"==a.row.orderStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分发货")])],1):t._e(),"3"==a.row.orderStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已发货")])],1):t._e(),"4"==a.row.orderStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已完成")])],1):t._e(),"5"==a.row.orderStatus?e("div",[e("el-tag",{attrs:{type:"info"}},[t._v("已取消")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"payStatus",label:"入库状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.stockStatus&&"0"!=a.row.stockStatus?t._e():e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("未入库")])],1),"1"==a.row.stockStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已入库")])],1):t._e(),"2"==a.row.stockStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分入库")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"payStatus",label:"支付状态"},scopedSlots:t._u([{key:"default",fn:function(a){return["0"!=a.row.payStatus&&a.row.payStatus||a.row.stockStatus&&"0"!=a.row.stockStatus?t._e():e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("未支付")])],1),"0"!=a.row.payStatus&&a.row.payStatus||!a.row.stockStatus||"0"==a.row.stockStatus?t._e():e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("未支付")])],1),"1"==a.row.payStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已支付")])],1):t._e(),"2"==a.row.payStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分支付")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"submitTime",formatter:t.time,label:"采购时间"}}),e("el-table-column",{attrs:{fixed:"right",label:"操作",align:"right",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showOrder(a.row)}}},[t._v("查看详情")])]}}])})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)])],1)},e.staticRenderFns=[]},tba2:function(t,e,a){"use strict";a.r(e);var s=a("tj37"),r=a("o0Yz");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,function(){return r[t]})}(i);a("HyTu");var l=a("gp09"),o=Object(l.a)(r.default,s.render,s.staticRenderFns,!1,null,"16cfd677",null);e.default=o.exports},tj37:function(t,e,a){"use strict";var s=a("sJzp");a.o(s,"render")&&a.d(e,"render",function(){return s.render}),a.o(s,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return s.staticRenderFns})}}]);