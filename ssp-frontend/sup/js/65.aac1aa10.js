(window.webpackJsonp=window.webpackJsonp||[]).push([[65],{"7eeB":function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"}),t("el-button",{attrs:{type:"info",icon:"el-icon-arrow-left"},on:{click:e.goBack}},[e._v("返回\n        ")])],1),t("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[t("el-tab-pane",{attrs:{label:"发票单信息",name:"first"}})],1),t("div",{staticClass:"form"},[t("el-form",{ref:"dataForm",staticClass:"item-form",attrs:{id:"invoiceData2",model:e.invoiceData,"label-position":"left","label-width":"110px"}},[t("div",{staticClass:"fromBox"},[t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"发票号"}},[t("el-input",{attrs:{value:e.invoiceData.data.no}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"医疗机构"}},[t("el-input",{attrs:{value:e.invoiceData.data.hospitalName}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"开票人"}},[t("el-input",{attrs:{value:e.invoiceData.data.invoiceName}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"配送企业"}},[t("el-input",{attrs:{value:e.invoiceData.data.deliveryName}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"开票时间"}},[t("el-input",{attrs:{value:e._f("formatTime")(e.invoiceData.data.invoiceDate)}})],1)],1)],1)],1)])],1),t("div",{staticClass:"memberTab"},[t("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.curTab,callback:function(t){e.curTab=t},expression:"curTab"}},[t("el-tab-pane",{attrs:{label:"发票明细信息"}},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticClass:"data_table",staticStyle:{width:"100%"},attrs:{data:e.invoiceData.invoiceItem,"header-cell-style":{background:"#f5f7fa"},border:"","highlight-current-row":""}},[e._e(),t("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单号"}}),t("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150"}}),t("el-table-column",{attrs:{prop:"approvalNumber",label:"批准文号",width:"150"}}),t("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"220"}}),t("el-table-column",{attrs:{prop:"num",label:"数量"}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"含税单价(元)"}}),t("el-table-column",{attrs:{prop:"taxesAmount",label:"含税金额(元)"}}),t("el-table-column",{attrs:{prop:"orderCode",label:"订单编号"}}),t("el-table-column",{attrs:{prop:"payTime",label:"回款时间"},scopedSlots:e._u([{key:"default",fn:function(o){return[t("span",[e._v(e._s(e._f("formatTime")(o.row.payTime)))])]}}])}),t("el-table-column",{attrs:{prop:"docInfo",label:"凭证信息"},scopedSlots:e._u([{key:"default",fn:function(o){return[t("div",{staticClass:"po-column el-button-group"},e._l(JSON.parse(o.row.docInfo||"[]"),function(o,a){return t("el-popover",{staticStyle:{"margin-left":"10px"},attrs:{placement:"left",title:"更多操作",width:150,trigger:"hover"}},[t("el-row",{staticStyle:{"margin-bottom":"5px"}},[e.isImageOrPdf(o.name)?t("el-col",{attrs:{span:12}},[t("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.onOnlineView(o.docId,o.name)}}},[e._v("预览")])],1):e._e(),t("el-col",{attrs:{span:12}},[t("el-button",{attrs:{type:"warning",size:"small"},on:{click:function(t){return e.downloadFile(o.docId,o.name)}}},[e._v("下载")])],1)],1),t("el-button",{attrs:{slot:"reference",type:"text",size:"small"},slot:"reference"},[e._v(" "+e._s(o.name))])],1)}),1)]}}])}),t("el-table-column",{attrs:{label:"状态",align:"right",width:"150"},scopedSlots:e._u([{key:"default",fn:function(o){return["1"!=o.row.payStatus?t("el-tag",{attrs:{type:"danger",size:"small"}},[e._v("未回款")]):e._e(),"1"==o.row.payStatus?t("el-tag",{attrs:{type:"success",size:"small"}},[e._v("已回款")]):e._e()]}}])}),t("el-table-column",{attrs:{prop:"remark",label:"备注","show-overflow-tooltip":"",width:"80"}})],1)],1)],1)],1),t("el-dialog",{attrs:{title:this.title,visible:e.show,width:"60%"},on:{close:e.close}},[t("div",{staticClass:"dialog-content"},[t("el-form",{ref:"invoiceVoucher",staticClass:"item-form",attrs:{id:"invoiceVoucher",model:e.invoiceVoucher,"label-width":"90px"}},[t("div",{staticClass:"fromBox"},[t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"支付金额",prop:"itemStock"}},[t("el-input",{attrs:{readonly:!0},model:{value:e.invoiceVoucher.payPrice,callback:function(t){e.$set(e.invoiceVoucher,"payPrice",e._n(t))},expression:"invoiceVoucher.payPrice"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"支付时间",prop:"stockTime"}},[t("el-date-picker",{attrs:{type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss",size:"small",placeholder:"请选择支付时间"},model:{value:e.invoiceVoucher.payTime,callback:function(t){e.$set(e.invoiceVoucher,"payTime",t)},expression:"invoiceVoucher.payTime"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"凭证上传"}},[t("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{headers:e.headers,action:e.uploadUrl,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"before-upload":e.beforeUpload,"before-remove":e.beforeRemove,"on-success":e.onSuccess,"on-error":e.onError,multiple:"",accept:".gif,.jpg,.jpeg,.bmp,.png,.doc,.docx,.txt,.xlsx,.xls,.pdf","file-list":e.fileList}},[t("el-button",{attrs:{size:"small"}},[e._v("点击上传")])],1)],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"备注",prop:"remark"}},[t("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.invoiceVoucher.remark,callback:function(t){e.$set(e.invoiceVoucher,"remark",t)},expression:"invoiceVoucher.remark"}})],1)],1)],1)],1)])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.close}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.saveInvoiceVoucher}},[e._v("确定")])],1)])],1)},t.staticRenderFns=[]},"8kUQ":function(e,t,o){"use strict";o("hOxI")},Tcu3:function(e,t,o){"use strict";o.r(t);var a=o("fWN1"),i=o.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){o.d(t,e,function(){return a[e]})}(r);t.default=i.a},fWN1:function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0});var a=n(o("omC7")),i=n(o("XRYr")),r=n(o("Q9c5")),l=n(o("DWNM"));function n(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[l.default],name:"invoice-detail",data:function(){return{headers:{},fileList:[],show:!1,title:"",invoiceVoucher:{invoiceId:"",deliveryItemId:"",id:"",payPrice:"",payTime:"",remark:"",docInfo:"",docIdList:""},previewUrl:r.default.baseContext+"/file",downloadUrl:r.default.baseContext+"/file/download",activeName:"first",curTab:"0",dialogVisible:!1,invoiceData:{data:{},invoiceItem:[]},listLoading:!1,tableHeight:100}},computed:{uploadUrl:function(){return r.default.baseContext+"/file/upload"}},components:{},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e),o=t.getFullYear()+"-",a=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",i=t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ";t.getHours(),t.getHours(),t.getMinutes(),t.getMinutes(),t.getSeconds(),t.getSeconds();return o+a+i}},watch:{},mounted:function(){var e=i.default.doCloundRequest(r.default.app_key,r.default.app_security,"");this.headers["x-aep-appkey"]=e["x-aep-appkey"],this.headers["x-aep-signature"]=e["x-aep-signature"],this.headers["x-aep-timestamp"]=e["x-aep-timestamp"],this.headers["x-aep-nonce"]=e["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,this.$route.query.invoiceCode&&this.getInvoiceDetail()},methods:{onSuccess:function(e,t,o){if(console.log(e),"1"!=e.state)return this.$message.error(e.message),this.$refs.upload.clearFiles(),!1;t.docId=e.row.id;var a={docId:e.row.id,name:e.row.name};this.invoiceVoucher.docIdList.push(a)},onError:function(e,t,o){this.rLoading.close(),console.log(e),this.$message.error("上传失败,请重试")},handlePreview:function(e){console.log(e);var t=this.downloadUrl+"?docId="+encodeURI(encodeURI(e.docId))+"&fileName="+e.name,o=document.createElement("a");o.href=t,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(t)},beforeUpload:function(e){return!(e.size/1024/1024>10)||(this.$message.error("上传文件过大，请分开上传，单个文件最大为10M。"),!1)},beforeRemove:function(e,t){return!this.beforeUpload(e)||this.$confirm("确定移除 "+e.name+"？")},handleRemove:function(e,t){var o=this;this.invoiceVoucher.docIdList.some(function(t,a){if(t.docId==e.docId)return o.invoiceVoucher.docIdList.splice(a,1),!0}),console.log(e.docId,e,t)},saveInvoiceVoucher:function(){var e=this;if(!this.invoiceVoucher.docIdList||0==this.invoiceVoucher.docIdList.length)return this.$message.error("请上传支付凭证。"),!1;this.$refs.invoiceVoucher.validate(function(t){if(t){e.invoiceVoucher.docInfo=(0,a.default)(e.invoiceVoucher.docIdList);var o=e.openLoading("提交中...");e.$http_post(r.default.baseContext+"/supervise/supInvoiceItem/savePayVoucher",e.invoiceVoucher,!0).then(function(t){1==t.state?(e.$message.success("提交成功"),e.getInvoiceDetail(),e.show=!1,o.close()):(e.$message.error(t.message),o.close())})}})},close:function(){this.show=!1},uploadVoucher:function(e){var t=this;this.invoiceVoucher={invoiceId:e.invoiceId,deliveryItemId:e.deliveryItemId,id:e.id,payTime:"",payPrice:e.taxesAmount,remark:"",docInfo:"",docIdList:[]},this.title="上传支付凭证",this.fileList=[],this.$nextTick(function(){t.show=!0})},isImageOrPdf:function(e){var t=e.lastIndexOf("."),o=e.substr(t+1);return-1!==["png","jpg","jpeg","bmp","gif","webp","psd","svg","tiff","pdf"].indexOf(o.toLowerCase())},onOnlineView:function(e,t){window.open(this.previewUrl+"/previewByDocId?docId="+encodeURI(encodeURI(e))+"&fileName="+t)},downloadFile:function(e,t){var o=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,a=document.createElement("a");a.href=o,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(o)},goBack:function(){this.$router.go(-1)},handleClick:function(e,t){this.curTab=e.index},getInvoiceDetail:function(){var e=this,t=this.openLoading("查询中"),o=r.default.baseContext+"/supervise/supInvoice/show/"+this.$route.query.invoiceCode;this.$http_post(o,{}).then(function(o){1==o.state?(e.invoiceData=o.row,t.close()):(t.close(),e.$alert(o.message))})}}}},hOxI:function(e,t,o){},hoVU:function(e,t,o){"use strict";o.r(t);var a=o("qGRQ"),i=o("Tcu3");for(var r in i)["default"].indexOf(r)<0&&function(e){o.d(t,e,function(){return i[e]})}(r);o("8kUQ");var l=o("gp09"),n=Object(l.a)(i.default,a.render,a.staticRenderFns,!1,null,"e6db7504",null);t.default=n.exports},qGRQ:function(e,t,o){"use strict";var a=o("7eeB");o.o(a,"render")&&o.d(t,"render",function(){return a.render}),o.o(a,"staticRenderFns")&&o.d(t,"staticRenderFns",function(){return a.staticRenderFns})}}]);