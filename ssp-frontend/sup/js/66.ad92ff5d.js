(window.webpackJsonp=window.webpackJsonp||[]).push([[66],{"/lPw":function(e,t,a){"use strict";a.r(t);var s=a("jhQN"),i=a.n(s);for(var l in s)["default"].indexOf(l)<0&&function(e){a.d(t,e,function(){return s[e]})}(l);t.default=i.a},"3H1E":function(e,t,a){"use strict";a("nXLd")},"70lI":function(e,t,a){"use strict";var s=a("xv+4");a.o(s,"render")&&a.d(t,"render",function(){return s.render}),a.o(s,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return s.staticRenderFns})},fg2Y:function(e,t,a){"use strict";a.r(t);var s=a("70lI"),i=a("/lPw");for(var l in i)["default"].indexOf(l)<0&&function(e){a.d(t,e,function(){return i[e]})}(l);a("3H1E");var o=a("gp09"),r=Object(o.a)(i.default,s.render,s.staticRenderFns,!1,null,"7b540b48",null);t.default=r.exports},jhQN:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var s=o(a("Q9c5")),i=o(a("DWNM")),l=o(a("XRYr"));function o(e){return e&&e.__esModule?e:{default:e}}t.default={name:"invoice-list",mixins:[i.default],data:function(){return{adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1,rLoading:{},fileList:[],headers:{},uploadData:{},importDialogVisible:!1,warningData:[],tableHeight:100,dialogVisible:!1,dataList:[],hospitalList:[],params:{page:1,limit:10,records:0,deliveryName:"",code:"",invoiceCode:"",deliveryCode:"",hospitalName:"",invoiceTime:"",country:"",status:"",payType:""},dateChangeValue:0}},props:{},computed:{uploadUrl:function(){return s.default.baseContext+"/file/upload"}},watch:{},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e),a=t.getFullYear()+"-",s=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",i=t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ";t.getHours(),t.getHours(),t.getMinutes(),t.getMinutes(),t.getSeconds(),t.getSeconds();return a+s+i}},methods:{setDate:function(){var e=(new Date).format("yyyy-MM-dd"),t=new Date,a=new Date(t.getTime()-2592e6).format("yyyy-MM-dd");this.params.invoiceTime=[a,e]},initRole:function(){var e=this.$store.getters.curUser.roleCode;-1!=e.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=e.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=e.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=e.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)},cancel:function(){this.importDialogVisible=!1},downloadTemplate:function(){var e=s.default.baseContext+"/file/downloadInvoiceImportTemplate",t=document.createElement("a");t.href=e,document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(e)},submitUpload:function(){var e=this;this.rLoading=this.openLoading("导入中,请耐心等待...",{timeout:1e5});var t=this.uploadData.docId;this.$http_post(s.default.baseContext+"/supervise/supInvoice/importCountryInvoice?docId="+t,{},null,{timeout:1e5}).then(function(t){1==t.state?(e.onQuery(),e.rLoading.close(),e.importDialogVisible=!1,e.$alert("导入成功，请前往【发票单批次日志】查看导入结果")):(e.onQuery(),e.rLoading.close(),e.importDialogVisible=!1,e.$message.error(t.message))})},onSuccess:function(e,t,a){if("1"!=e.state)return this.$refs.upload.clearFiles(),this.$alert(e.message),!1;t.docId=e.row.id,this.uploadData={docId:e.row.id,name:e.row.name}},onError:function(e,t,a){this.rLoading.close(),console.log(e),this.$message.error("上传失败,请重试")},handlePreview:function(e){var t=this.downloadUrl+"?docId="+encodeURI(encodeURI(e.docId))+"&fileName="+e.name;console.log(e);var a=document.createElement("a");a.href=t,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(t)},handleExceed:function(e,t){this.$message.warning("一次只能上传一个Excel文件")},beforeUpload:function(e){if(e.size/1024/1024>20)return this.$message.error("上传文件过大，请分开上传，单个文件最大为20M。"),!1;var t=e.name;return"xlsx"==t.substring(t.length-4)||"xls"==t.substring(t.length-3)||(this.$message.error("上传文件格式只能为【.xlsx】或【.xls】"),!1)},beforeRemove:function(e,t){return!this.beforeUpload(e)||this.$confirm("确定移除 "+e.name+"？")},handleRemove:function(e,t){return this.uploadData={},console.log(e.docId,e,t),!0},invoiceImport:function(){this.importDialogVisible=!0},showDetail:function(e){this.$router.push({name:"invoiceDetail",query:{invoiceCode:e.itemNo}})},onSearch:function(e){"reset"==e?(this.params.code="",this.params.hospitalName="",this.params.deliveryName="",this.params.deliveryCode="",this.params.invoiceTime="",this.params.page=1,this.dateChangeValue=0,this.onQuery()):(this.params.page=1,this.dateChangeValue=1,this.onQuery())},onPageClick:function(e){this.params.page=e,this.onQuery()},getHospitalList:function(){var e=this,t=s.default.baseContext+"/supervise/supHospital/all";this.$http_post(t,{}).then(function(t){1==t.state?e.hospitalList=t.row:e.$alert(t.message)})},onQuery:function(){var e=this;0!=this.dateChangeValue&&""!=this.dateChangeValue||this.setDate();var t=this.params,a=this.openLoading(),i=s.default.baseContext+"/supervise/supInvoiceItem/getInvoiceItemListNew";this.$http_post(i,t).then(function(t){1==t.state?(e.dataList=t.rows,e.params.records=t.records,a.close()):(a.close(),e.$alert(t.message))})}},mounted:function(){var e=this,t=l.default.doCloundRequest(s.default.app_key,s.default.app_security,"");this.headers["x-aep-appkey"]=t["x-aep-appkey"],this.headers["x-aep-signature"]=t["x-aep-signature"],this.headers["x-aep-timestamp"]=t["x-aep-timestamp"],this.headers["x-aep-nonce"]=t["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,this.initRole(),this.onQuery(),this.getHospitalList(),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-150}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-150}},beforeDestroy:function(){window.onresize=null}}},nXLd:function(e,t,a){},"xv+4":function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.staticRenderFns=t.render=void 0;var s=function(e){return e&&e.__esModule?e:{default:e}}(a("/umX"));t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left"},[t("el-row",{attrs:{gutter:10}},[t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("发票号")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入发票号"},model:{value:e.params.code,callback:function(t){e.$set(e.params,"code",t)},expression:"params.code"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("发票代码")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入发票代码"},model:{value:e.params.invoiceCode,callback:function(t){e.$set(e.params,"invoiceCode",t)},expression:"params.invoiceCode"}})],1)])]),this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("医疗机构")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:e.params.hospitalName,callback:function(t){e.$set(e.params,"hospitalName",t)},expression:"params.hospitalName"}},e._l(e.hospitalList,function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}),1)],1)])]):e._e(),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("配送企业")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入配送企业名称"},model:{value:e.params.deliveryName,callback:function(t){e.$set(e.params,"deliveryName",t)},expression:"params.deliveryName"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("开票时间")]),t("div",{staticClass:"searchInput"},[t("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:e.params.invoiceTime,callback:function(t){e.$set(e.params,"invoiceTime",t)},expression:"params.invoiceTime"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("是否集采")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择集采状态"},model:{value:e.params.country,callback:function(t){e.$set(e.params,"country",t)},expression:"params.country"}},[t("el-option",{attrs:{label:"是",value:"1"}}),t("el-option",{attrs:{label:"否",value:"0"}})],1)],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("回款状态")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择回款状态"},model:{value:e.params.payStatus,callback:function(t){e.$set(e.params,"payStatus",t)},expression:"params.payStatus"}},[t("el-option",{attrs:{label:"未回款",value:"0"}}),t("el-option",{attrs:{label:"已回款",value:"1"}})],1)],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("回款方式")]),t("div",{staticClass:"searchInput"},[t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择回款方式"},model:{value:e.params.payType,callback:function(t){e.$set(e.params,"payType",t)},expression:"params.payType"}},[t("el-option",{attrs:{label:"自动回款",value:"0"}}),t("el-option",{attrs:{label:"平台确认",value:"1"}})],1)],1)])])],1)],1),t("div",{staticClass:"right"},[t("div",{staticStyle:{"margin-bottom":"5px"}},[t("el-button",{attrs:{size:"small",type:"warning",icon:"el-icon-download"},on:{click:e.invoiceImport}},[e._v("导入发票")])],1),t("div",[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1)])]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.dataList,"highlight-current-row":"",height:e.tableHeight,border:""}},[t("el-table-column",{attrs:{prop:"code",label:"发票代码"}}),t("el-table-column",{attrs:{prop:"itemNo",label:"发票号"}}),t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构"}}),t("el-table-column",{attrs:{prop:"deliveryName",label:"配送企业"}}),t("el-table-column",{attrs:{prop:"status",label:"回款状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return["1"==a.row.payStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已回款")])],1):e._e(),"2"==a.row.payStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分回款")])],1):e._e(),"0"!=a.row.payStatus&&a.row.payStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未回款")])],1)]}}])}),t("el-table-column",{attrs:{prop:"invoiceDate",label:"开票时间"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e._f("formatTime")(a.row.invoiceDate)))])]}}])}),t("el-table-column",{attrs:{prop:"invoiceRemark",label:"备注"}}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"200"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showDetail(a.row)}}},[e._v("查看详情")])]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)]),t("el-dialog",{staticClass:"upload-box",attrs:{title:"药品发票明细单导入",visible:e.importDialogVisible,"append-to-body":"",width:"40%"},on:{"update:visible":function(t){e.importDialogVisible=t}}},[t("el-form",{ref:"importDataForm",attrs:{"label-position":"left",model:e.uploadData}},[t("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:(0,s.default)({drag:"",limit:1,headers:e.headers,action:e.uploadUrl,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"on-exceed":e.handleExceed,"before-upload":e.beforeUpload,"before-remove":e.beforeRemove,"on-success":e.onSuccess,"on-error":e.onError,multiple:"",accept:".xls,.xlsx","file-list":e.fileList},"multiple",!1)},[t("i",{staticClass:"el-icon-upload"}),t("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),t("em",[e._v("点击上传")])]),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("只能上传【 .xlsx / .xls 】文件")])])],1),t("span",{staticStyle:{color:"red"}},[e._v("提示：请下载 Excel 模板，按照格式进行导入！")]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitUpload("")}}},[e._v("确 定")]),t("el-button",{attrs:{type:"warning"},on:{click:function(t){return e.downloadTemplate("")}}},[e._v("下载模板")]),t("el-button",{on:{click:e.cancel}},[e._v("取消")])],1)],1)],1)},t.staticRenderFns=[]}}]);