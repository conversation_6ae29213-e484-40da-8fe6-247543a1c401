(function(doc, win, width) {
	var docEl = doc.documentElement;
	var head = doc.getElementsByTagName("head")[0];
	var hFirst = head.firstChild || head.firstElementChild;
	var cssEl = doc.createElement('style');
	//手机横屏，竖屏可在此处设置 
	var resizeEvt = 'orientationchange' in win ? 'orientationchange' : 'resize';
	var recalc = function() {
		var x;
		var n = win.devicePixelRatio;
		if (n >= 3) {
			x = 3;
		} else if (n >= 2) {
			x = 2;
		} else {
			x = 1;
		}
		var clientWidth = docEl.clientWidth;
		var clientHeight = docEl.clientHeight;
		if (!clientWidth) return;
		if(!clientHeight) return;
		//var pxPerRem = 100 * (clientWidth / width);
		var pxPerRem = 100 * (clientWidth / width)
		cssEl.innerHTML = 'html{ font-size:' + pxPerRem + 'px!important; }';
		docEl.setAttribute("data-dpr", x); //x 限制范围取值 1,2,3
		head.appendChild(cssEl);
	};
	if (!doc.addEventListener) return;
	win.addEventListener(resizeEvt, recalc, false);
    doc.addEventListener('DOMContentLoaded', recalc, false);
    recalc();
})(document, window, 1920)