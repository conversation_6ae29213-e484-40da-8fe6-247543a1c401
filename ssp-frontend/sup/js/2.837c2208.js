(window.webpackJsonp=window.webpackJsonp||[]).push([[2,68,83],{"5sBe":function(e,t,r){"use strict";var o=r("OcOx");r.o(o,"render")&&r.d(t,"render",function(){return o.render}),r.o(o,"staticRenderFns")&&r.d(t,"staticRenderFns",function(){return o.staticRenderFns})},"7ALK":function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var o=d(r("tNMj")),n=d(r("8h+H")),a=d(r("pU08")),i=d(r("OmRK")),s=d(r("DWNM")),l=d(r("Q9c5"));function d(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[s.default],name:"itemDetailList",data:function(){return{warningData:[],drugSourceList:[],listLoading:!1,orderItemId:"",orderId:"",amount:1,dialogVisible:!1}},props:{orderItem:{type:Array,default:function(){}},operateHide:{type:Boolean,default:function(){return!1}},recommend:{type:Boolean,default:function(){return!1}}},mounted:function(){this.getDictItem("SOURCE"),this.getDictItem("WARNING")},components:{stockVoucher:o.default,deliveryItemList:n.default,invoiceItem:a.default,itemDetail:i.default},methods:{getWaringType:function(e){for(var t=0;t<this.warningData.length;t++)if(this.warningData[t].value==e)return this.warningData[t].label},getWaring:function(e){for(var t=[],r=e.split(","),o=0;o<r.length;o++){var n=this.getWaringType(r[o]);n&&t.push({name:n})}return t},showDeliveryAndInvoice:function(e){this.orderId=e.orderId,this.orderItemId=e.id,this.dialogVisible=!0},getPrice:function(e,t){if(e){var r=e.match(/\d+/g);return t?(t/r.join("")).toFixed(3):""}return""},getDictItemName:function(e){for(var t=0;t<this.drugSourceList.length;t++)if(this.drugSourceList[t].value==e)return this.drugSourceList[t].label},getDictItem:function(e){var t=this,r=this.openLoading();this.$http_post(l.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(o){var n=o.rows;1==o.state?("SOURCE"==e&&(t.drugSourceList=n),"WARNING"==e&&(t.warningData=n),r.close()):(r.close(),t.$message.error(o.message))})},close:function(){this.$refs.asyncDialog.dialogVisible=!1},showVoucher:function(e){this.amount=e.amount,this.orderItemId=e.id,this.$refs.asyncDialog.dialogVisible=!0}}}},8713:function(e,t,r){"use strict";r("e3HG")},"8h+H":function(e,t,r){"use strict";r.r(t);var o=r("duH7"),n=r("w6aq");for(var a in n)["default"].indexOf(a)<0&&function(e){r.d(t,e,function(){return n[e]})}(a);r("bX9j");var i=r("gp09"),s=Object(i.a)(n.default,o.render,o.staticRenderFns,!1,null,"705fd030",null);t.default=s.exports},AQM8:function(e,t,r){"use strict";r.r(t);var o=r("7ALK"),n=r.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){r.d(t,e,function(){return o[e]})}(a);t.default=n.a},AvpF:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var o=l(r("/umX")),n=l(r("omC7")),a=l(r("XRYr")),i=l(r("Q9c5")),s=l(r("DWNM"));function l(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[s.default],name:"stockVoucher-list",data:function(){return{stockStatus:"",title:"",orderStock:{id:"",orderId:"",stockTime:"",orderItemId:"",itemStock:0,docIdList:[],remark:"",docInfo:"",sourceId:""},headers:{},rules:{itemStock:[{validator:function(e,t,r){0==/(^[1-9]\d*$)/.test(t)?r(new Error("请输入大于零的数")):r()},trigger:"change"},{validator:function(e,t,r){0==/(^[1-9]\d*$)/.test(t)?r(new Error("请输入大于零的数")):r()},trigger:"blur"},{type:"number",required:!0,message:"入库数不能为空",trigger:"blur"}]},show:!1,dialogVisible:!1,downloadUrl:i.default.baseContext+"/file/download",orderStocks:[],params:{page:1,limit:10,records:0,orderItemId:""},fileList:[]}},computed:{uploadUrl:function(){return i.default.baseContext+"/file/upload"}},props:{orderItemId:{type:String,default:""},amount:{type:Number,default:""},sourceId:{type:String,default:""}},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ")+(t.getHours()<10?"0"+t.getHours()+":":t.getHours()+":")+(t.getMinutes()<10?"0"+t.getMinutes()+":":t.getMinutes()+":")+(t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds())}},watch:{orderItemId:function(e){e&&this.queryOrderStocks(),console.log(this.$route.name)}},mounted:function(){var e=a.default.doCloundRequest(i.default.app_key,i.default.app_security,"");this.headers["x-aep-appkey"]=e["x-aep-appkey"],this.headers["x-aep-signature"]=e["x-aep-signature"],this.headers["x-aep-timestamp"]=e["x-aep-timestamp"],this.headers["x-aep-nonce"]=e["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,this.orderItemId&&this.queryOrderStocks()},methods:(0,o.default)({onSuccess:function(e,t,r){if(console.log(e),"1"!=e.state)return this.$message.error(e.message),this.$refs.upload.clearFiles(),!1;t.docId=e.row.id;var o={docId:e.row.id,name:e.row.name};this.orderStock.docIdList.push(o)},onError:function(e,t,r){this.rLoading.close(),console.log(e),this.$message.error("上传失败,请重试")},handlePreview:function(e){var t=this.downloadUrl+"?docId="+encodeURI(encodeURI(e.docId))+"&fileName="+e.name;console.log(e);var r=document.createElement("a");r.href=t,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(t)},beforeUpload:function(e){return!(e.size/1024/1024>10)||(this.$message.error("上传文件过大，请分开上传，单个文件最大为10M。"),!1)},beforeRemove:function(e,t){return!this.beforeUpload(e)||this.$confirm("确定移除 "+e.name+"？")},handleRemove:function(e,t){var r=this;this.orderStock.docIdList.some(function(t,o){if(t.docId==e.docId)return r.orderStock.docIdList.splice(o,1),!0}),console.log(e.docId,e,t)},saveOrderStock:function(){var e=this;if(!this.orderStock.docIdList||0==this.orderStock.docIdList.length)return this.$message.error("请上传入库凭证。"),!1;this.$refs.orderStock.validate(function(t){if(t){e.orderStock.docInfo=(0,n.default)(e.orderStock.docIdList);var r=e.openLoading("提交中...");e.$http_post(i.default.baseContext+"/supervise/supOrderStock/save",e.orderStock,!0).then(function(t){1==t.state?(e.$message.success("提交成功"),e.queryOrderStocks(),e.show=!1,r.close()):(e.$message.error(t.message),r.close())})}})},deleteOrderStock:function(e){var t=this;this.$confirm("确定要删除该记录吗").then(function(){var r=t.openLoading("");t.$http_post(i.default.baseContext+"/supervise/supOrderStock/delete/"+e.id,{orderId:t.$route.query.orderId,sourceId:t.sourceId}).then(function(e){1==e.state?(t.$message.success("删除成功"),t.queryOrderStocks(),r.close()):(t.$alert(e.message),r.close())})})},editOrderStock:function(e){var t=this;console.log(e),this.orderStock={id:e.id,sourceId:this.sourceId,orderId:this.$route.query.orderId,itemStock:e.itemStock,stockTime:e.stockTime,orderItemId:e.orderItemId,remark:e.remark,docInfo:e.docInfo,docIdList:JSON.parse(e.docInfo)},this.fileList=JSON.parse(e.docInfo),this.title="编辑入库凭证",this.$nextTick(function(){t.show=!0})},downloadFile:function(e,t){var r=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,o=document.createElement("a");o.href=r,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(r)},close:function(){this.show=!1},onPageClick:function(e){this.params.page=e,this.queryOrderStocks()},uploadVoucher:function(){var e=this,t=this.$route.query.orderId;this.orderStock={id:"",stockTime:"",sourceId:this.sourceId,orderId:t,itemStock:this.amount,orderItemId:this.orderItemId,remark:"",docInfo:"",docIdList:[]},this.title="上传入库凭证",this.fileList=[],this.$nextTick(function(){e.show=!0})},queryOrderStocks:function(){var e=this;this.params.orderId=this.$route.query.orderId,this.params.orderItemId=this.orderItemId;var t=this.openLoading("");this.$http_post(i.default.baseContext+"/supervise/supOrderStock/all",this.params).then(function(r){t.close(),1==r.state?(e.stockStatus=r.message,e.orderStocks=r.rows,e.params.records=r.records):e.$alert(r.message)})}},"downloadFile",function(e,t){var r=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,o=document.createElement("a");o.href=r,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(r)})}},CFHU:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var o=a(r("Q9c5")),n=a(r("DWNM"));function a(e){return e&&e.__esModule?e:{default:e}}t.default={name:"invoiceItem-list",mixins:[n.default],data:function(){return{invoiceItemDataList:[],invoiceParams:{deliveryItemId:"",orderItemId:"",page:1,limit:10,records:0},previewUrl:o.default.baseContext+"/file",downloadUrl:o.default.baseContext+"/file/download",tableHeight:350}},props:{deliveryItemId:{type:String,default:"#"},orderItemId:{type:String,default:"#"}},watch:{deliveryItemId:function(e){e&&this.onInvoiceQuery()},orderItemId:function(e){e&&this.onInvoiceQuery()}},mounted:function(){this.onInvoiceQuery()},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e),r=t.getFullYear()+"-",o=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",n=t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ";t.getHours(),t.getHours(),t.getMinutes(),t.getMinutes(),t.getSeconds(),t.getSeconds();return r+o+n}},methods:{isImageOrPdf:function(e){var t=e.lastIndexOf("."),r=e.substr(t+1);return-1!==["png","jpg","jpeg","bmp","gif","webp","psd","svg","tiff","pdf"].indexOf(r.toLowerCase())},onOnlineView:function(e,t){window.open(this.previewUrl+"/previewByDocId?docId="+encodeURI(encodeURI(e))+"&fileName="+t)},downloadFile:function(e,t){var r=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,o=document.createElement("a");o.href=r,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(r)},onInvoicePageClick:function(e){this.invoiceParams.page=e,this.onInvoiceQuery()},onInvoiceQuery:function(){var e=this;this.invoiceParams.deliveryItemId=this.deliveryItemId,this.invoiceParams.orderItemId=this.orderItemId;var t=this.invoiceParams,r=this.openLoading(),n=o.default.baseContext+"/supervise/supInvoiceItem/getInvoiceItem";this.$http_post(n,t).then(function(t){1==t.state?(e.invoiceItemDataList=t.rows,e.invoiceParams.records=t.records,r.close()):(r.close(),e.$alert(t.message))})}}}},FYSU:function(e,t,r){},GTtU:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var o=i(r("tNMj")),n=i(r("DWNM")),a=i(r("Q9c5"));function i(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[n.default],name:"itemDetailList",data:function(){return{warningData:[],drugSourceList:[],listLoading:!1,orderItemId:"",orderId:"",amount:1,dialogVisible:!1,itemDetailList:[]}},props:{orderItem:{type:Array,default:function(){}},operateHide:{type:Boolean,default:function(){return!1}},recommend:{type:Boolean,default:function(){return!1}},orderItemId:{type:String,default:"#"}},computed:{goPath:{get:function(){if("#"!=this.orderItemId)return"/supervise/supOrderItem/list"},set:function(e){}}},watch:{orderItemId:function(e){e&&this.onQuery()}},mounted:function(){this.getDictItem("SOURCE"),this.getDictItem("WARNING"),this.onQuery()},components:{stockVoucher:o.default},methods:{getWaringType:function(e){for(var t=0;t<this.warningData.length;t++)if(this.warningData[t].value==e)return this.warningData[t].label},getWaring:function(e){for(var t=[],r=e.split(","),o=0;o<r.length;o++){var n=this.getWaringType(r[o]);n&&t.push({name:n})}return t},getPrice:function(e,t){if(e){var r=e.match(/\d+/g);return t?(t/r.join("")).toFixed(3):""}return""},getDictItemName:function(e){for(var t=0;t<this.drugSourceList.length;t++)if(this.drugSourceList[t].value==e)return this.drugSourceList[t].label},getDictItem:function(e){var t=this,r=this.openLoading();this.$http_post(a.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(o){var n=o.rows;1==o.state?("SOURCE"==e&&(t.drugSourceList=n),"WARNING"==e&&(t.warningData=n),r.close()):(r.close(),t.$message.error(o.message))})},onQuery:function(){var e=this,t={orderItemId:"#"};"#"!=this.orderItemId&&(t={page:1,limit:10,orderItemId:this.orderItemId});var r=this.openLoading(),o=a.default.baseContext+"/supervise/supOrderItem/list";this.$http_post(o,t).then(function(t){if(1==t.state){var o=t.rows;e.itemDetailList=o,r.close()}else r.close(),e.$alert(t.message)})},close:function(){this.$refs.asyncDialog.dialogVisible=!1}}}},I7mx:function(e,t,r){},Len8:function(e,t,r){"use strict";r("I7mx")},MaVJ:function(e,t,r){"use strict";r("FYSU")},OcOx:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"}),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.invoiceItemDataList,"highlight-current-row":"",height:e.tableHeight}},[t("el-table-column",{attrs:{prop:"itemNo",label:"发票号",width:"100px"}}),t("el-table-column",{attrs:{prop:"invoiceItemNo",label:"发票明细号",width:"150px"}}),t("el-table-column",{attrs:{prop:"num",label:"数量"}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"单价"}}),t("el-table-column",{attrs:{prop:"taxesAmount",label:"金额"}}),t("el-table-column",{attrs:{prop:"orderCode",label:"订单编号",width:"120px"}}),t("el-table-column",{attrs:{prop:"invoiceDate",label:"开票时间",width:"120px","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s(e._f("formatTime")(r.row.invoiceDate)))])]}}])}),t("el-table-column",{attrs:{prop:"payStatus",label:"回款状态"},scopedSlots:e._u([{key:"default",fn:function(r){return["1"==r.row.payStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已回款")])],1):e._e(),"2"==r.row.payStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分回款")])],1):e._e(),"0"!=r.row.payStatus&&r.row.payStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未回款")])],1)]}}])}),"0"!=e.$route.query.payStatus?t("el-table-column",{attrs:{prop:"docInfo",label:"凭证信息",width:"250px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",{staticClass:"po-column el-button-group"},e._l(JSON.parse(r.row.docInfo||"[]"),function(r,o){return t("el-popover",{staticStyle:{"margin-left":"10px"},attrs:{placement:"left",title:"更多操作",width:e.isImageOrPdf(r.name)?150:80,trigger:"hover"}},[t("el-row",{staticStyle:{"margin-bottom":"5px"}},[e.isImageOrPdf(r.name)?t("el-col",{attrs:{span:12}},[t("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.onOnlineView(r.docId,r.name)}}},[e._v("预览")])],1):e._e(),t("el-col",{attrs:{span:12}},[t("el-button",{attrs:{type:"warning",size:"small"},on:{click:function(t){return e.downloadFile(r.docId,r.name)}}},[e._v("下载")])],1)],1),t("el-button",{attrs:{slot:"reference",type:"text",size:"small"},slot:"reference"},[e._v(" "+e._s(r.name))])],1)}),1)]}}],null,!1,3285525396)}):e._e(),t("el-table-column",{attrs:{prop:"remark",label:"备注","show-overflow-tooltip":""}})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.invoiceParams.records,"page-size":e.invoiceParams.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onInvoicePageClick}})],1)],1)])],1)},t.staticRenderFns=[]},OmRK:function(e,t,r){"use strict";r.r(t);var o=r("RHiA"),n=r("igvt");for(var a in n)["default"].indexOf(a)<0&&function(e){r.d(t,e,function(){return n[e]})}(a);r("Len8");var i=r("gp09"),s=Object(i.a)(n.default,o.render,o.staticRenderFns,!1,null,"6877f124",null);t.default=s.exports},RHiA:function(e,t,r){"use strict";var o=r("YA8J");r.o(o,"render")&&r.d(t,"render",function(){return o.render}),r.o(o,"staticRenderFns")&&r.d(t,"staticRenderFns",function(){return o.staticRenderFns})},S2an:function(e,t,r){"use strict";r("Th+o")},"Th+o":function(e,t,r){},YA8J:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"item-item"},[t("div",{staticClass:"flex-row-default"},[t("div",{staticClass:"box-card box-right"},[t("div",{staticClass:"text item"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticClass:"data_table",staticStyle:{width:"100%"},attrs:{data:e.itemDetailList,border:"","highlight-current-row":""}},[e._e(),t("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150"}}),t("el-table-column",{attrs:{prop:"specs",label:"规格"}}),t("el-table-column",{attrs:{prop:"packingSpecs",label:"包装规格"}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"最小单位价(元)",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(e.getPrice(t.row.packingSpecs,t.row.unitPrice))+"\n                        ")]}}])}),t("el-table-column",{attrs:{prop:"unitPrice",label:"商品价(元)"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(t.row.unitPrice)+"\n                        ")]}}])}),t("el-table-column",{attrs:{prop:"amount",label:"采购数量"}}),t("el-table-column",{attrs:{prop:"itemPrice",label:"合计"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(t.row.itemPrice+"元")+"\n                        ")]}}])}),t("el-table-column",{attrs:{prop:"systemContrast",label:"系统推荐",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return["1"==r.row.systemContrast?t("span",[e._v("是")]):e._e(),"0"==r.row.systemContrast?t("div",[t("el-popover",{attrs:{placement:"top-start",width:"250",trigger:"hover"}},[r.row.reason?t("div",{staticClass:"text item"},[e._v("\n                                 "+e._s(r.row.reason)+"\n                                ")]):t("div",{staticClass:"text item"},[e._v("\n                                  无\n                                ")]),t("el-tag",{attrs:{slot:"reference",type:"danger"},slot:"reference"},[e._v("否(查看原因)")])],1)],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"countryTag",label:"是否国家集采",width:"110",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return["1"==r.row.countryTag?t("div",[t("el-tag",{attrs:{type:"primary"}},[e._v("是")])],1):e._e(),"1"!=r.row.countryTag?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("否")])],1):e._e()]}}])}),e.recommend?e._e():t("el-table-column",{attrs:{prop:"ORDER_ITEM_STATUS",label:"发货状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return["0"==r.row.orderItemStatus?t("div",[t("el-tag",{attrs:{type:"info"}},[e._v("待确认")])],1):e._e(),"1"==r.row.orderItemStatus?t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("待发货")])],1):e._e(),"2"==r.row.orderItemStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分发货")])],1):e._e(),"3"==r.row.orderItemStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已发货")])],1):e._e(),"4"==r.row.orderItemStatus?t("div",[t("el-tag",{attrs:{type:"primary"}},[e._v("已完成")])],1):e._e(),r.row.orderItemStatus&&"6"!=r.row.orderItemStatus?e._e():t("div",[t("el-tag",{attrs:{type:"info"}},[e._v("无")])],1)]}}],null,!1,3071207368)}),e.recommend?e._e():t("el-table-column",{attrs:{prop:"stockStatus",label:"入库状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.stockStatus&&"0"!=r.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未入库")])],1),"1"==r.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已入库")])],1):e._e(),"2"==r.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分入库")])],1):e._e()]}}],null,!1,1922240075)}),e.recommend?e._e():t("el-table-column",{attrs:{prop:"payStatus",label:"支付状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return["1"==r.row.payStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已支付")])],1):e._e(),"2"==r.row.payStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分支付")])],1):e._e(),"0"!=r.row.payStatus&&r.row.payStatus||r.row.stockStatus&&"0"!=r.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("未支付")])],1),"0"!=r.row.payStatus&&r.row.payStatus||!r.row.stockStatus||"0"==r.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未支付")])],1)]}}],null,!1,984211271)})],1)],1)])])])},t.staticRenderFns=[]},ZzA3:function(e,t,r){},bX9j:function(e,t,r){"use strict";r("ZzA3")},"cK+H":function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"item-item"},[t("div",{staticClass:"flex-row-default"},[t("div",{staticClass:"box-card box-right"},[t("div",{staticClass:"text item"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticClass:"data_table",staticStyle:{width:"100%"},attrs:{data:e.orderItem,"header-cell-style":{background:"#f5f7fa"},border:"","highlight-current-row":""}},[e._e(),t("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150"}}),t("el-table-column",{attrs:{prop:"specs",label:"规格"}}),t("el-table-column",{attrs:{prop:"packingSpecs",label:"包装规格"}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"最小单位价(元)",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(e.getPrice(t.row.packingSpecs,t.row.unitPrice))+"\n                        ")]}}])}),t("el-table-column",{attrs:{prop:"unitPrice",label:"商品价(元)"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(t.row.unitPrice)+"\n                        ")]}}])}),t("el-table-column",{attrs:{prop:"amount",label:"采购数量"}}),t("el-table-column",{attrs:{prop:"itemPrice",label:"合计"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                            "+e._s(t.row.itemPrice+"元")+"\n                        ")]}}])}),t("el-table-column",{attrs:{prop:"systemContrast",label:"系统推荐",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return["1"==r.row.systemContrast?t("span",[e._v("是")]):e._e(),"0"==r.row.systemContrast?t("div",[t("el-popover",{attrs:{placement:"top-start",width:"250",trigger:"hover"}},[r.row.reason?t("div",{staticClass:"text item"},[e._v("\n                                 "+e._s(r.row.reason)+"\n                                ")]):t("div",{staticClass:"text item"},[e._v("\n                                  无\n                                ")]),t("el-tag",{attrs:{slot:"reference",type:"danger"},slot:"reference"},[e._v("否(查看原因)")])],1)],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"country",label:"是否国家集采",width:"110",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return["1"==r.row.country?t("div",[t("el-tag",{attrs:{type:"primary"}},[e._v("是")])],1):e._e(),"1"!=r.row.country?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("否")])],1):e._e()]}}])}),e.recommend?e._e():t("el-table-column",{attrs:{prop:"ORDER_ITEM_STATUS",label:"发货状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return["0"==r.row.orderItemStatus?t("div",[t("el-tag",{attrs:{type:"info"}},[e._v("待确认")])],1):e._e(),"1"==r.row.orderItemStatus?t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("待发货")])],1):e._e(),"2"==r.row.orderItemStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分发货")])],1):e._e(),"3"==r.row.orderItemStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已发货")])],1):e._e(),"4"==r.row.orderItemStatus?t("div",[t("el-tag",{attrs:{type:"primary"}},[e._v("已完成")])],1):e._e(),r.row.orderItemStatus&&"6"!=r.row.orderItemStatus?e._e():t("div",[t("el-tag",{attrs:{type:"info"}},[e._v("无")])],1)]}}],null,!1,3071207368)}),e.recommend?e._e():t("el-table-column",{attrs:{prop:"stockStatus",label:"入库状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.stockStatus&&"0"!=r.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未入库")])],1),"1"==r.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已入库")])],1):e._e(),"2"==r.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分入库")])],1):e._e()]}}],null,!1,1922240075)}),e.recommend?e._e():t("el-table-column",{attrs:{prop:"payStatus",label:"支付状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return["1"==r.row.payStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已支付")])],1):e._e(),"2"==r.row.payStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分支付")])],1):e._e(),"0"!=r.row.payStatus&&r.row.payStatus||r.row.stockStatus&&"0"!=r.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("未支付")])],1),"0"!=r.row.payStatus&&r.row.payStatus||!r.row.stockStatus||"0"==r.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未支付")])],1)]}}],null,!1,984211271)}),t("el-table-column",{attrs:{prop:"",label:"查看详情",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.showDeliveryAndInvoice(r.row)}}},[e._v("查看")])]}}])})],1)],1)])]),t("el-dialog",{attrs:{title:"详情信息",visible:e.dialogVisible,width:"70%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",{staticClass:"dialog-content"},[t("p",[e._v("订单明细信息")]),t("item-detail",{ref:"orderItem",attrs:{name:"0",orderItemId:this.orderItemId}})],1),t("div",{staticClass:"dialog-content1"},[t("p",[e._v("配送明细信息")]),t("delivery-item-list",{ref:"deliveryItem",attrs:{name:"0",orderItemId:this.orderItemId}})],1),t("div",{staticClass:"dialog-content2"},[t("p",[e._v("发票明细信息")]),t("invoice-item",{ref:"deliveryItem",attrs:{name:"0",orderItemId:this.orderItemId}})],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("关闭")])],1)])],1)},t.staticRenderFns=[]},chxd:function(e,t,r){"use strict";r.r(t);var o=r("AvpF"),n=r.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){r.d(t,e,function(){return o[e]})}(a);t.default=n.a},cjK8:function(e,t,r){"use strict";r.r(t);var o=r("xa2m"),n=r("AQM8");for(var a in n)["default"].indexOf(a)<0&&function(e){r.d(t,e,function(){return n[e]})}(a);r("S2an");var i=r("gp09"),s=Object(i.a)(n.default,o.render,o.staticRenderFns,!1,null,"c05a7a04",null);t.default=s.exports},cyWD:function(e,t,r){"use strict";var o=r("zY1e");r.o(o,"render")&&r.d(t,"render",function(){return o.render}),r.o(o,"staticRenderFns")&&r.d(t,"staticRenderFns",function(){return o.staticRenderFns})},duH7:function(e,t,r){"use strict";var o=r("eJHg");r.o(o,"render")&&r.d(t,"render",function(){return o.render}),r.o(o,"staticRenderFns")&&r.d(t,"staticRenderFns",function(){return o.staticRenderFns})},e3HG:function(e,t,r){},eJHg:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"item-item"},[t("div",{staticClass:"flex-row-default"},[t("div",{staticClass:"box-card box-right"},[t("div",{staticClass:"text item"},[t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{border:"",data:e.deliveryItemDataList,"highlight-current-row":""}},[t("el-table-column",{attrs:{prop:"itemNo",label:"配送明细号",width:"180"}}),t("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"220"}}),t("el-table-column",{attrs:{prop:"deliveryName",label:"配送企业",width:"220"}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"单价(元)",width:"100"}}),t("el-table-column",{attrs:{prop:"num",label:"发货数量"}}),t("el-table-column",{attrs:{prop:"amount",label:"发货金额"}}),t("el-table-column",{attrs:{prop:"deliveryTime",label:"配送时间"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s(e._f("formatTime")(r.row.deliveryTime)))])]}}])}),t("el-table-column",{attrs:{prop:"warning",label:"预警状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.warning&&"1"!=r.row.warning&&"4"!=r.row.warning?e._e():t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("正常")])],1),r.row.warning&&"1"!=r.row.warning&&"4"!=r.row.warning?t("div",[t("el-popover",{attrs:{placement:"top-start",width:"250",trigger:"hover"}},[e._l(e.getWaring(r.row.warning),function(r,o){return t("div",{key:o,staticClass:"text item"},[e._v("\n                                    "+e._s(o+1+"、"+r.name)+"\n                                  ")])}),t("el-tag",{attrs:{slot:"reference",type:"danger"},slot:"reference"},[e._v("查看预警")])],2)],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"stockStatus",label:"入库状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.stockStatus&&"0"!=r.row.stockStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未入库")])],1),"1"==r.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已入库")])],1):e._e(),"2"==r.row.stockStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分入库")])],1):e._e()]}}])})],1)],1)])],1)])]),t("el-dialog",{attrs:{title:"发票信息",visible:e.dialogVisible,width:"70%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",{staticClass:"dialog-content"},[t("invoice-item",{ref:"deliveryItem",attrs:{name:"0",deliveryItemId:this.deliveryItemId}})],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("关闭")])],1)])],1)},t.staticRenderFns=[]},igvt:function(e,t,r){"use strict";r.r(t);var o=r("GTtU"),n=r.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){r.d(t,e,function(){return o[e]})}(a);t.default=n.a},pU08:function(e,t,r){"use strict";r.r(t);var o=r("5sBe"),n=r("w18v");for(var a in n)["default"].indexOf(a)<0&&function(e){r.d(t,e,function(){return n[e]})}(a);r("MaVJ");var i=r("gp09"),s=Object(i.a)(n.default,o.render,o.staticRenderFns,!1,null,"9a07a89c",null);t.default=s.exports},qYj6:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var o=i(r("pU08")),n=i(r("DWNM")),a=i(r("Q9c5"));function i(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[n.default],name:"deliveryItemList",data:function(){return{warningData:[],deliveryItemId:"",dialogVisible:!1,deliveryItemDataList:[]}},props:{deliveryCode:{type:String,default:"#"},orderCode:{type:String,default:"#"},orderItemId:{type:String,default:"#"}},components:{invoiceItem:o.default},computed:{goPath:{get:function(){return"#"!=this.orderCode?"/order/orderDetail/invoice/invoiceItemList":"#"!=this.deliveryCode?"/delivery/deliveryDetail/invoice/invoiceItemList":"#"!=this.orderItemId?"/sup/delivery/deliveryItemList":void 0},set:function(e){}}},watch:{orderCode:function(e){e&&this.onDeliveryQuery()},deliveryCode:function(e){e&&this.onDeliveryQuery()},orderItemId:function(e){e&&this.onDeliveryQuery()}},mounted:function(){this.onDeliveryQuery(),this.getDictItem("WARNING")},methods:{getDictItem:function(e){var t=this,r=this.openLoading();this.$http_post(a.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(o){var n=o.rows;1==o.state?("SOURCE"==e&&(t.drugSourceList=n),"WARNING"==e&&(t.warningData=n),r.close()):(r.close(),t.$message.error(o.message))})},getWaringType:function(e){for(var t=0;t<this.warningData.length;t++)if(this.warningData[t].value==e)return this.warningData[t].label},getWaring:function(e){for(var t=[],r=e.split(","),o=0;o<r.length;o++){var n=this.getWaringType(r[o]);n&&t.push({name:n})}return t},showInvoice:function(e){this.deliveryItemId=e,this.dialogVisible=!0},onDeliveryQuery:function(){var e=this,t={orderCode:"#",orderItemId:"#"};"#"!=this.orderCode&&(t={orderCode:this.orderCode}),"#"!=this.orderItemId&&(t={orderItemId:this.orderItemId}),"#"!=this.deliveryCode&&(t={deliveryCode:this.deliveryCode});var r=this.openLoading(),o=a.default.baseContext+"/supervise/supDeliveryItem/getDeliveryItemByCode";this.$http_post(o,t).then(function(t){1==t.state?(e.deliveryItemDataList=t.rows,r.close()):(r.close(),e.$alert(t.message))})}},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e),r=t.getFullYear()+"-",o=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",n=t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ";t.getHours(),t.getHours(),t.getMinutes(),t.getMinutes(),t.getSeconds(),t.getSeconds();return r+o+n}}}},tNMj:function(e,t,r){"use strict";r.r(t);var o=r("cyWD"),n=r("chxd");for(var a in n)["default"].indexOf(a)<0&&function(e){r.d(t,e,function(){return n[e]})}(a);r("8713");var i=r("gp09"),s=Object(i.a)(n.default,o.render,o.staticRenderFns,!1,null,"dec4c0de",null);t.default=s.exports},w18v:function(e,t,r){"use strict";r.r(t);var o=r("CFHU"),n=r.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){r.d(t,e,function(){return o[e]})}(a);t.default=n.a},w6aq:function(e,t,r){"use strict";r.r(t);var o=r("qYj6"),n=r.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){r.d(t,e,function(){return o[e]})}(a);t.default=n.a},xa2m:function(e,t,r){"use strict";var o=r("cK+H");r.o(o,"render")&&r.d(t,"render",function(){return o.render}),r.o(o,"staticRenderFns")&&r.d(t,"staticRenderFns",function(){return o.staticRenderFns})},zY1e:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",[t("el-dialog",{attrs:{title:"入库凭证信息",visible:e.dialogVisible,top:"10vh",width:"70%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",{staticClass:"dialog-content"},[t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:!1,expression:"false"}],staticClass:"data_table",staticStyle:{width:"100%"},attrs:{"header-cell-style":{background:"#f5f7fa"},data:e.orderStocks,"highlight-current-row":""}},[t("el-table-column",{attrs:{label:"序号",width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(t.$index+1)+"\n                            ")]}}])}),t("el-table-column",{attrs:{label:"入库数量",align:"center",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(t.row.itemStock)+"\n                            ")]}}])}),t("el-table-column",{attrs:{width:"180px",label:"入库时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(e._f("formatTime")(t.row.stockTime))+"\n                            ")]}}])}),t("el-table-column",{attrs:{width:"300px",label:"入库凭证",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return e._l(JSON.parse(r.row.docInfo),function(r,o){return t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.downloadFile(r.docId,r.name)}}},[e._v("\n                                    "+e._s(r.name)+"\n                                ")])})}}])}),t("el-table-column",{attrs:{label:"备注",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(t.row.remark)+"\n                            ")]}}])}),t("el-table-column",{attrs:{width:"180px",label:"数据上传时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(e._f("formatTime")(t.row.creationTime))+"\n                            ")]}}])}),"itemStockVoucher"==e.$route.name?t("el-table-column",{attrs:{label:"操作",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.editOrderStock(r.row)}}},[e._v("编辑")]),t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.deleteOrderStock(r.row)}}},[e._v("删除")])]}}],null,!1,209884114)}):e._e()],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("关闭")])],1)]),t("el-dialog",{attrs:{title:this.title,visible:e.show,width:"60%"},on:{close:e.close}},[t("div",{staticClass:"dialog-content"},[t("el-form",{ref:"orderStock",staticClass:"item-form",attrs:{id:"orderStock",model:e.orderStock,"label-width":"90px"}},[t("div",{staticClass:"fromBox"},[t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"入库数量",prop:"itemStock"}},[t("el-input",{model:{value:e.orderStock.itemStock,callback:function(t){e.$set(e.orderStock,"itemStock",e._n(t))},expression:"orderStock.itemStock"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"入库时间",prop:"stockTime"}},[t("el-date-picker",{attrs:{type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss",size:"small",placeholder:"请选择入库时间"},model:{value:e.orderStock.stockTime,callback:function(t){e.$set(e.orderStock,"stockTime",t)},expression:"orderStock.stockTime"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"凭证上传"}},[t("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{headers:e.headers,action:e.uploadUrl,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"before-upload":e.beforeUpload,"before-remove":e.beforeRemove,"on-success":e.onSuccess,"on-error":e.onError,multiple:"",accept:".gif,.jpg,.jpeg,.bmp,.png,.doc,.docx,.txt,.xlsx,.xls,.pdf","file-list":e.fileList}},[t("el-button",{attrs:{size:"small"}},[e._v("点击上传")])],1)],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"备注",prop:"remark"}},[t("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.orderStock.remark,callback:function(t){e.$set(e.orderStock,"remark",t)},expression:"orderStock.remark"}})],1)],1)],1)],1)])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.close}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.saveOrderStock}},[e._v("确定")])],1)])],1)},t.staticRenderFns=[]}}]);