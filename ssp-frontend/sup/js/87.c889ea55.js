(window.webpackJsonp=window.webpackJsonp||[]).push([[87],{"Bp/v":function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var s=n(a("Q9c5")),r=n(a("DWNM"));function n(t){return t&&t.__esModule?t:{default:t}}e.default={name:"paymentList",mixins:[r.default],data:function(){return{hospitalList:[],rLoading:{},tableHeight:100,dataList:[],params:{page:1,limit:10,records:0,num:"",date:"",bizStatus:"",transferStatus:"",startDate:"",endDate:""},adminRole:!1,hospitalAdmin:!1,medicalAdmin:!1,deliveryAdmin:!1}},props:{},watch:{},computed:{uploadUrl:function(){}},mounted:function(){var t=this,e=this.$store.getters.curUser.roleCode;-1!=e.indexOf("DELIVERY_ADMIN")&&(this.deliveryAdmin=!0),-1!=e.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=e.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=e.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0),this.onQuery(),this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}},methods:{onSearch:function(t){"reset"==t?(this.params.num="",this.params.bizStatus="",this.params.date="",this.params.startDate="",this.params.endDate="",this.onQuery()):""!=this.params.num||""!=this.params.bizStatus||""!=this.params.date||""!=this.params.transferStatus?(this.params.page=1,this.onQuery()):this.$message.error("请输入查询条件")},onPageClick:function(t){this.params.page=t,this.onQuery()},onQuery:function(){var t=this;this.params.date&&(this.params.startDate=this.params.date[0],this.params.endDate=this.params.date[1]);var e=this.params,a=this.openLoading(),r=s.default.baseContext+"/supervise/supPayment/list";this.$http_post(r,e).then(function(e){1==e.state?(t.dataList=e.rows,t.params.records=e.records,a.close()):(a.close(),t.$alert(e.message))})},transfer:function(t){this.$router.push({name:"paymentTransferDetail",query:{paymentId:t.id}})},detail:function(t){this.$router.push({name:"paymentDetail",query:{paymentId:t.id}})},recall:function(t){}},beforeDestroy:function(){window.onresize=null},filters:{formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}}}},HG8q:function(t,e,a){},LnhI:function(t,e,a){"use strict";var s=a("hn1H");a.o(s,"render")&&a.d(e,"render",function(){return s.render}),a.o(s,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return s.staticRenderFns})},ft91:function(t,e,a){"use strict";a.r(e);var s=a("LnhI"),r=a("yuSx");for(var n in r)["default"].indexOf(n)<0&&function(t){a.d(e,t,function(){return r[t]})}(n);a("xMMu");var l=a("gp09"),i=Object(l.a)(r.default,s.render,s.staticRenderFns,!1,null,"9a33e6f8",null);e.default=i.exports},hn1H:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left"},[e("el-row",{attrs:{gutter:10}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("支付单编号")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入通用名"},model:{value:t.params.num,callback:function(e){t.$set(t.params,"num",e)},expression:"params.num"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("业务状态")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择状态"},model:{value:t.params.bizStatus,callback:function(e){t.$set(t.params,"bizStatus",e)},expression:"params.bizStatus"}},[e("el-option",{key:"1",attrs:{label:"在办",value:"1"}}),e("el-option",{key:"2",attrs:{label:"支付单阶段完成",value:"2"}}),e("el-option",{key:"3",attrs:{label:"不予办理",value:"2"}})],1)],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("支付状态")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择状态"},model:{value:t.params.transferStatus,callback:function(e){t.$set(t.params,"transferStatus",e)},expression:"params.transferStatus"}},[e("el-option",{key:"0",attrs:{label:"待支付",value:"0"}}),e("el-option",{key:"1",attrs:{label:"已支付",value:"1"}}),e("el-option",{key:"2",attrs:{label:"无需支付",value:"2"}})],1)],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("申请时间")]),e("div",{staticClass:"searchInput"},[e("el-date-picker",{attrs:{size:"small","value-format":"yyyy-MM-dd HH:mm:ss",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:t.params.date,callback:function(e){t.$set(t.params,"date",e)},expression:"params.date"}})],1)])])],1)],1),e("div",{staticClass:"right"},[e("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1)]),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.dataList,"highlight-current-row":"",border:"",height:t.tableHeight}},[e("el-table-column",{attrs:{type:"index",label:"序号",width:"50"}}),e("el-table-column",{attrs:{prop:"num",label:"支付单编号","min-width":"130px"}}),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构名称","min-width":"130px"}}),e("el-table-column",{attrs:{prop:"deliveryName",label:"配送企业名称","min-width":"140px"}}),e("el-table-column",{attrs:{prop:"count",label:"总笔数"}}),e("el-table-column",{attrs:{prop:"totalPrice",label:"总金额"}}),e("el-table-column",{attrs:{prop:"backCount",label:"打回笔数"}}),e("el-table-column",{attrs:{prop:"backPrice",label:"打回总金额"}}),e("el-table-column",{attrs:{prop:"bizStatus",label:"业务状态","min-width":"100px;"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.bizStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("在办")])],1):t._e(),"2"==a.row.bizStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("支付单阶段完成")])],1):t._e(),"3"==a.row.bizStatus?e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("不予办理")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"transferStatus",label:"支付状态"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.bizStatus?e("div",[e("el-tag",[t._v("未办结")])],1):"0"==a.row.transferStatus?e("div",[e("el-tag",{attrs:{type:"warming"}},[t._v("待支付")])],1):"1"==a.row.transferStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已支付")])],1):"2"==a.row.transferStatus?e("div",[e("el-tag",{attrs:{type:"info"}},[t._v("无需支付")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"curNode",label:"当前环节","min-width":"100px;"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s("2"==a.row.bizStatus?"支付单阶段完成":"1"==a.row.curNode?"结算申请":"2"==a.row.curNode?"结算申请审核":"3"==a.row.curNode?"结算申请复核":"4"==a.row.curNode?"对账单生成":"5"==a.row.curNode?"结算对账医疗机构确认":"6"==a.row.curNode?"结算对账药企确认":"7"==a.row.curNode?"支付单生成":"8"==a.row.curNode?"结算支付审批":"9"==a.row.curNode?"结算支付完成":""))])]}}])}),e("el-table-column",{attrs:{prop:"creationTime",label:"生成时间","min-width":"120px"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t._f("formatTime")(a.row.creationTime)))])]}}])}),e("el-table-column",{attrs:{prop:"createName",label:"创建人"}}),t.$route.query.type?t._e():e("el-table-column",{attrs:{label:"操作",align:"center",width:"100",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.detail(a.row)}}},[t._v("详情")]),"9"==a.row.curNode&&(t.adminRole||t.medicalAdmin)?e("el-button",{attrs:{disabled:!(a.row.count>a.row.backCount),type:"text",size:"small"},on:{click:function(e){return t.transfer(a.row)}}},[t._v("划转")]):t._e()]}}],null,!1,**********)})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)])],1)},e.staticRenderFns=[]},xMMu:function(t,e,a){"use strict";a("HG8q")},yuSx:function(t,e,a){"use strict";a.r(e);var s=a("Bp/v"),r=a.n(s);for(var n in s)["default"].indexOf(n)<0&&function(t){a.d(e,t,function(){return s[t]})}(n);e.default=r.a}}]);