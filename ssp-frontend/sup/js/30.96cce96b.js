(window.webpackJsonp=window.webpackJsonp||[]).push([[30],{"+dkC":function(e,t,n){"use strict";n.r(t);var r=n("TVGL"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=a.a},AfVr:function(e,t,n){"use strict";var r=n("b9UE");n.o(r,"render")&&n.d(t,"render",function(){return r.render}),n.o(r,"staticRenderFns")&&n.d(t,"staticRenderFns",function(){return r.staticRenderFns})},BzFU:function(e,t,n){"use strict";n.r(t);var r=n("XhNf"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,function(){return r[e]})}(o);t.default=a.a},Ecnb:function(e,t,n){"use strict";n("lDFQ")},M6Pp:function(e,t,n){"use strict";n.r(t);var r=n("AfVr"),a=n("BzFU");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,function(){return a[e]})}(o);n("NKlQ");var s=n("gp09"),i=Object(s.a)(a.default,r.render,r.staticRenderFns,!1,null,"7e154d66",null);t.default=i.exports},MVUy:function(e,t,n){"use strict";n.r(t);var r=n("YhEh"),a=n("+dkC");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,function(){return a[e]})}(o);n("Ecnb");var s=n("gp09"),i=Object(s.a)(a.default,r.render,r.staticRenderFns,!1,null,"266883d8",null);t.default=i.exports},NKlQ:function(e,t,n){"use strict";n("pzka")},T7pg:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"flex-row content"},[t("div",{staticClass:"left"},[t("div",{staticClass:"title flex-row"},[t("span",{staticClass:"name"},[e._v("行政组织机构树")]),t("el-button",{attrs:{disabled:""==this.parentId,type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.openAdd}})],1),t("div",{staticClass:"tree"},[t("organization-tree",{ref:"tree",on:{onClick:e.onTree}})],1)]),t("div",{staticClass:"right"},[t("el-table",{staticStyle:{width:"100%"},attrs:{border:"",height:e.tableHeight,data:e.tableData}},[t("el-table-column",{attrs:{prop:"code",label:"编码",width:"180"}}),t("el-table-column",{attrs:{prop:"name",label:"名称"}}),t("el-table-column",{attrs:{prop:"status",width:"100",align:"center",label:"状态"},scopedSlots:e._u([{key:"default",fn:function(n){return["1"==n.row.status?t("el-tag",{attrs:{effect:"plain",type:"success"}},[e._v("\n                        有效\n                    ")]):t("el-tag",{attrs:{effect:"plain",type:"danger"}},[e._v("\n                        无效\n                    ")])]}}])}),t("el-table-column",{attrs:{width:"230",align:"right"},scopedSlots:e._u([{key:"header",fn:function(n){return[t("el-input",{attrs:{size:"medium",placeholder:"输入名称搜索"},model:{value:e.searchValue,callback:function(t){e.searchValue=t},expression:"searchValue"}},[t("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"suffix"},on:{click:e.getList},slot:"suffix"})])]}},{key:"default",fn:function(n){return[t("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:function(t){return e.addEdit("edit",n.row)}}},[e._v("编辑")]),t("el-button",{attrs:{type:"danger",size:"small",icon:"el-icon-delete"},on:{click:function(t){return e.remove(n.row,n.$index)}}},[e._v("删除")])]}}])})],1),t("el-pagination",{attrs:{"page-size":e.pagination.size,layout:"total, prev, pager, next, jumper",total:e.pagination.total},on:{"current-change":e.handleCurrentChange}})],1),t("el-dialog",{attrs:{title:""==this.form.id?"添加组织机构":"编辑组织机构",visible:e.addBox,width:"50%"},on:{"update:visible":function(t){e.addBox=t}}},[t("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-position":"left","label-width":"80px"}},[t("el-form-item",{attrs:{label:"上级"}},[e._v("\n                "+e._s(e.form.parentName)+"\n            ")]),t("el-form-item",{attrs:{label:"编码",prop:"code"}},[t("el-input",{model:{value:e.form.code,callback:function(t){e.$set(e.form,"code",t)},expression:"form.code"}})],1),t("el-form-item",{attrs:{label:"名称",prop:"name"}},[t("el-input",{model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),t("el-form-item",{attrs:{label:"状态",prop:"status"}},[t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}})],1),t("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[t("el-input-number",{attrs:{min:0,label:"请输入排序"},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,"sortOrder",t)},expression:"form.sortOrder"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.addBox=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addEdit("add")}}},[e._v("确 定")])],1)],1)],1)},t.staticRenderFns=[]},TVGL:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=o(n("Q9c5")),a=o(n("M6Pp"));function o(e){return e&&e.__esModule?e:{default:e}}t.default={name:"organization",components:{OrganizationTree:a.default},data:function(){return{searchValue:"",parentInnerCode:"",addBox:!1,parentId:"",parentName:"",form:{id:"",code:"",name:"",status:0,sortOrder:1,parentId:""},rules:{code:[{required:!0,message:"编码不能为空",trigger:"blur"}],name:[{required:!0,message:"请输入名称",trigger:"blur"}],status:[{required:!0,message:"请选择",trigger:"change"}],sortOrder:[{type:"number",required:!0,message:"请输入排序",trigger:"change"}]},tableData:[],tableHeight:100,pagination:{page:1,size:10,total:0}}},watch:{},computed:{},mounted:function(){var e=this;this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-40}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-40},this.getList()},methods:{openAdd:function(){""!=this.parentId?(this.form={id:"",code:"",name:"",status:0,sortOrder:1,parentName:this.parentName,parentId:this.parentId},this.addBox=!0):this.$message.error("请选择左侧行政组织机构树")},addEdit:function(e,t){var n=this;if("add"==e&&this.$refs.form.validate(function(e){if(!e)return!1;var t={code:n.form.code},a={code:n.form.code,name:n.form.name,status:n.form.status,sortOrder:n.form.sortOrder,parentId:n.form.parentId},o="/bsp/pubDept/save";null!=n.form.id&&""!=n.form.id&&(a.parentId=n.form.parentId,a.id=n.form.id,t.id=n.form.id,o="/bsp/pubDept/update");var s=n.openLoading();n.$http_post(r.default.baseContext+"/bsp/pubDept/checkCode",t).then(function(e){1==e.state?n.$http_post(r.default.baseContext+o,a).then(function(e){1==e.state?(n.addBox=!1,n.$refs.tree.requestNewData(),-1!=o.indexOf("save")?n.$message.success("添加成功"):n.$message.success("修改成功"),n.getList()):null!=e.message?n.$message.error(e.message):n.$message.error("系统异常"),s.close()}).catch(function(e){s.close(),console.log(e)}):n.$alert(e.message),s.close()})}),"edit"==e){var a=this.openLoading();this.$http_post(r.default.baseContext+"/bsp/pubDept/show",{id:t.id}).then(function(e){if(1==e.state)if(null!=e.row){var t=e.row;n.form={id:t.id,code:t.code,name:t.name,status:t.status,sortOrder:t.sortOrder,parentId:t.parentId,parentName:"#"!=t.parentId?t.parentName:t.name},n.addBox=!0}else n.$message.error("系统异常");else null!=e.message?n.$message.error(e.message):n.$message.error("系统异常");a.close()}).catch(function(e){a.close(),console.log(e)})}},remove:function(e,t){var n=this;this.$confirm("确认删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var t=n.openLoading("删除中...");n.$http_post(r.default.baseContext+"/bsp/pubDept/delete",{id:e.id}).then(function(e){1==e.state?(n.$message.success("删除成功"),n.$refs.tree.requestNewData(),n.getList()):null!=e.message?n.$message.error(e.message):n.$message.error("系统异常"),t.close()}).catch(function(e){t.close(),console.log(e)})}).catch(function(e){console.log(e)})},getList:function(){var e=this,t={parentInnerCode:this.parentInnerCode,name:this.searchValue,page:this.pagination.page,limit:this.pagination.size},n=this.openLoading();this.$http_post(r.default.baseContext+"/bsp/pubDept/query",t).then(function(t){1==t.state?(e.pagination.total=t.records,null!=t.rows?e.tableData=t.rows:e.$message.error("系统异常")):null!=t.message?e.$message.error(t.message):e.$message.error("系统异常"),n.close()}).catch(function(e){n.close(),console.log(e)})},onTree:function(e,t,n){this.parentId=e.id,this.parentName=e.name,this.parentInnerCode=e.innerCode,this.getList()},handleCurrentChange:function(e){this.pagination.page=e,this.getList()}},beforeDestroy:function(){window.onresize=null}}},XhNf:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=function(e){return e&&e.__esModule?e:{default:e}}(n("Q9c5"));t.default={name:"organization-tree",components:{},props:{expandAll:{type:Boolean,default:!1},expandedKeys:{type:Array,default:function(){return[]}},accordion:{type:Boolean,default:!1},onClickNode:{type:Boolean,default:!1},highlightCurrent:{type:Boolean,default:!0},parentCode:""},data:function(){return{data:[],props:{label:"name",children:"children",isLeaf:"leaf"},node_had:[],resolve_had:[]}},watch:{},computed:{},mounted:function(){},methods:{loadNode:function(e,t){var n=this;this.node_had=e,this.resolve_had=t;var a={parentId:"#"},o=this.parentCode;if(null!=o&&void 0!=o&&(a={code:o}),void 0!=e.data.id)a.parentId=e.data.id;else for(var s=r.default.treeCompetence,i=this.$store.getters.curUser,l=this.$store.getters.curUser.roleCode.split(","),d=0,c=l.length;d<c;d++)for(var u=0,f=s.length;u<f;u++)l[d]==s[u]&&(a={code:i.deptCode});this.$http_post(r.default.baseContext+"/bsp/pubDept/list",a).then(function(e){1==e.state?null!=e.rows?t(e.rows):n.$message.error("系统异常"):null!=e.message?n.$message.error(e.message):n.$message.error("系统异常")}).catch(function(e){console.log(e)})},requestNewData:function(){this.node_had.childNodes=[],this.loadNode(this.node_had,this.resolve_had)},onClick:function(e,t,n){this.$emit("onClick",e,t,n)},getCurrentNode:function(){return this.$refs.deptTree.getCurrentNode()}}}},YhEh:function(e,t,n){"use strict";var r=n("T7pg");n.o(r,"render")&&n.d(t,"render",function(){return r.render}),n.o(r,"staticRenderFns")&&n.d(t,"staticRenderFns",function(){return r.staticRenderFns})},b9UE:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",[t("el-tree",{ref:"deptTree",attrs:{data:e.data,"node-key":"id",load:e.loadNode,lazy:"",props:e.props,"default-expand-all":e.expandAll,"highlight-current":e.highlightCurrent,"default-expanded-keys":e.expandedKeys,accordion:e.accordion,"expand-on-click-node":e.onClickNode},on:{"node-click":e.onClick},scopedSlots:e._u([{key:"default",fn:function(n){var r=n.node;return n.data,t("span",{staticClass:"custom-tree-node"},[t("span",{staticClass:"icon el-icon-house"}),t("span",[e._v(e._s(r.label))])])}}])})],1)},t.staticRenderFns=[]},lDFQ:function(e,t,n){},pzka:function(e,t,n){}}]);