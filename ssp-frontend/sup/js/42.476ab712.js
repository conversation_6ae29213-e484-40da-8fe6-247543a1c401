(window.webpackJsonp=window.webpackJsonp||[]).push([[42],{"5xgi":function(t,e,a){"use strict";a("tm9F")},AGGB:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var s=o(a("DWNM")),r=o(a("Q9c5")),l=o(a("rGKd"));function o(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[s.default],name:"orderItemList",data:function(){return{adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1,areaRegionAdmin:!1,batchList:[],pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,s=new Date(a.getTime()-6048e5).format("yyyy-MM-dd");t.$emit("pick",[s,e])}},{text:"最近一个月",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,s=new Date(a.getTime()-2592e6).format("yyyy-MM-dd");t.$emit("pick",[s,e])}},{text:"最近三个月",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,s=new Date(a.getTime()-7776e6).format("yyyy-MM-dd");t.$emit("pick",[s,e])}}]},tableHeight:100,warningData:[],drugSourceList:[],listLoading:!1,orderItemList:[],hospitalList:[],params:{systemContrast:"",source:"",catalogName:"",submitTime:"",hospitalName:"",country:"",page:1,limit:10,records:0,orderCode:"",orderItemStatus:"",stockStatus:"",payStatus:"",batch:"",stockInTime:"",specs:"",drugCompanyName:"",standardCode:"",medicalInsuranceCode:"",regionId:""},dateChangeValue:0,regionList:[{code:"440703",name:"蓬江区"},{code:"440704",name:"江海区"},{code:"440705",name:"新会区"},{code:"440781",name:"台山市"},{code:"440783",name:"开平市"},{code:"440784",name:"鹤山市"},{code:"440785",name:"恩平市"},{code:"440700",name:"江门市"}]}},watch:{$route:function(){this.$route.query.country&&(this.params.country=this.$route.query.country,this.onSearch("reset"),this.onQuery(),this.initHeight())}},mounted:function(){this.initRole(),this.getDictItem("SOURCE"),this.getDictItem("WARNING"),this.onQuery(),this.getHospitalList(),l.default.getBatchList(this.setBatchList),this.initHeight()},beforeDestroy:function(){window.onresize=null},methods:{setDate:function(){var t=(new Date).format("yyyy-MM-dd"),e=new Date,a=new Date(e.getTime()-2592e6).format("yyyy-MM-dd");this.params.submitTime=[a,t]},setBatchList:function(t){for(var e=t.rows,a=0;a<e.length;a++)this.batchList.push(e[a])},initRole:function(){var t=this.$store.getters.curUser.roleCode;-1!=t.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=t.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=t.indexOf("DELIVERY_ADMIN")?this.areaRegionAdmin=!0:-1!=t.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=t.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)},getHospitalList:function(){var t=this,e=r.default.baseContext+"/supervise/supHospital/all";this.$http_post(e,{}).then(function(e){1==e.state?t.hospitalList=e.row:t.$alert(e.message)})},orderItemExportExcel:function(){var t=this,e=this.$store.getters.curUser.id,a=this.$store.getters.curUser.roleCode;e||this.$message.error("用户未登录"),null==this.params.submitTime&&(this.params.submitTime=""),this.$confirm("确定导出采购明细数据吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var s=r.default.baseContext+"/supervise/supOrderItem/exportOrderItem?hospitalName="+t.params.hospitalName+"&orderItemStatus="+t.params.orderItemStatus+"&orderCode="+t.params.orderCode+"&userId="+e+"&roleValue="+a+"&catalogName="+t.params.catalogName+"&submitTime="+t.params.submitTime+"&country="+t.$route.query.country+"&source="+t.params.source+"&stockStatus="+t.params.stockStatus+"&payStatus="+t.params.payStatus+"&batch="+t.params.batch+"&stockInTime"+t.params.stockInTime+"&regionId="+t.params.regionId+"&specs="+t.params.specs+"&drugCompanyName="+t.params.drugCompanyName+"&standardCode="+t.params.standardCode+"&medicalInsuranceCode="+t.params.medicalInsuranceCode;t.$post_blob(s,{responseType:"blob",timeout:6e4}).then(function(e){if("application/json"==e.type){var a=new FileReader;return a.addEventListener("loadend",function(e){t.$message.error(JSON.parse(e.target.result).message)}),void a.readAsText(e)}var s=URL.createObjectURL(e),r=document.createElement("a");r.href=s,r.target="_blank","0"==t.$route.query.country&&(r.download="非集采采购明细数据.xls"),"1"==t.$route.query.country&&(r.download="国集采购明细数据.xls"),"2"==t.$route.query.country&&(r.download="线下采购明细数据.xls"),r.click()}).catch(function(t){console.log(t)})}).catch(function(t){console.log(t)})},time:function(t,e){if(void 0==t.submitTime||""==t.submitTime)return"";var a=new Date(t.submitTime),s=a.getFullYear()+"-",r=(a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-",l=a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ";a.getHours(),a.getHours(),a.getMinutes(),a.getMinutes(),a.getSeconds(),a.getSeconds();return s+r+l},initHeight:function(){var t=this;this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-180}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-180}},onQuery:function(){var t=this;this.$route.query.country&&(this.params.country=this.$route.query.country),0!=this.dateChangeValue&&""!=this.dateChangeValue||this.setDate();var e=this.params,a=this.openLoading(),s=r.default.baseContext+"/supervise/supOrderItem/list";this.$http_post(s,e).then(function(e){if(1==e.state){var s=e.rows;t.orderItemList=s,t.params.records=e.records,a.close()}else a.close(),t.$alert(e.message)})},onSearch:function(t){"reset"==t?(this.params.systemContrast="",this.params.orderItemStatus="",this.params.orderCode="",this.params.hospitalName="",this.params.catalogName="",this.params.source="",this.params.stockStatus="",this.params.payStatus="",this.params.regionId="",this.params.specs="",this.params.drugCompanyName="",this.params.standardCode="",this.params.medicalInsuranceCode="",this.params.batch="",this.onQuery(),this.dateChangeValue=0):(this.params.page=1,this.dateChangeValue=1,this.onQuery())},onPageClick:function(t){this.params.page=t,this.onQuery()},getWaringType:function(t){for(var e=0;e<this.warningData.length;e++)if(this.warningData[e].value==t)return this.warningData[e].label},getWaring:function(t){for(var e=[],a=t.split(","),s=0;s<a.length;s++){var r=this.getWaringType(a[s]);r&&e.push({name:r})}return e},getPrice:function(t,e){if(!t)return"";var a=t.match(/\d+/g);if(!e)return"";try{return(e/a.join("")).toFixed(3)}catch(t){return""}},getPriceAmount:function(t,e){if(!t)return"";var a=t.match(/\d+/g),s=parseInt(a[0]),r=parseInt(a[1]);if(r){var l=s*r;a[0]=l}else a[0]=s;if(!e)return"";try{return e*a[0].toFixed(2)}catch(t){return""}},getDictItemName:function(t){for(var e=0;e<this.drugSourceList.length;e++)if(this.drugSourceList[e].value==t)return this.drugSourceList[e].label},getDictItem:function(t){var e=this,a=this.openLoading();this.$http_post(r.default.baseContext+"/supervise/supDictItem/getItemList",{code:t}).then(function(s){var r=s.rows;1==s.state?("SOURCE"==t&&(e.drugSourceList=r),"WARNING"==t&&(e.warningData=r),a.close()):(a.close(),e.$message.error(s.message))})},formatBatch:function(t){if(void 0==t||""==t)return"";for(var e=0;e<this.batchList.length;e++)if(this.batchList[e].code==t)return this.batchList[e].batchName}}}},N3Vh:function(t,e,a){"use strict";a.r(e);var s=a("mLCN"),r=a("OWQg");for(var l in r)["default"].indexOf(l)<0&&function(t){a.d(e,t,function(){return r[t]})}(l);a("5xgi");var o=a("gp09"),i=Object(o.a)(r.default,s.render,s.staticRenderFns,!1,null,"0c623dd0",null);e.default=i.exports},OWQg:function(t,e,a){"use strict";a.r(e);var s=a("AGGB"),r=a.n(s);for(var l in s)["default"].indexOf(l)<0&&function(t){a.d(e,t,function(){return s[t]})}(l);e.default=r.a},h9Sk:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left"},[e("el-row",{attrs:{gutter:10}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("订单号")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入订单号"},model:{value:t.params.orderCode,callback:function(e){t.$set(t.params,"orderCode",e)},expression:"params.orderCode"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("通用名")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入药品通用名"},model:{value:t.params.catalogName,callback:function(e){t.$set(t.params,"catalogName",e)},expression:"params.catalogName"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("规格")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入药品规格"},model:{value:t.params.specs,callback:function(e){t.$set(t.params,"specs",e)},expression:"params.specs"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("生产企业")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入生产企业名"},model:{value:t.params.drugCompanyName,callback:function(e){t.$set(t.params,"drugCompanyName",e)},expression:"params.drugCompanyName"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("本位码")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入药品本位码"},model:{value:t.params.standardCode,callback:function(e){t.$set(t.params,"standardCode",e)},expression:"params.standardCode"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("国家医保码")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入国家医保码"},model:{value:t.params.medicalInsuranceCode,callback:function(e){t.$set(t.params,"medicalInsuranceCode",e)},expression:"params.medicalInsuranceCode"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("批次")]),e("div",{staticClass:"searchInput"},["1"==t.$route.query.country?e("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择批次"},model:{value:t.params.batch,callback:function(e){t.$set(t.params,"batch",e)},expression:"params.batch"}},[e("el-option",{attrs:{label:"全部",value:""}}),t._l(t.batchList,function(t){return e("el-option",{key:t.code,attrs:{label:t.batchName,value:t.code}})})],2):t._e(),"1"!=t.$route.query.country?e("el-select",{attrs:{size:"small",clearable:"",filterable:"",disabled:"",placeholder:"请选择批次"},model:{value:t.params.batch,callback:function(e){t.$set(t.params,"batch",e)},expression:"params.batch"}},[e("el-option",{attrs:{label:"全部",value:""}}),t._l(t.batchList,function(t){return e("el-option",{key:t.code,attrs:{label:t.batchName,value:t.code}})})],2):t._e()],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("采购平台")]),e("div",{staticClass:"searchInput"},["2"!=t.$route.query.country?e("el-select",{attrs:{size:"small",placeholder:"请选择平台"},model:{value:t.params.source,callback:function(e){t.$set(t.params,"source",e)},expression:"params.source"}},[e("el-option",{key:"",attrs:{label:"全部",value:""}}),e("el-option",{key:"1",attrs:{label:"深圳市药品交易平台",value:"1"}}),e("el-option",{key:"2",attrs:{label:"广东省药品交易平台",value:"2"}}),e("el-option",{key:"3",attrs:{label:"广州市药品交易平台",value:"3"}})],1):t._e(),"2"==t.$route.query.country?e("el-select",{attrs:{size:"small",disabled:"",placeholder:"请选择平台"},model:{value:t.params.source,callback:function(e){t.$set(t.params,"source",e)},expression:"params.source"}},[e("el-option",{key:"",attrs:{label:"全部",value:""}}),e("el-option",{key:"1",attrs:{label:"深圳市药品交易平台",value:"1"}}),e("el-option",{key:"2",attrs:{label:"广东省药品交易平台",value:"2"}}),e("el-option",{key:"3",attrs:{label:"广州市药品交易平台",value:"3"}})],1):t._e()],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("发货状态")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",placeholder:"全部"},model:{value:t.params.orderItemStatus,callback:function(e){t.$set(t.params,"orderItemStatus",e)},expression:"params.orderItemStatus"}},[e("el-option",{key:"",attrs:{label:"全部",value:""}}),e("el-option",{key:"0",attrs:{label:"待确认",value:"0"}}),e("el-option",{key:"1",attrs:{label:"待发货",value:"1"}}),e("el-option",{key:"2",attrs:{label:"部分发货",value:"2"}}),e("el-option",{key:"3",attrs:{label:"已发货",value:"3"}}),e("el-option",{key:"4",attrs:{label:"已完成",value:"4"}}),e("el-option",{key:"5",attrs:{label:"已取消",value:"5"}})],1)],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("入库状态")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",placeholder:"全部"},model:{value:t.params.stockStatus,callback:function(e){t.$set(t.params,"stockStatus",e)},expression:"params.stockStatus"}},[e("el-option",{key:"",attrs:{label:"全部",value:""}}),e("el-option",{key:"0",attrs:{label:"未入库",value:"0"}}),e("el-option",{key:"1",attrs:{label:"已入库",value:"1"}}),e("el-option",{key:"2",attrs:{label:"部分入库",value:"2"}})],1)],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("支付状态")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",placeholder:"全部"},model:{value:t.params.payStatus,callback:function(e){t.$set(t.params,"payStatus",e)},expression:"params.payStatus"}},[e("el-option",{key:"",attrs:{label:"全部",value:""}}),e("el-option",{key:"0",attrs:{label:"未支付",value:"0"}}),e("el-option",{key:"1",attrs:{label:"已支付",value:"1"}}),e("el-option",{key:"2",attrs:{label:"部分支付",value:"2"}})],1)],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("是否推荐")]),e("div",{staticClass:"searchInput"},["0"==t.$route.query.country?e("el-select",{attrs:{size:"small",placeholder:"请选择是否系统推荐"},model:{value:t.params.systemContrast,callback:function(e){t.$set(t.params,"systemContrast",e)},expression:"params.systemContrast"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"是",value:"1"}}),e("el-option",{attrs:{label:"否",value:"0"}})],1):t._e(),"0"!=t.$route.query.country?e("el-select",{attrs:{size:"small",disabled:"",placeholder:"请选择是否系统推荐"},model:{value:t.params.systemContrast,callback:function(e){t.$set(t.params,"systemContrast",e)},expression:"params.systemContrast"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"是",value:"1"}}),e("el-option",{attrs:{label:"否",value:"0"}})],1):t._e()],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("医疗机构")]),e("div",{staticClass:"searchInput"},[this.adminRole||this.medicalAdmin||this.socialAdmin||this.areaRegionAdmin?e("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:t.params.hospitalName,callback:function(e){t.$set(t.params,"hospitalName",e)},expression:"params.hospitalName"}},t._l(t.hospitalList,function(t){return e("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})}),1):t._e(),this.adminRole||this.medicalAdmin||this.socialAdmin||this.areaRegionAdmin?t._e():e("el-select",{attrs:{size:"small",disabled:"",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:t.params.hospitalName,callback:function(e){t.$set(t.params,"hospitalName",e)},expression:"params.hospitalName"}},t._l(t.hospitalList,function(t){return e("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})}),1)],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("采购时间")]),e("div",{staticClass:"searchInput"},[e("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":t.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",clearable:"",align:"left"},model:{value:t.params.submitTime,callback:function(e){t.$set(t.params,"submitTime",e)},expression:"params.submitTime"}})],1)])]),this.adminRole||this.medicalAdmin||this.socialAdmin?e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("选择区划")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"区划管理"},model:{value:t.params.regionId,callback:function(e){t.$set(t.params,"regionId",e)},expression:"params.regionId"}},t._l(t.regionList,function(t){return e("el-option",{key:t.name,attrs:{label:t.name,value:t.code}})}),1)],1)])]):t._e()],1)],1),e("div",{staticClass:"right"},[e("div",{staticStyle:{"margin-bottom":"5px"}},[e("el-button",{attrs:{size:"small",type:"success",icon:"el-icon-upload"},on:{click:function(e){return t.orderItemExportExcel("")}}},[t._v("Excel 导出")])],1),e("div",[e("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1)])]),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.orderItemList,height:t.tableHeight,border:""}},[t._e(),"2"!=t.$route.query.country?e("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return"2"!=t.$route.query.country?[t._v("\n            "+t._s(t.getDictItemName(e.row.source))+"\n          ")]:void 0}}],null,!0)}):t._e(),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"200"}}),e("el-table-column",{attrs:{prop:"orderNum",label:"订单号",width:"150"}}),e("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"submitTime",label:"采购时间",formatter:t.time,width:"150"}}),e("el-table-column",{attrs:{prop:"ORDER_ITEM_STATUS",label:"发货状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return["0"==a.row.orderItemStatus?e("div",[e("el-tag",{attrs:{type:"info"}},[t._v("待确认")])],1):t._e(),"1"==a.row.orderItemStatus?e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("待发货")])],1):t._e(),"2"==a.row.orderItemStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分发货")])],1):t._e(),"3"==a.row.orderItemStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已发货")])],1):t._e(),"4"==a.row.orderItemStatus?e("div",[e("el-tag",{attrs:{type:"primary"}},[t._v("已完成")])],1):t._e(),"5"==a.row.orderItemStatus?e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("已取消")])],1):t._e(),a.row.orderItemStatus&&"6"!=a.row.orderItemStatus?t._e():e("div",[e("el-tag",{attrs:{type:"info"}},[t._v("无")])],1)]}}])}),e("el-table-column",{attrs:{prop:"stockStatus",label:"入库状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.stockStatus&&"0"!=a.row.stockStatus?t._e():e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("未入库")])],1),"1"==a.row.stockStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已入库")])],1):t._e(),"2"==a.row.stockStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分入库")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"payStatus",label:"支付状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.payStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已支付")])],1):t._e(),"2"==a.row.payStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分支付")])],1):t._e(),"0"!=a.row.payStatus&&a.row.payStatus||a.row.stockStatus&&"0"!=a.row.stockStatus?t._e():e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("未支付")])],1),"0"!=a.row.payStatus&&a.row.payStatus||!a.row.stockStatus||"0"==a.row.stockStatus?t._e():e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("未支付")])],1)]}}])}),"1"==t.$route.query.country?e("el-table-column",{attrs:{prop:"countryBatch",label:"批次",width:"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(t.formatBatch(e.row.countryBatch))+"\n          ")]}}],null,!1,4068207063)}):t._e(),e("el-table-column",{attrs:{prop:"specs",label:"规格","show-overflow-tooltip":""}}),e("el-table-column",{attrs:{prop:"packingSpecs",label:"包装规格"}}),e("el-table-column",{attrs:{prop:"dosageForm",label:"剂型","show-overflow-tooltip":"",width:"100"}}),e("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"190","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"standardCode",label:"药品本位码",width:"190","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"medicalInsuranceCode",label:"国家医保码",width:"190","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"unitPrice",label:"最小单位价(元)",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(t.getPrice(e.row.packingSpecs,e.row.unitPrice))+"\n          ")]}}])}),e("el-table-column",{attrs:{prop:"unitPrice",label:"最小单位数量",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(t.getPriceAmount(e.row.packingSpecs,e.row.amount))+"\n          ")]}}])}),e("el-table-column",{attrs:{prop:"unitPrice",label:"商品价(元)",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(e.row.unitPrice)+"\n          ")]}}])}),e("el-table-column",{attrs:{prop:"amount",label:"采购数量",width:"100"}}),e("el-table-column",{attrs:{prop:"itemPrice",label:"合计(元)",width:"130"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(e.row.itemPrice)+"\n          ")]}}])}),"1"!=t.$route.query.country?e("el-table-column",{attrs:{prop:"systemContrast",width:"150",label:"系统推荐",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return"1"!=t.$route.query.country?["1"==a.row.systemContrast?e("span",[t._v("是")]):t._e(),"0"==a.row.systemContrast?e("div",[e("el-popover",{attrs:{placement:"left",width:"250",trigger:"hover"}},[a.row.reason?e("div",{staticClass:"text item"},[t._v("\n                  "+t._s(a.row.reason)+"\n                ")]):e("div",{staticClass:"text item"},[t._v("\n                  无\n                ")]),e("el-tag",{attrs:{slot:"reference",type:"danger"},slot:"reference"},[t._v("否(查看原因)")])],1)],1):t._e()]:void 0}}],null,!0)}):t._e()],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)])],1)},e.staticRenderFns=[]},mLCN:function(t,e,a){"use strict";var s=a("h9Sk");a.o(s,"render")&&a.d(e,"render",function(){return s.render}),a.o(s,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return s.staticRenderFns})},rGKd:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var s=l(a("Q9c5")),r=l(a("ERIh"));function l(t){return t&&t.__esModule?t:{default:t}}var o=s.default.baseContext+"/supervise/supDrugBatch/getBatchList";var i={getBatchList:function(t){r.default.$http_api("GET",o,{},{jsonContentType:!1,responseType:null,addBaseUrl:!0}).then(function(e){1==e.state?function(t){return null!=t&&void 0!==t&&"function"==typeof t}(t)&&t(e):console.warn("查询集采批次失败",e.message)}).catch(function(t){console.warn("查询集采批次失败",t.message)})}};e.default=i},tm9F:function(t,e,a){}}]);