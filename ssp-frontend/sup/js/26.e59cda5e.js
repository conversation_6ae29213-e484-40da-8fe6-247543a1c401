(window.webpackJsonp=window.webpackJsonp||[]).push([[26],{"+VWq":function(e,t,a){"use strict";a.r(t);var i=a("g/JD"),s=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,function(){return i[e]})}(o);t.default=s.a},"1nK7":function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{staticStyle:{height:"500px"}},[t("quill-editor",{ref:"myQuillEditor",staticStyle:{height:"460px"},attrs:{disabled:e.isDisable,options:e.editorOption},on:{change:e.setHtmlVale},model:{value:e.htmlEditor,callback:function(t){e.htmlEditor=t},expression:"htmlEditor"}},[t("div",{attrs:{slot:"toolbar",id:"toolbar"},slot:"toolbar"},[t("button",{staticClass:"ql-bold",attrs:{title:"加粗"}},[e._v("Bold")]),t("button",{staticClass:"ql-italic",attrs:{title:"斜体"}},[e._v("Italic")]),t("button",{staticClass:"ql-underline",attrs:{title:"下划线"}},[e._v("underline")]),t("button",{staticClass:"ql-strike",attrs:{title:"删除线"}},[e._v("strike")]),t("button",{staticClass:"ql-blockquote",attrs:{title:"引用"}}),t("button",{staticClass:"ql-header",attrs:{value:"1",title:"标题1"}}),t("button",{staticClass:"ql-header",attrs:{value:"2",title:"标题2"}}),t("button",{staticClass:"ql-list",attrs:{value:"ordered",title:"有序列表"}}),t("button",{staticClass:"ql-list",attrs:{value:"bullet",title:"无序列表"}}),t("select",{staticClass:"ql-header",attrs:{title:"段落格式"}},[t("option",{attrs:{selected:""}},[e._v("段落")]),t("option",{attrs:{value:"1"}},[e._v("标题1")]),t("option",{attrs:{value:"2"}},[e._v("标题2")]),t("option",{attrs:{value:"3"}},[e._v("标题3")]),t("option",{attrs:{value:"4"}},[e._v("标题4")]),t("option",{attrs:{value:"5"}},[e._v("标题5")]),t("option",{attrs:{value:"6"}},[e._v("标题6")])]),t("select",{staticClass:"ql-size",attrs:{title:"字体大小"}},[t("option",{attrs:{value:"10px"}},[e._v("10px")]),t("option",{attrs:{value:"12px"}},[e._v("12px")]),t("option",{attrs:{value:"14px"}},[e._v("14px")]),t("option",{attrs:{value:"16px",selected:""}},[e._v("16px")]),t("option",{attrs:{value:"18px"}},[e._v("18px")]),t("option",{attrs:{value:"20px"}},[e._v("20px")])]),t("select",{staticClass:"ql-font",attrs:{title:"字体"}},[t("option",{attrs:{value:"SimSun"}},[e._v("宋体")]),t("option",{attrs:{value:"SimHei"}},[e._v("黑体")]),t("option",{attrs:{value:"Microsoft-YaHei"}},[e._v("微软雅黑")]),t("option",{attrs:{value:"KaiTi"}},[e._v("楷体")]),t("option",{attrs:{value:"FangSong"}},[e._v("仿宋")]),t("option",{attrs:{value:"Arial"}},[e._v("Arial")])]),t("select",{staticClass:"ql-color",attrs:{value:"color",title:"字体颜色"}}),t("select",{staticClass:"ql-background",attrs:{value:"background",title:"背景颜色"}}),t("select",{staticClass:"ql-align",attrs:{value:"align",title:"对齐"}}),t("button",{staticClass:"ql-image",attrs:{title:"图片"}})])])],1)},t.staticRenderFns=[]},"2xaP":function(e,t,a){"use strict";a("YSMq")},MBge:function(e,t,a){},R453:function(e,t,a){"use strict";a("MBge")},Xj9Q:function(e,t,a){"use strict";a.r(t);var i=a("dz8k"),s=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,function(){return i[e]})}(o);t.default=s.a},YPw9:function(e,t,a){"use strict";var i=a("1nK7");a.o(i,"render")&&a.d(t,"render",function(){return i.render}),a.o(i,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return i.staticRenderFns})},YSMq:function(e,t,a){},"cJn/":function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"on-operating"},[t("el-button",{attrs:{type:"info",icon:"el-icon-arrow-left"},on:{click:e.goBack}},[e._v("返回")]),e.showBtn?t("div",[t("el-button",{attrs:{type:"warning",icon:"el-icon-document"},on:{click:function(t){return e.saveOfficialData("0")}}},[e._v("暂存\n            ")]),t("el-button",{attrs:{type:"primary",icon:"el-icon-s-promotion"},on:{click:function(t){return e.saveOfficialData("1")}}},[e._v("发送\n            ")])],1):e._e()],1),"show"!=e.$route.query.editType||e.$route.query.updateIsSee?e._e():t("el-tabs",{staticStyle:{"margin-left":"10px","margin-right":"10px"},on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[t("el-tab-pane",{attrs:{label:"公文信息",name:"first"}}),t("el-tab-pane",{attrs:{label:"查阅情况",name:"second"}})],1),"add"==e.$route.query.editType?t("el-tabs",{staticStyle:{"margin-left":"10px","margin-right":"10px"},on:{"tab-click":e.handleTabClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[t("el-tab-pane",{attrs:{label:"普通发文",name:"first"}}),t("el-tab-pane",{attrs:{label:"智能发文",name:"second"}})],1):e._e(),"first"==e.activeName||"first"==e.activeTab?t("div",{staticClass:"form"},[t("el-form",{ref:"officialData",staticClass:"item-form",attrs:{rules:e.rules,model:e.officialData,"label-position":"left","label-width":"110px"}},[t("div",{staticClass:"fromBox"},[e.isDisable?t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"发件人",prop:"title"}},[t("el-input",{attrs:{readonly:!0},model:{value:e.officialData.sendUserName,callback:function(t){e.$set(e.officialData,"sendUserName",t)},expression:"officialData.sendUserName"}})],1)],1)],1):e._e(),"first"==e.activeTab||e.isDisable?t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"收件人",prop:"receiveUserNames"}},[t("el-input",{attrs:{readonly:!0,placeholder:"请选择收件人"},model:{value:e.officialData.receiveUserNames,callback:function(t){e.$set(e.officialData,"receiveUserNames",t)},expression:"officialData.receiveUserNames"}},[e.isDisable?e._e():t("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.selectUser},slot:"append"},[e._v("选择收件人\n                                ")])],1)],1)],1)],1):e._e(),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"标题",prop:"title"}},[t("el-input",{attrs:{readonly:e.isDisable,placeholder:"请填写标题"},model:{value:e.officialData.title,callback:function(t){e.$set(e.officialData,"title",t)},expression:"officialData.title"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"上传附件"}},[t("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{disabled:e.isDisable,headers:e.headers,action:e.uploadUrl,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"before-upload":e.beforeUpload,"before-remove":e.beforeRemove,"on-success":e.onSuccess,"on-error":e.onError,accept:".gif,.jpg,.jpeg,.bmp,.png,.doc,.docx,.txt,.xlsx,.xls,.pdf",multiple:"","file-list":e.fileList}},[e.isDisable?e._e():t("el-button",{attrs:{size:"small"}},[e._v("点击上传")])],1)],1)],1)],1),"second"!=e.activeTab||e.isDisable?e._e():t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"附件分类"}},[e.isDisable||"second"!=e.activeTab?e._e():t("el-button",{staticStyle:{"margin-left":"10px","margin-bottom":"10px"},attrs:{size:"small",type:"primary"},on:{click:e.showFileResult}},[e._v("刷新附件分类")]),t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.fileData,border:"","header-cell-style":{padding:"0px"},"highlight-current-row":""}},[t("el-table-column",{attrs:{label:"医疗机构",prop:"hospitalName",width:"250px"}}),t("el-table-column",{attrs:{label:"附件",prop:"docInfo"},scopedSlots:e._u([{key:"default",fn:function(a){return e._l(a.row.docInfo,function(a,i){return t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.downloadFile(a.docId,a.name)}}},[e._v("\n                                            "+e._s(a.name)+"\n                                        ")])})}}],null,!1,3776790686)}),t("el-table-column",{attrs:{label:"收件人名称",prop:"receiveUserNames"}})],1)],1)],1)],1),t("el-row",[t("el-col",[t("el-form-item",{attrs:{label:"内容"}},[t("uiEditor",{ref:"uiEditor",tag:"component",attrs:{value:e.officialData.content,isDisable:e.isDisable}})],1)],1)],1)],1)])],1):e._e(),"second"==e.activeName?t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"},[t("span",[e._v("查阅状态")]),t("el-select",{attrs:{placeholder:"请选择查阅状态"},model:{value:e.params.isSee,callback:function(t){e.$set(e.params,"isSee",t)},expression:"params.isSee"}},[t("el-option",{attrs:{label:"已查阅",value:"1"}}),t("el-option",{attrs:{label:"未查阅",value:"0"}})],1),t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1)]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.tableDataList,"highlight-current-row":""},on:{"current-change":e.handleCurrentChange}},[t("el-table-column",{attrs:{label:"名称",prop:"userName",align:"left"}}),t("el-table-column",{attrs:{label:"所属区划",prop:"deptName"}}),t("el-table-column",{attrs:{label:"查阅状态",prop:"isSee"},scopedSlots:e._u([{key:"default",fn:function(a){return["0"==a.row.isSee?t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未查阅")])],1):e._e(),"1"==a.row.isSee?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已查阅")])],1):e._e()]}}],null,!1,820948885)}),t("el-table-column",{attrs:{label:"查阅时间",prop:"seeTime",formatter:e.time,align:"right"}})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)])],1):e._e(),t("el-dialog",{attrs:{title:"选择收件人",visible:e.show,width:"60%"},on:{close:function(t){e.show=!1}}},[t("div",{staticClass:"dialog-content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"},[t("span",[e._v("用户名称")]),t("el-input",{attrs:{placeholder:"请输入用户名称"},model:{value:e.relationParams.name,callback:function(t){e.$set(e.relationParams,"name",t)},expression:"relationParams.name"}}),t("el-button",{attrs:{icon:"el-icon-delete"},on:{click:e.reset}},[e._v("重置")]),t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.search}},[e._v("查询")])],1)]),t("el-table",{ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:e.receiveUserList,"header-cell-style":{background:"#f5f7fa"},"max-height":"400","row-key":e.getRowKeys},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection","reserve-selection":!0}}),t("el-table-column",{attrs:{label:"用户名称",prop:"name",align:"left"}}),t("el-table-column",{attrs:{label:"所属区划",prop:"deptName",align:"right"}})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{"current-page":e.relationParams.page,"page-size":e.relationParams.limit,layout:"total,prev, pager, next, jumper",total:e.relationParams.total},on:{"current-change":e.handleCurrentChange,"update:currentPage":function(t){return e.$set(e.relationParams,"page",t)},"update:current-page":function(t){return e.$set(e.relationParams,"page",t)}}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[[t("el-button",{attrs:{type:"primary"},on:{click:e.chooseUser}},[e._v("确定")]),t("el-button",{on:{click:e.cancel}},[e._v("取消")])]],2)])],1)},t.staticRenderFns=[]},dz8k:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var i=a("+RH8");a("rUV2");var s=a("uIHo");a("EhoX"),a("fkE9"),a("lr6j");var o=a("So8+");a("kAC2"),s.Quill.register("modules/imageDrop",o.ImageDrop);var l=s.Quill.import("attributors/style/size");l.whitelist=["10px","12px","14px","16px","18px","20px"],s.Quill.register(l,!0);var r=s.Quill.import("formats/font");r.whitelist=["SimSun","SimHei","Microsoft-YaHei","KaiTi","FangSong","Arial","Times-New-Roman","sans-serif","宋体","黑体"],s.Quill.register(r,!0),t.default={name:"FuncFormsEdit",components:{quillEditor:s.quillEditor,mavonEditor:i.mavonEditor},data:function(){return{htmlEditor:this.value,valueData:this.value,toolbars:{bold:!0,italic:!0,header:!0,underline:!0,strikethrough:!0,mark:!0,superscript:!0,subscript:!0,quote:!0,ol:!0,ul:!0,link:!0,code:!1,table:!0,fullscreen:!0,readmodel:!0,htmlcode:!0,help:!0,undo:!0,redo:!0,trash:!0,save:!1,navigation:!0,alignleft:!0,aligncenter:!0,alignright:!0,subfield:!0,preview:!0},editorOption:{placeholder:"开始编辑",theme:"snow",modules:{imageDrop:!0,toolbar:{container:"#toolbar"}}}}},mounted:function(){},props:{isDisable:{type:Boolean,default:function(){return!1}},value:{type:String,default:""}},watch:{value:function(e){e&&this.initData()}},methods:{initData:function(){this.htmlEditor=this.value,this.valueData=this.value},setHtmlVale:function(){this.valueData=this.htmlEditor}}}},"g/JD":function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var i=n(a("omC7")),s=n(a("iJIm")),o=n(a("XRYr")),l=n(a("Q9c5")),r=n(a("DWNM"));function n(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[r.default],name:"sendPage",components:{uiEditor:s.default},data:function(){return{fileData:[],activeTab:"first",activeName:"first",tableDataList:[],tableHeight:100,params:{page:1,limit:10,records:0,isSee:"",officialId:""},show:!1,receiveUserList:[],relationParams:{page:1,limit:10,total:0,name:""},select:[],rLoading:{},fileList:[],headers:{},uploadData:{},officialData:{id:"",title:"",content:"",sendUserList:[],docIdList:[],docInfo:"",status:"",receiveUserIds:[],receiveUserNames:"",docList:[]},downloadUrl:l.default.baseContext+"/file/download",showBtn:!0,isDisable:!1,officialId:"",rules:{receiveUserNames:[{required:!0,message:"请选择收件人",trigger:"blur"}]}}},mounted:function(){var e=o.default.doCloundRequest(l.default.app_key,l.default.app_security,"");this.headers["x-aep-appkey"]=e["x-aep-appkey"],this.headers["x-aep-signature"]=e["x-aep-signature"],this.headers["x-aep-timestamp"]=e["x-aep-timestamp"],this.headers["x-aep-nonce"]=e["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,this.onQueryUserList(),this.$route.query.id&&(this.officialId=this.$route.query.id),this.$route.query.updateIsSee&&this.updateIsSee(this.$route.query.updateIsSee),"show"==this.$route.query.editType?(this.showBtn=!1,this.isDisable=!0,this.queryOfficialData()):"edit"==this.$route.query.editType?(this.showBtn=!0,this.isDisable=!1,this.queryOfficialData()):"add"==this.$route.query.editType&&(this.showBtn=!0,this.isDisable=!1,this.officialData={id:"",title:"",content:"",sendUserId:"",sendUserName:"",docIdList:[],docInfo:"",status:"",receiveUserIds:[],receiveUserNames:""},this.fileList=[])},computed:{uploadUrl:function(){return l.default.baseContext+"/file/upload"}},methods:{downloadFile:function(e,t){var a=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,i=document.createElement("a");i.href=a,document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(a)},cancel:function(){this.$refs.multipleTable.clearSelection(),this.show=!1},getRowKeys:function(e){return e.id},handleClick:function(e,t){this.activeName=e.name,this.activeTab="1","second"==this.activeName&&this.onQuerySee()},handleTabClick:function(e,t){this.activeName="first",this.activeTab=e.name},onSearch:function(e){"reset"==e?(this.params.isSee="",this.onQuerySee()):""!=this.params.isSee?(this.params.page=1,this.onQuerySee()):this.$message.warning("请选择状态查询")},onPageClick:function(e){this.params.page=e,this.onQuerySee()},onQuerySee:function(){var e=this;this.params.officialId=this.officialId,this.$http_post(l.default.baseContext+"/supervise/supOfficialReceive/getReceiveList",this.params).then(function(t){if(1!=t.state)return!1;var a=t.rows;e.$set(e,"tableDataList",a),e.params.records=t.records})},selectUser:function(){this.show=!0,this.$nextTick(function(){var e=this;this.receiveUserList.forEach(function(t){e.officialData.receiveUserIds.indexOf(t.id)>=0&&e.$refs.multipleTable.toggleRowSelection(t,!0)})})},onQueryUserList:function(){var e=this;this.$http_post(l.default.baseContext+"/bsp/pubUser/query",this.relationParams).then(function(t){if(e.receiveUserList=[],1!=t.state)return!1;var a=t.rows;e.$set(e,"receiveUserList",a),e.relationParams.total=t.records})},time:function(e,t){if(void 0==e.seeTime||""==e.seeTime)return"";var a=new Date(e.seeTime);return a.getFullYear()+"-"+((a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-")+(a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ")+(a.getHours()<10?"0"+a.getHours()+":":a.getHours()+":")+(a.getMinutes()<10?"0"+a.getMinutes()+":":a.getMinutes()+":")+(a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds())},search:function(){this.onQueryUserList()},reset:function(){this.relationParams.name="",this.onQueryUserList()},handleCurrentChange:function(e){this.relationParams.page=e,this.onQueryUserList()},handleSelectionChange:function(e){this.select=e},chooseUser:function(){var e=[];if(0!=this.select){for(var t="",a=0;a<this.select.length;a++)e.push(this.select[a].id),a==this.select.length-1?t+=this.select[a].name:t+=this.select[a].name+"、";this.officialData.receiveUserIds=e,this.officialData.receiveUserNames=t,this.show=!1}else this.$message.error("请至少选择一个收件人！")},updateIsSee:function(e){var t=this,a=this.openLoading();this.$http_post(l.default.baseContext+"/supervise/supOfficialReceive/see/"+e,null).then(function(e){a.close(),1==e.state?a.close():(a.close(),t.$message.error(e.message))})},queryOfficialData:function(){var e=this,t=this.openLoading("查询中"),a=l.default.baseContext+"/supervise/supOfficial/info/"+this.officialId;this.$http_post(a,{}).then(function(a){1==a.state?(e.$set(e,"officialData",a.row),e.officialData.docIdList=JSON.parse(a.row.docInfo),e.fileList=JSON.parse(a.row.docInfo),t.close()):(t.close(),e.$alert(a.message))})},showFileResult:function(){var e=this;if(0==this.officialData.docIdList.length)this.$message.warning("请先上传附件");else{var t={docList:this.officialData.docIdList},a=this.openLoading("加载中...");this.$http_post(l.default.baseContext+"/supervise/supOfficial/showFileResult",t,!0).then(function(t){if(1==t.state){var i=t.rows;e.$set(e,"fileData",i),a.close()}else e.$message.error(t.message),a.close()})}},saveOfficialData:function(e){var t=this;if("first"==this.activeTab&&(this.officialData.content=this.$refs.uiEditor.valueData,this.$refs.officialData.validate(function(a){if(a||"0"==e){t.officialData.status=e,t.officialData.docInfo=(0,i.default)(t.officialData.docIdList);var s=t.openLoading("提交中...");t.$http_post(l.default.baseContext+"/supervise/supOfficial/edit",t.officialData,!0).then(function(a){if(1==a.state){var i=a.row;"0"==e?(t.officialData.id=i.id,t.$message.success("暂存成功")):"1"==e&&(t.officialData.id=i.id,t.$message.success("发送成功")),s.close()}else t.$message.error(a.message),s.close()})}})),"second"==this.activeTab)if(0==this.officialData.docIdList.length)this.$message.warning("请先上传附件");else{this.officialData.content=this.$refs.uiEditor.valueData,this.officialData.status=e,this.fileData.filter(function(e){e.docInfo=(0,i.default)(e.docInfo)}),this.officialData.docList=this.fileData;var a=this.openLoading("提交中...");this.$http_post(l.default.baseContext+"/supervise/supOfficial/smartPost",this.officialData,!0).then(function(i){1==i.state?(a.close(),"0"==e?t.$message.success("暂存成功"):"1"==e&&t.$message.success("发送成功"),t.$router.push({name:"sendList"})):(t.$message.error(i.message),a.close())})}},onSuccess:function(e,t,a){if(console.log(e),"1"!=e.state)return this.$message.error(e.message),this.$refs.upload.clearFiles(),!1;t.docId=e.row.id;var i={docId:e.row.id,name:e.row.name};this.officialData.docIdList.push(i)},onError:function(e,t,a){this.rLoading.close(),console.log(e),this.$message.error("上传失败,请重试")},handlePreview:function(e){var t=this.downloadUrl+"?docId="+encodeURI(encodeURI(e.docId))+"&fileName="+e.name;console.log(e);var a=document.createElement("a");a.href=t,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(t)},beforeUpload:function(e){return!(e.size/1024/1024>10)||(this.$message.error("上传文件过大，请分开上传，单个文件最大为10M。"),!1)},beforeRemove:function(e,t){return!this.beforeUpload(e)||this.$confirm("确定移除 "+e.name+"？")},handleRemove:function(e,t){var a=this;this.officialData.docIdList.some(function(t,i){if(t.docId==e.docId)return a.officialData.docIdList.splice(i,1),!0}),console.log(e.docId,e,t),this.showFileResult()},goBack:function(){"show"==this.$route.query.editType&&this.$route.query.updateIsSee&&!this.$route.query.isSee?this.$router.push({path:"/official/receiveList"}):"0"==this.$route.query.isSee?this.$router.push({path:"/official/receiveList",query:{isSee:"0"}}):this.$router.push({path:"/official/sendList"})}}}},hVxj:function(e,t,a){"use strict";a.r(t);var i=a("moXY"),s=a("+VWq");for(var o in s)["default"].indexOf(o)<0&&function(e){a.d(t,e,function(){return s[e]})}(o);a("2xaP");var l=a("gp09"),r=Object(l.a)(s.default,i.render,i.staticRenderFns,!1,null,"7813bb35",null);t.default=r.exports},iJIm:function(e,t,a){"use strict";a.r(t);var i=a("YPw9"),s=a("Xj9Q");for(var o in s)["default"].indexOf(o)<0&&function(e){a.d(t,e,function(){return s[e]})}(o);a("R453");var l=a("gp09"),r=Object(l.a)(s.default,i.render,i.staticRenderFns,!1,null,null,null);t.default=r.exports},kAC2:function(e,t,a){},moXY:function(e,t,a){"use strict";var i=a("cJn/");a.o(i,"render")&&a.d(t,"render",function(){return i.render}),a.o(i,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return i.staticRenderFns})}}]);