(window.webpackJsonp=window.webpackJsonp||[]).push([[11],{"+hT0":function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var n=s(a("omC7")),o=s(a("D+Rp")),i=s(a("RUFv")),r=s(a("Q9c5")),l=s(a("DWNM"));function s(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[l.default],name:"paymentTodoDetail2",data:function(){return{activeName:"first",drugSourceList:[],curTab:"0",dialogVisible:!1,paymentData:{data:{},items:[]},submitDataForm:{data:{},items:[],hospital:{},opinion:""},checkedItems:[],confirmShow:!1,listLoading:!1,tableHeight:100,checkedAllFlag:"",opinionShow:!1,opinionDataForm:{allType:"",item:"",opinion:""},hospitalAdmin:!1}},components:{itemDetail:o.default,process:i.default},filters:{formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}},watch:{confirmShow:function(t,e){var a=this,n=0,o=0,i=0,r=0,l="";this.submitDataForm.items.forEach(function(t,e){"3"==t.status&&(n++,o=a.numAdd(o,t.taxesAmount),l+="[明细编号："+t.itemNum+"不通过，原因："+t.opinion+"] "),i++,r=a.numAdd(r,t.taxesAmount)}),this.submitDataForm.data.count=i,this.submitDataForm.data.totalPrice=r,this.submitDataForm.data.backCount=n,this.submitDataForm.data.backPrice=o,this.submitDataForm.opinion=l||"同意"}},mounted:function(){this.$route.query.paymentId&&(this.getPaymentDetail(),this.initRole(),this.getDictItem("SOURCE"),this.getDictItem("WARNING"))},methods:{goBack:function(){this.$router.go(-1)},submit:function(){this.submitDataForm.items=JSON.parse((0,n.default)(this.paymentData.items));for(var t=0;t<this.submitDataForm.items.length;t++){if(!this.submitDataForm.items[t].checked)return void this.$message({type:"error",message:"请勾选支付单单明细是否通过！"})}this.submitDataForm.data=JSON.parse((0,n.default)(this.paymentData.data)),this.confirmShow=!0},confirmOpinion:function(){var t=this;this.opinionDataForm.opinion?("1"==this.opinionDataForm.allType&&(this.paymentData.items.forEach(function(e,a){e.status="3",e.opinion=t.opinionDataForm.opinion}),this.opinionShow=!1,this.submit()),"0"==this.opinionDataForm.allType&&(this.paymentData.items[this.opinionDataForm.index].status="3",this.paymentData.items[this.opinionDataForm.index].opinion=this.opinionDataForm.opinion,this.opinionShow=!1),this.opinionDataForm={allType:"",item:"",opinion:""}):this.$message({type:"error",message:"不通过请填写意见！"})},checkedAll:function(){"0"==this.checkedAllFlag?(this.paymentData.items.forEach(function(t,e){t.checked="3"}),this.opinionDataForm.allType="1",this.opinionShow=!0):(this.paymentData.items.forEach(function(t,e){t.checked="1",t.status="1",t.opinion="同意"}),this.submit()),console.log(this.paymentData.items)},itemCheck:function(t,e){this.checkedAllFlag="","3"==t.checked?(this.opinionDataForm.allType="0",this.opinionDataForm.index=e,this.opinionShow=!0):(this.opinionDataForm.allType="",this.opinionDataForm.index="",this.opinionDataForm.opinion="",this.paymentData.items[e].status="1",this.paymentData.items[e].opinion="同意",this.opinionShow=!1),console.log("-----\x3e",t)},saveNextData:function(){for(var t=this,e=0;e<this.submitDataForm.items.length;e++){var a=this.submitDataForm.items[e];if("3"==a.status&&!a.opinion)return void this.$message({type:"error",message:"不通过请填写意见！"})}if(this.submitDataForm.opinion){console.log(this.submitDataForm);var n=this.openLoading("提交中"),o=r.default.baseContext+"/supervise/supSettlement/nextData";this.$http_post(o,this.submitDataForm,!0).then(function(e){1==e.state?(t.$message({type:"success",message:"提交成功！"}),n.close(),t.$router.push({name:"settlementTodo"})):(n.close(),t.$alert(e.message))})}else this.$message({type:"error",message:"请填写意见！"})},handleClick:function(t,e){this.curTab=t.index},getPaymentDetail:function(){var t=this,e=this.openLoading("查询中"),a=r.default.baseContext+"/supervise/supPayment/show/"+this.$route.query.paymentId;this.$http_post(a,{}).then(function(a){1==a.state?(t.paymentData=a.row,t.submitDataForm.hospital.id=a.row.data.hospitalId,t.submitDataForm.hospital.name=a.row.data.hospitalName,console.log(t.paymentData),e.close()):(e.close(),t.$alert(a.message))})},getDictItemName:function(t){for(var e=0;e<this.drugSourceList.length;e++)if(this.drugSourceList[e].value==t)return this.drugSourceList[e].label},getDictItem:function(t){var e=this,a=this.openLoading();this.$http_post(r.default.baseContext+"/supervise/supDictItem/getItemList",{code:t}).then(function(n){var o=n.rows;1==n.state?("SOURCE"==t&&(e.drugSourceList=o),"WARNING"==t&&(e.warningData=o),a.close()):(a.close(),e.$message.error(n.message))})},initRole:function(t){-1!=this.$store.getters.curUser.roleCode.indexOf("HOSPITAL_ADMIN")&&(this.hospitalAdmin=!0)},numAdd:function(t,e){var a,n,o,i;try{a=t.toString().split(".")[1].length}catch(t){a=0}try{n=e.toString().split(".")[1].length}catch(t){n=0}if(i=Math.abs(a-n),o=Math.pow(10,Math.max(a,n)),i>0){var r=Math.pow(10,i);a>n?(t=Number(t.toString().replace(".","")),e=Number(e.toString().replace(".",""))*r):(t=Number(t.toString().replace(".",""))*r,e=Number(e.toString().replace(".","")))}else t=Number(t.toString().replace(".","")),e=Number(e.toString().replace(".",""));return(t+e)/o}}}},"1DhX":function(t,e,a){},"48vQ":function(t,e,a){"use strict";a.r(e);var n=a("r3Vc"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,function(){return n[t]})}(i);e.default=o.a},BP1K:function(t,e,a){"use strict";a("UqiM")},BVIF:function(t,e,a){"use strict";a.r(e);var n=a("FJKn"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,function(){return n[t]})}(i);e.default=o.a},"D+Rp":function(t,e,a){"use strict";a.r(e);var n=a("xYo7"),o=a("BVIF");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,function(){return o[t]})}(i);a("BP1K");var r=a("gp09"),l=Object(r.a)(o.default,n.render,n.staticRenderFns,!1,null,"688832c6",null);e.default=l.exports},E1wh:function(t,e,a){"use strict";a("zzNX")},FJKn:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var n=i(a("DWNM")),o=i(a("Q9c5"));function i(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[n.default],name:"reconcileItem-list",data:function(){return{warningData:[],drugSourceList:[],listLoading:!1,orderItemId:"",itemCourseShow:!1,itemCourseArr:[]}},props:{itemArr:{type:Array,default:function(){return[]}}},mounted:function(){this.getDictItem("SOURCE"),this.getDictItem("WARNING")},components:{},methods:{itemCourse:function(t){var e=this,a=this.openLoading();this.$http_get(o.default.baseContext+"/supervise/supSettlement/itemCourse/"+t.settlementItemId).then(function(t){t.rows;1==t.state?(e.itemCourseShow=!0,e.itemCourseArr=t.rows,a.close()):(a.close(),e.$message.error(t.message))})},getWaringType:function(t){for(var e=0;e<this.warningData.length;e++)if(this.warningData[e].value==t)return this.warningData[e].label},getWaring:function(t){for(var e=[],a=t.split(","),n=0;n<a.length;n++){var o=this.getWaringType(a[n]);o&&e.push({name:o})}return e},getPrice:function(t,e){if(t){var a=t.match(/\d+/g);return e?(e/a.join("")).toFixed(3):""}return""},getDictItemName:function(t){for(var e=0;e<this.drugSourceList.length;e++)if(this.drugSourceList[e].value==t)return this.drugSourceList[e].label},getDictItem:function(t){var e=this,a=this.openLoading();this.$http_post(o.default.baseContext+"/supervise/supDictItem/getItemList",{code:t}).then(function(n){var o=n.rows;1==n.state?("SOURCE"==t&&(e.drugSourceList=o),"WARNING"==t&&(e.warningData=o),a.close()):(a.close(),e.$message.error(n.message))})},close:function(){this.$refs.asyncDialog.dialogVisible=!1}},filters:{formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}}}},OtTm:function(t,e,a){},RUFv:function(t,e,a){"use strict";a.r(e);var n=a("UCDD"),o=a("48vQ");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,function(){return o[t]})}(i);a("E1wh"),a("Wdxc");var r=a("gp09"),l=Object(r.a)(o.default,n.render,n.staticRenderFns,!1,null,"215df5f7",null);e.default=l.exports},"ScI+":function(t,e,a){"use strict";a("OtTm")},UCDD:function(t,e,a){"use strict";var n=a("ltVK");a.o(n,"render")&&a.d(e,"render",function(){return n.render}),a.o(n,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return n.staticRenderFns})},UqiM:function(t,e,a){},Wdxc:function(t,e,a){"use strict";a("1DhX")},dks4:function(t,e,a){"use strict";var n=a("hKcu");a.o(n,"render")&&a.d(e,"render",function(){return n.render}),a.o(n,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return n.staticRenderFns})},hKcu:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left flex-row"}),"todo"==this.$route.query.type?e("el-button",{attrs:{type:"success",icon:"el-icon-check"},on:{click:function(e){return t.submit()}}},[t._v("提交22")]):t._e(),e("el-button",{attrs:{type:"info",icon:"el-icon-arrow-left"},on:{click:t.goBack}},[t._v("返回")])],1),e("el-tabs",{model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[e("el-tab-pane",{attrs:{label:"支付单信息",name:"first"}})],1),e("div",{staticClass:"form"},[e("el-form",{ref:"dataForm",staticClass:"item-form",attrs:{model:t.paymentData.data,"label-position":"left","label-width":"110px"}},[e("div",{staticClass:"fromBox"},[e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"支付单编号"}},[e("el-input",{attrs:{value:t.paymentData.data.num}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"创建人"}},[e("el-input",{attrs:{value:t.paymentData.data.createName}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"生成时间"}},[e("el-input",{attrs:{value:t._f("formatTime")(t.paymentData.data.creationTime)}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"业务状态"}},["1"==t.paymentData.data.bizStatus?e("el-input",{attrs:{value:"在办"}}):t._e(),"2"==t.paymentData.data.bizStatus?e("el-input",{attrs:{value:"支付单阶段完成"}}):t._e(),"3"==t.paymentData.data.bizStatus?e("el-input",{attrs:{value:"退回办结"}}):t._e()],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"医疗机构"}},[e("el-input",{attrs:{value:t.paymentData.data.hospitalName}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"配送企业"}},[e("el-input",{attrs:{value:t.paymentData.data.deliveryName}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"支付单总笔数"}},[e("el-input",{attrs:{value:t.paymentData.data.count}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"支付单总金额"}},[e("el-input",{attrs:{value:t.paymentData.data.totalPrice}})],1)],1)],1)],1)])],1),e("div",{staticClass:"memberTab",staticStyle:{position:"relative"}},[e("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.curTab,callback:function(e){t.curTab=e},expression:"curTab"}},[e("el-tab-pane",{attrs:{label:"支付单明细信息"}},[e("el-table",{ref:"data_table",staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.paymentData.items,"header-cell-style":{background:"#f5f7fa"},border:""}},[e("el-table-column",{attrs:{type:"index",label:"序号",width:"50"}}),e("el-table-column",{attrs:{prop:"itemNum",label:"明细编号",width:"200"}}),e("el-table-column",{attrs:{prop:"invoiceCode",label:"发票代码",width:"120"}}),e("el-table-column",{attrs:{prop:"invoiceNo",label:"发票号",width:"120"}}),e("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"180",align:"left"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",[t._v("\n                                "+t._s(t.getDictItemName(a.row.source))+"\n                            ")])]}}])}),e("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单号",width:"120"}}),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"150"}}),e("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150"}}),e("el-table-column",{attrs:{prop:"num",label:"开票数量"}}),e("el-table-column",{attrs:{prop:"unitPrice",label:"单价"}}),e("el-table-column",{attrs:{prop:"taxesAmount",label:"金额"}}),e("el-table-column",{attrs:{prop:"orderNum",label:"订单编号",width:"120"}}),e("el-table-column",{attrs:{prop:"country",label:"来源",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                            "+t._s("1"==e.row.country?"国家集采":"2"==e.row.country?"非国家集采":"线下采购")+"\n                        ")]}}])}),e("el-table-column",{attrs:{label:"超时天数",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                            "+t._s(null==e.row.overDay?30:-e.row.overDay)+"\n                        ")]}}])}),e("el-table-column",{attrs:{label:"已通知次数",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                            "+t._s(null==e.row.noticeNum?0:e.row.noticeNum)+"\n                        ")]}}])}),e("el-table-column",{attrs:{label:"详情状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.status?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("在办")])],1):t._e(),"2"==a.row.status?e("div",[e("el-tag",{attrs:{type:"primary"}},[t._v("办结")])],1):t._e(),"3"==a.row.status?e("div",[e("el-tag",{attrs:{type:"error"}},[t._v("退回")])],1):t._e()]}}])}),e("el-table-column",{attrs:{label:"是否确认",align:"center",width:"200",fixed:"right"},scopedSlots:t._u([{key:"header",fn:function(a){return[e("el-radio-group",{on:{change:function(e){return t.checkedAll(a.row)}},model:{value:t.checkedAllFlag,callback:function(e){t.checkedAllFlag=e},expression:"checkedAllFlag"}},[e("el-radio",{attrs:{label:"1"}},[t._v("全通过")]),e("el-radio",{attrs:{label:"0"}},[t._v("全不通过")])],1)]}},{key:"default",fn:function(a){return[e("el-radio-group",{on:{change:function(e){return t.itemCheck(a.row,a.$index)}},model:{value:a.row.checked,callback:function(e){t.$set(a.row,"checked",e)},expression:"scope.row.checked"}},[e("el-radio",{attrs:{label:"1"}},[t._v("通过")]),e("el-radio",{attrs:{label:"3"}},[t._v("不通过")])],1)]}}])})],1)],1),e("el-tab-pane",{attrs:{label:"流程信息"}},[void 0!=t.paymentData.courses?e("process",{ref:"process",attrs:{name:"1",processData:t.paymentData.courses}}):t._e()],1)],1)],1),t.confirmShow?e("el-dialog",{attrs:{title:"确认支付单单",visible:t.confirmShow,"close-on-click-modal":!1,"close-on-press-escape":!1,width:"90%"},on:{"update:visible":function(e){t.confirmShow=e}}},[e("div",{staticClass:"orderBox-dialog-content"},[e("el-form",{ref:"confirmOrderDataForm",staticClass:"item-form",attrs:{id:"confirmOrderData",model:t.submitDataForm,"label-width":"90px"}},[e("div",{staticClass:"orderBox-content"},[e("div",{staticClass:"header-table"},[e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.submitDataForm.items,border:"","highlight-current-row":""}},[e("el-table-column",{attrs:{type:"index",label:"序号",width:"50"}}),e("el-table-column",{attrs:{prop:"itemNum",label:"明细编号",width:"200"}}),e("el-table-column",{attrs:{prop:"invoiceCode",label:"发票代码",width:"120"}}),e("el-table-column",{attrs:{prop:"invoiceNo",label:"发票号",width:"120"}}),e("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"180",align:"left"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",[t._v("\n                                        "+t._s(t.getDictItemName(a.row.source))+"\n                                    ")])]}}],null,!1,1301161043)}),e("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单号",width:"120"}}),e("el-table-column",{attrs:{prop:"deliveryName",label:"配送企业",width:"180"}}),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"150"}}),e("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150"}}),e("el-table-column",{attrs:{prop:"num",label:"开票数量"}}),e("el-table-column",{attrs:{prop:"unitPrice",label:"单价"}}),e("el-table-column",{attrs:{prop:"taxesAmount",label:"金额"}}),e("el-table-column",{attrs:{prop:"orderNum",label:"订单编号",width:"120"}}),e("el-table-column",{attrs:{prop:"country",label:"来源",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                                    "+t._s("1"==e.row.country?"国家集采":"2"==e.row.country?"非国家集采":"线下采购")+"\n                                ")]}}],null,!1,*********)}),e("el-table-column",{attrs:{label:"超时天数",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                                    "+t._s(null==e.row.overDay?30:-e.row.overDay)+"\n                                ")]}}],null,!1,*********)}),e("el-table-column",{attrs:{label:"已通知次数",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                                    "+t._s(null==e.row.noticeNum?0:e.row.noticeNum)+"\n                                ")]}}],null,!1,*********9)}),e("el-table-column",{attrs:{label:"是否通过",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.status?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("通过")])],1):t._e(),"3"==a.row.status?e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("退回")])],1):t._e()]}}],null,!1,618538765)}),e("el-table-column",{attrs:{label:"意见",align:"center",fixed:"right",width:"200px;"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-input",{attrs:{type:"textarea",placeholder:"请输入意见"},model:{value:a.row.opinion,callback:function(e){t.$set(a.row,"opinion",e)},expression:"scope.row.opinion"}})]}}],null,!1,3530832571)})],1)],1),e("div",{staticClass:"fromBox",staticStyle:{"margin-top":"5px"}},[e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"支付单编号"}},[e("el-input",{attrs:{value:t.submitDataForm.data.num,disabled:""}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"医疗机构"}},[e("el-input",{attrs:{disabled:""},model:{value:t.submitDataForm.hospital.name,callback:function(e){t.$set(t.submitDataForm.hospital,"name",e)},expression:"submitDataForm.hospital.name"}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"确认总笔数"}},[e("el-input",{attrs:{value:t.submitDataForm.data.count,disabled:""}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"确认总金额"}},[e("el-input",{attrs:{value:t.submitDataForm.data.totalPrice,disabled:""}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"打回总笔数"}},[e("el-input",{attrs:{value:t.submitDataForm.data.backCount}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"打回总金额"}},[e("el-input",{attrs:{value:t.submitDataForm.data.backPrice}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"意见",prop:"address"}},[e("el-input",{attrs:{type:"textarea",placeholder:"请输入意见"},model:{value:t.submitDataForm.opinion,callback:function(e){t.$set(t.submitDataForm,"opinion",e)},expression:"submitDataForm.opinion"}})],1)],1)],1)],1)])])],1),e("div",{staticClass:"dialog-footer flex-row"},[e("span"),e("span",[e("el-button",{on:{click:function(e){t.confirmShow=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.saveNextData}},[t._v("提 交")])],1)])]):t._e(),t.opinionShow?e("el-dialog",{attrs:{title:"审核意见",visible:t.opinionShow,"show-close":!1,"close-on-click-modal":!1,"close-on-press-escape":!1,width:"50%"},on:{"update:visible":function(e){t.opinionShow=e}}},[e("div",[e("el-form",{ref:"opinionDataForm",staticClass:"item-form",attrs:{id:"opinionData",model:t.opinionDataForm,"label-width":"90px"}},[e("div",{staticClass:"fromBox"},[e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"意见"}},[e("el-input",{attrs:{type:"textarea",placeholder:"请输入意见"},model:{value:t.opinionDataForm.opinion,callback:function(e){t.$set(t.opinionDataForm,"opinion",e)},expression:"opinionDataForm.opinion"}})],1)],1)],1)],1)])],1),e("div",{staticClass:"dialog-footer flex-row"},[e("span"),e("span",[e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.confirmOpinion()}}},[t._v("确 定")])],1)])]):t._e()],1)},e.staticRenderFns=[]},lUaE:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"item-item"},[e("div",{staticClass:"flex-row-default"},[e("div",{staticClass:"box-card box-right"},[e("div",{staticClass:"text item"},[e("el-table",{ref:"data_table",staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.itemArr,"header-cell-style":{background:"#f5f7fa"},border:""}},[e("el-table-column",{attrs:{type:"index",label:"序号",width:"50"}}),e("el-table-column",{attrs:{prop:"itemNum",label:"明细编码",width:"200"}}),e("el-table-column",{attrs:{prop:"invoiceCode",label:"发票代码",width:"120"}}),e("el-table-column",{attrs:{prop:"invoiceNo",label:"发票号",width:"120"}}),e("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"180",align:"left"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",[t._v("\n                                "+t._s(t.getDictItemName(a.row.source))+"\n                            ")])]}}])}),e("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单号",width:"120"}}),e("el-table-column",{attrs:{prop:"deliveryName",label:"配送企业",width:"180"}}),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"150"}}),e("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150"}}),e("el-table-column",{attrs:{prop:"num",label:"开票数量"}}),e("el-table-column",{attrs:{prop:"unitPrice",label:"单价"}}),e("el-table-column",{attrs:{prop:"taxesAmount",label:"金额"}}),e("el-table-column",{attrs:{prop:"orderNum",label:"订单编号",width:"120"}}),e("el-table-column",{attrs:{prop:"country",label:"来源",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                            "+t._s("1"==e.row.country?"国家集采":"2"==e.row.country?"非国家集采":"线下采购")+"\n                        ")]}}])}),e("el-table-column",{attrs:{label:"超时天数",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                            "+t._s(null==e.row.overDay?30:-e.row.overDay)+"\n                        ")]}}])}),e("el-table-column",{attrs:{label:"已通知次数",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                            "+t._s(null==e.row.noticeNum?0:e.row.noticeNum)+"\n                        ")]}}])}),e("el-table-column",{attrs:{label:"详情状态",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.status?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("在办")])],1):t._e(),"2"==a.row.status?e("div",[e("el-tag",{attrs:{type:"primary"}},[t._v("办结")])],1):t._e(),"3"==a.row.status?e("div",[e("el-tag",{attrs:{type:"error"}},[t._v("退回")])],1):t._e()]}}])}),e("el-table-column",{attrs:{label:"明细日志",fixed:"right","min-width":"80px"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{size:"small"},on:{click:function(e){return t.itemCourse(a.row)}}},[t._v("查看")])]}}])})],1)],1)])]),t.itemCourseShow?e("el-dialog",{attrs:{title:"明细日志",visible:t.itemCourseShow,width:"50%"},on:{"update:visible":function(e){t.itemCourseShow=e}}},[e("div",{staticClass:"orderBox-dialog-content"},[e("el-table",{ref:"data_table",staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.itemCourseArr,"header-cell-style":{background:"#f5f7fa"},border:""}},[e("el-table-column",{attrs:{type:"index",label:"序号",width:"50"}}),e("el-table-column",{attrs:{prop:"curNodeName",label:"环节名称"}}),e("el-table-column",{attrs:{prop:"creationTime",label:"操作时间"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t._f("formatTime")(a.row.creationTime)))])]}}],null,!1,3080248501)}),e("el-table-column",{attrs:{prop:"status",label:"审批状态"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.status?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("通过")])],1):t._e(),"1"!=a.row.status?e("div",[e("el-tag",{attrs:{type:"error"}},[t._v("不通过")])],1):t._e()]}}],null,!1,3016323678)}),e("el-table-column",{attrs:{prop:"userName",label:"审批人"}}),e("el-table-column",{attrs:{prop:"opinion",label:"审批意见"}})],1)],1),e("div",{staticClass:"dialog-footer flex-row"},[e("span"),e("span",[e("el-button",{on:{click:function(e){t.itemCourseShow=!1}}},[t._v("取 消")])],1)])]):t._e()],1)},e.staticRenderFns=[]},ltVK:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{staticClass:"view"},[e("h2",[t._v("过程意见")]),e("div",{staticClass:"block process"},[e("el-timeline",t._l(t.processData,function(a,n){return e("el-timeline-item",{key:n,attrs:{placement:"top"}},[e("div",{staticClass:"card-div",staticStyle:{"background-color":"rgb(244 247 250)","padding-top":"10px"}},[e("h3",{staticStyle:{"margin-left":"10px"}},[e("span",[t._v("环节名称:"+t._s(t._f("nodeSplit")(n)))])]),e("el-row",t._l(a,function(a,n){return e("el-col",{key:n,attrs:{span:8}},[e("el-card",[e("h3",[e("i",{staticClass:"iconfont icon-renyuan"}),e("span",[t._v(t._s(a.userName))])]),e("span",[e("i"),t._v("接收时间："+t._s(t._f("formatTime")(a.receiveTime)))]),e("span",[e("i"),t._v("办理时间："+t._s(t._f("formatTime")(a.sendTime)))]),e("p",[t._v(t._s(a.opinion))])])],1)}),1)],1)])}),1)],1)])},e.staticRenderFns=[]},nqWy:function(t,e,a){"use strict";a.r(e);var n=a("+hT0"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,function(){return n[t]})}(i);e.default=o.a},p5R1:function(t,e,a){"use strict";a.r(e);var n=a("dks4"),o=a("nqWy");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,function(){return o[t]})}(i);a("ScI+");var r=a("gp09"),l=Object(r.a)(o.default,n.render,n.staticRenderFns,!1,null,"73b29cd4",null);e.default=l.exports},r3Vc:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default={name:"process",components:{},data:function(){return{}},computed:{},props:{processData:{type:Object,required:!0}},watch:{processData:function(t,e){console.log("----",t)}},methods:{},filters:{nodeSplit:function(t){return t.split(",")[1]},formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())}}}},xYo7:function(t,e,a){"use strict";var n=a("lUaE");a.o(n,"render")&&a.d(e,"render",function(){return n.render}),a.o(n,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return n.staticRenderFns})},zzNX:function(t,e,a){}}]);