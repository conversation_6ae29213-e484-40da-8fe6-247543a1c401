<style type="text/css" src="./index.font.css" />
<style type="text/css" src="./index.vue.css" />
<style type="text/css" src="./theme.css" />
<template>
	<div class="flex flex-v flex-align-center" :class="{appCS:showHeader,app:!showHeader,guide:$route.path == '/guideView'}">
		<head-bar v-if="showHeader"></head-bar>
		<router-view class="flex-1"></router-view>
	</div>
</template>
<script>
	import HeadBar from "./header"
	import BspApi from '../../../api/BspApi'
    import "./index.rem.js"
	export default {
		name: 'App',
		components: {
			HeadBar
		},
        data(){
            return{
                showHeader: true,
        }
        },

		mounted(){
            this.tooggleClass("cms-vip custom-"+this.$store.getters.globalSetting.themeStyle.substr(1));
			BspApi.getUserInfo(this.handleUser);
		},

        methods:{
            //换肤
          tooggleClass(className){
              if(!className){
                  return
              }
              document.body.className = className;
          },
			handleUser(ret){
				if(ret.state == 1){
					var user = ret.row.USER;
					user.menus = ret.row.menus;
					this.$store.dispatch("setUser",user)
					this.isLogin = true;//获取用户之前，不作其他请求。
				}else{
					this.$router.push({
						path: '/login'
					});
					// this.$alert('获取用户信息失败，请重试', '系统提示', {
					//   type: 'error',
					//   confirmButtonText: '确定',
					//   callback: () => {
					//     window.location.reload();
					//   }
					// });
				}
        const currUser=this.$store.getters.curUser;
        if(!currUser || !currUser.menus || !this.$router.canAccess(currUser.menus,this.$route.fullPath)){
          // 禁止访问
          console.error("禁止访问")
          this.$router.push({
            path: '/admin/404',
            query: {}
          });
        }
			},
        }
	}
</script>
<style>
    .app {
        width: 19.2rem;
        /*height: 9.63rem;*/
        height:100%;
        overflow: hidden;
        position: relative;
        background: url("../../../images/desktop/bg.jpg") no-repeat;
        background-size:  100% auto;
    }
    .appCS{
        width: 19.2rem;
        height:100%;
        overflow: hidden;
        position: relative;
        background: url("../../../images/desktop/bg.jpg") no-repeat;
        background-size:  100% auto;
    }
    .guide{
        background:white !important;
        height: 100% !important;
    }
</style>
