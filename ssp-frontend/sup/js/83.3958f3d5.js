(window.webpackJsonp=window.webpackJsonp||[]).push([[83],{8713:function(e,t,o){"use strict";o("e3HG")},AvpF:function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0});var r=d(o("/umX")),s=d(o("omC7")),n=d(o("XRYr")),a=d(o("Q9c5")),i=d(o("DWNM"));function d(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[i.default],name:"stockVoucher-list",data:function(){return{stockStatus:"",title:"",orderStock:{id:"",orderId:"",stockTime:"",orderItemId:"",itemStock:0,docIdList:[],remark:"",docInfo:"",sourceId:""},headers:{},rules:{itemStock:[{validator:function(e,t,o){0==/(^[1-9]\d*$)/.test(t)?o(new Error("请输入大于零的数")):o()},trigger:"change"},{validator:function(e,t,o){0==/(^[1-9]\d*$)/.test(t)?o(new Error("请输入大于零的数")):o()},trigger:"blur"},{type:"number",required:!0,message:"入库数不能为空",trigger:"blur"}]},show:!1,dialogVisible:!1,downloadUrl:a.default.baseContext+"/file/download",orderStocks:[],params:{page:1,limit:10,records:0,orderItemId:""},fileList:[]}},computed:{uploadUrl:function(){return a.default.baseContext+"/file/upload"}},props:{orderItemId:{type:String,default:""},amount:{type:Number,default:""},sourceId:{type:String,default:""}},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ")+(t.getHours()<10?"0"+t.getHours()+":":t.getHours()+":")+(t.getMinutes()<10?"0"+t.getMinutes()+":":t.getMinutes()+":")+(t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds())}},watch:{orderItemId:function(e){e&&this.queryOrderStocks(),console.log(this.$route.name)}},mounted:function(){var e=n.default.doCloundRequest(a.default.app_key,a.default.app_security,"");this.headers["x-aep-appkey"]=e["x-aep-appkey"],this.headers["x-aep-signature"]=e["x-aep-signature"],this.headers["x-aep-timestamp"]=e["x-aep-timestamp"],this.headers["x-aep-nonce"]=e["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,this.orderItemId&&this.queryOrderStocks()},methods:(0,r.default)({onSuccess:function(e,t,o){if(console.log(e),"1"!=e.state)return this.$message.error(e.message),this.$refs.upload.clearFiles(),!1;t.docId=e.row.id;var r={docId:e.row.id,name:e.row.name};this.orderStock.docIdList.push(r)},onError:function(e,t,o){this.rLoading.close(),console.log(e),this.$message.error("上传失败,请重试")},handlePreview:function(e){var t=this.downloadUrl+"?docId="+encodeURI(encodeURI(e.docId))+"&fileName="+e.name;console.log(e);var o=document.createElement("a");o.href=t,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(t)},beforeUpload:function(e){return!(e.size/1024/1024>10)||(this.$message.error("上传文件过大，请分开上传，单个文件最大为10M。"),!1)},beforeRemove:function(e,t){return!this.beforeUpload(e)||this.$confirm("确定移除 "+e.name+"？")},handleRemove:function(e,t){var o=this;this.orderStock.docIdList.some(function(t,r){if(t.docId==e.docId)return o.orderStock.docIdList.splice(r,1),!0}),console.log(e.docId,e,t)},saveOrderStock:function(){var e=this;if(!this.orderStock.docIdList||0==this.orderStock.docIdList.length)return this.$message.error("请上传入库凭证。"),!1;this.$refs.orderStock.validate(function(t){if(t){e.orderStock.docInfo=(0,s.default)(e.orderStock.docIdList);var o=e.openLoading("提交中...");e.$http_post(a.default.baseContext+"/supervise/supOrderStock/save",e.orderStock,!0).then(function(t){1==t.state?(e.$message.success("提交成功"),e.queryOrderStocks(),e.show=!1,o.close()):(e.$message.error(t.message),o.close())})}})},deleteOrderStock:function(e){var t=this;this.$confirm("确定要删除该记录吗").then(function(){var o=t.openLoading("");t.$http_post(a.default.baseContext+"/supervise/supOrderStock/delete/"+e.id,{orderId:t.$route.query.orderId,sourceId:t.sourceId}).then(function(e){1==e.state?(t.$message.success("删除成功"),t.queryOrderStocks(),o.close()):(t.$alert(e.message),o.close())})})},editOrderStock:function(e){var t=this;console.log(e),this.orderStock={id:e.id,sourceId:this.sourceId,orderId:this.$route.query.orderId,itemStock:e.itemStock,stockTime:e.stockTime,orderItemId:e.orderItemId,remark:e.remark,docInfo:e.docInfo,docIdList:JSON.parse(e.docInfo)},this.fileList=JSON.parse(e.docInfo),this.title="编辑入库凭证",this.$nextTick(function(){t.show=!0})},downloadFile:function(e,t){var o=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,r=document.createElement("a");r.href=o,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(o)},close:function(){this.show=!1},onPageClick:function(e){this.params.page=e,this.queryOrderStocks()},uploadVoucher:function(){var e=this,t=this.$route.query.orderId;this.orderStock={id:"",stockTime:"",sourceId:this.sourceId,orderId:t,itemStock:this.amount,orderItemId:this.orderItemId,remark:"",docInfo:"",docIdList:[]},this.title="上传入库凭证",this.fileList=[],this.$nextTick(function(){e.show=!0})},queryOrderStocks:function(){var e=this;this.params.orderId=this.$route.query.orderId,this.params.orderItemId=this.orderItemId;var t=this.openLoading("");this.$http_post(a.default.baseContext+"/supervise/supOrderStock/all",this.params).then(function(o){t.close(),1==o.state?(e.stockStatus=o.message,e.orderStocks=o.rows,e.params.records=o.records):e.$alert(o.message)})}},"downloadFile",function(e,t){var o=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,r=document.createElement("a");r.href=o,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(o)})}},chxd:function(e,t,o){"use strict";o.r(t);var r=o("AvpF"),s=o.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){o.d(t,e,function(){return r[e]})}(n);t.default=s.a},cyWD:function(e,t,o){"use strict";var r=o("zY1e");o.o(r,"render")&&o.d(t,"render",function(){return r.render}),o.o(r,"staticRenderFns")&&o.d(t,"staticRenderFns",function(){return r.staticRenderFns})},e3HG:function(e,t,o){},tNMj:function(e,t,o){"use strict";o.r(t);var r=o("cyWD"),s=o("chxd");for(var n in s)["default"].indexOf(n)<0&&function(e){o.d(t,e,function(){return s[e]})}(n);o("8713");var a=o("gp09"),i=Object(a.a)(s.default,r.render,r.staticRenderFns,!1,null,"dec4c0de",null);t.default=i.exports},zY1e:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",[t("el-dialog",{attrs:{title:"入库凭证信息",visible:e.dialogVisible,top:"10vh",width:"70%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",{staticClass:"dialog-content"},[t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:!1,expression:"false"}],staticClass:"data_table",staticStyle:{width:"100%"},attrs:{"header-cell-style":{background:"#f5f7fa"},data:e.orderStocks,"highlight-current-row":""}},[t("el-table-column",{attrs:{label:"序号",width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(t.$index+1)+"\n                            ")]}}])}),t("el-table-column",{attrs:{label:"入库数量",align:"center",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(t.row.itemStock)+"\n                            ")]}}])}),t("el-table-column",{attrs:{width:"180px",label:"入库时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(e._f("formatTime")(t.row.stockTime))+"\n                            ")]}}])}),t("el-table-column",{attrs:{width:"300px",label:"入库凭证",align:"center"},scopedSlots:e._u([{key:"default",fn:function(o){return e._l(JSON.parse(o.row.docInfo),function(o,r){return t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.downloadFile(o.docId,o.name)}}},[e._v("\n                                    "+e._s(o.name)+"\n                                ")])})}}])}),t("el-table-column",{attrs:{label:"备注",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(t.row.remark)+"\n                            ")]}}])}),t("el-table-column",{attrs:{width:"180px",label:"数据上传时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(e._f("formatTime")(t.row.creationTime))+"\n                            ")]}}])}),"itemStockVoucher"==e.$route.name?t("el-table-column",{attrs:{label:"操作",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(o){return[t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.editOrderStock(o.row)}}},[e._v("编辑")]),t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.deleteOrderStock(o.row)}}},[e._v("删除")])]}}],null,!1,209884114)}):e._e()],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("关闭")])],1)]),t("el-dialog",{attrs:{title:this.title,visible:e.show,width:"60%"},on:{close:e.close}},[t("div",{staticClass:"dialog-content"},[t("el-form",{ref:"orderStock",staticClass:"item-form",attrs:{id:"orderStock",model:e.orderStock,"label-width":"90px"}},[t("div",{staticClass:"fromBox"},[t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"入库数量",prop:"itemStock"}},[t("el-input",{model:{value:e.orderStock.itemStock,callback:function(t){e.$set(e.orderStock,"itemStock",e._n(t))},expression:"orderStock.itemStock"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"入库时间",prop:"stockTime"}},[t("el-date-picker",{attrs:{type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss",size:"small",placeholder:"请选择入库时间"},model:{value:e.orderStock.stockTime,callback:function(t){e.$set(e.orderStock,"stockTime",t)},expression:"orderStock.stockTime"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"凭证上传"}},[t("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{headers:e.headers,action:e.uploadUrl,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"before-upload":e.beforeUpload,"before-remove":e.beforeRemove,"on-success":e.onSuccess,"on-error":e.onError,multiple:"",accept:".gif,.jpg,.jpeg,.bmp,.png,.doc,.docx,.txt,.xlsx,.xls,.pdf","file-list":e.fileList}},[t("el-button",{attrs:{size:"small"}},[e._v("点击上传")])],1)],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"备注",prop:"remark"}},[t("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.orderStock.remark,callback:function(t){e.$set(e.orderStock,"remark",t)},expression:"orderStock.remark"}})],1)],1)],1)],1)])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.close}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.saveOrderStock}},[e._v("确定")])],1)])],1)},t.staticRenderFns=[]}}]);