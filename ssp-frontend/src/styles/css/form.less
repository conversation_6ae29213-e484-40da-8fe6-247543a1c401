h2{
    font-size:16px;
    border-bottom:1px solid #e2e2e2;
    padding-bottom:10px;
    margin:20px 0 14px;
}
h2:first-child{
  margin-top:0;
}
h4{
  margin-bottom:14px;
  height:14px;
  margin-top:10px;
}
h4:before{ 
  display:inline-block;
  content:"";
  width:4px;
  height:100%;
  background:#3671da;
  margin-right:6px;
  position:relative;
  top:2px;
}
/deep/.el-table th.is-leaf{
    background:#f5f7fa;
}
/deep/.el-checkbox-group{
  padding-left:10px;
  .el-checkbox{
    margin-right:20px;
  }
}
.icon-icon02{
  position:relative;
  top:1px;
  margin-right:4px;
}
/deep/.el-checkbox__inner{
  padding-right:0!important;
 
}
/deep/.el-select-dropdown__item{
  span{
    padding-right:0!important;
  }
}
.el-tabs__content{
  position:relative;
  .cardView{
    //height:calc(100vh - 175px);
    padding-top:15px;
    
    .view{
      //height:calc(100vh - 270px);
     // overflow:auto;
    }
    .footer{
      width:100%;
      height:50px;
      position:absolute;
      left:0;
      bottom:14px;
      margin-top:30px;
      div{
        position:absolute;
        left:50%;
        top:0;
        transform: translateX(-50%);
        /deep/button{
          width:120px!important;
        }
      }
    }
  }
}
/deep/.el-card{
  box-shadow:none!important;
  border:1px solid #e2e2e2;
  border-radius:0;
  .el-card__header{
    padding: 14px 10px;
    border-bottom:1px solid #e2e2e2;
  }
  /deep/.el-card__body{
    padding:0;
    .el-table--border{
      border:none;
    }
    .el-table{
      height:auto!important;
      th.is-leaf{
        border-color:#e2e2e2;
        background:#f5f7fa;
      }
      tr:last-child{
        td{
          border-bottom:none;
        }
      }
    }
    .el-table::before{
      height:0;
    }
    .el-table td{
      border-color:#e2e2e2;
    }
  }
}
/deep/.el-table{
  height:auto!important;
}
/deep/.el-table__body-wrapper{
  height:auto!important;
}
/deep/.el-tabs__content{
  padding-top:0!important;
}
.fromBox{
  /deep/.el-form-item__content{
    border-right:1px solid #e2e2e2;
  }
}
.el-form{
    .fromBox{
        border-bottom:1px solid #e2e2e2;
    }
    .el-row{
        border:1px solid #e2e2e2;
        border-right:none;
        border-bottom:none;
        min-height:48px;
       .el-col{
            height:100%;
            .el-form-item{
                margin:0;
                /deep/.el-form-item__label{
                    height:100%;
                    background:#f5f7fa;
                    border-right:1px solid #e2e2e2;
                    display:flex;
                    align-items:center;
                    text-align:right;
                    justify-content: flex-end;
                    position:absolute;
                    line-height:20px;
                }
                /deep/.el-form-item__content{
                    padding:6px;
                    &>.el-input{
                      .el-input__inner{
                          border:none;
                          display:flex;
                          align-items:center;
                      }
                    }
                    .el-table{
                      .el-date-editor.el-input{
                        width:100%!important;
                      }
                    }
                    .el-radio-group{
                      margin-left:10px;
                    }
                    .el-select{
                        width:100%;
                        .el-input__inner{
                          border:1px solid #e2e2e2;
                        }
                    }
                    .upload-demo{
                        padding:10px;
                    }
                    .el-date-editor{
                      .el-icon-date{
                        position:relative;
                        top:1px;
                      }
                    }
                    .department{
                      margin-left:10px;
                      margin-bottom:6px;
                      span{
                        color:#999;
                      }
                      .el-tag{
                        margin-right:20px;
                      }
                    }
                    .row{
                      display:inline-block;
                      //padding:10px;
                      position:relative;
                      width:70%;
                      margin-left:30px;
                      .el-input__inner{
                        border:1px solid #e2e2e2;
                      }
                      button{
                        position:absolute;
                        right:0;
                        top:2px;
                        border-radius: 0 4px 4px 0;
                      }
                    }
                    .linkButton{
                      margin-left:10px;
                    }
                }
              /deep/.el-form-item__error{
                display: none;
              }
            }
         /deep/ .select{
           .el-form-item__error{
             display: block;
             top: 30%;
             right: 11px;
             left: unset;
           }
         }
         /deep/.selects{
           .el-form-item__label:before{
             content: '*';
             color: #ff1818;
           }
         }
         /deep/ .is-error{
           input::-webkit-input-placeholder,textarea::-webkit-input-placeholder{
             color: #f56c6c;
           }
         }
            
       }
    }
}
.timeDayBox{
  display:flex;
  border-right:1px solid #e2e2e2;
  flex-direction:row;
  /deep/.el-input{
    width:80%;
  }
  /deep/.el-form-item__content{
   border-right:none;
  }
}
/deep/table{
  thead{
    th{
      background: #f5f7fa!important;
    }
  }
}
.iconfont{
  font-size:14px;
}