(window.webpackJsonp=window.webpackJsonp||[]).push([[63],{"+Xw+":function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});n(a("XRYr"));var i=n(a("Q9c5")),s=n(a("DWNM"));n(a("cH4l"));function n(e){return e&&e.__esModule?e:{default:e}}t.default={name:"invoiceItemPay-list",mixins:[s.default],data:function(){return{adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1,pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=(new Date).format("yyyy-MM-dd HH:mm:ss"),a=new Date,i=new Date(a.getTime()-6048e5).format("yyyy-MM-dd HH:mm:ss");e.$emit("pick",[i,t])}},{text:"最近一个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd HH:mm:ss"),a=new Date,i=new Date(a.getTime()-2592e6).format("yyyy-MM-dd HH:mm:ss");e.$emit("pick",[i,t])}},{text:"最近三个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd HH:mm:ss"),a=new Date,i=new Date(a.getTime()-7776e6).format("yyyy-MM-dd HH:mm:ss");e.$emit("pick",[i,t])}}]},invoiceItemDataList:[],hospitalList:[],invoiceParams:{deductionCode:"",catalogName:"",payDate:"",page:1,limit:10,records:0,hospitalName:"",deliveryName:""},dialogVisible:!1,listLoading:!1,tableHeight:100}},mounted:function(){var e=this;this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100},this.initRole(),this.onInvoiceQuery(),this.getHospitalList()},computed:{uploadUrl:function(){return i.default.baseContext+"/file/upload"}},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+"")},rateFormat:function(e){return e=isNaN(e)||""===e?"--":Number(100*e).toFixed(1)+"%"}},methods:{initRole:function(){var e=this.$store.getters.curUser.roleCode;-1!=e.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=e.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=e.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=e.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)},getHospitalList:function(){var e=this,t=i.default.baseContext+"/supervise/supHospital/all";this.$http_post(t,{}).then(function(t){1==t.state?e.hospitalList=t.row:e.$alert(t.message)})},onInvoicePageClick:function(e){this.invoiceParams.page=e,this.onInvoiceQuery()},onInvoiceQuery:function(){var e=this,t=this.invoiceParams,a=this.openLoading(),s=i.default.baseContext+"/supervise/supInsuranceLog/list";this.$http_post(s,t).then(function(t){1==t.state?(e.invoiceItemDataList=t.rows,e.invoiceParams.records=t.records,a.close()):(a.close(),e.$alert(t.message))})},onInvoiceSearch:function(e){"reset"==e?(this.invoiceParams.deductionCode="",this.invoiceParams.deliveryName="",this.invoiceParams.hospitalName="",this.invoiceParams.payDate="",this.invoiceParams.catalogName="",this.onInvoiceQuery()):this.invoiceParams.hospitalName||this.invoiceParams.payDate||this.invoiceParams.catalogName||this.invoiceParams.deductionCode?(this.invoiceParams.page=1,this.onInvoiceQuery()):this.$message.warning("请输入查询条件")}}}},"/nKX":function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left"},[t("el-row",{attrs:{gutter:10}},[this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("医疗机构")]),t("div",{staticClass:"searchInput"},[this.adminRole||this.medicalAdmin||this.socialAdmin?t("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:e.invoiceParams.hospitalName,callback:function(t){e.$set(e.invoiceParams,"hospitalName",t)},expression:"invoiceParams.hospitalName"}},e._l(e.hospitalList,function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})}),1):e._e()],1)])]):e._e(),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("扣款单号")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入扣款单号"},model:{value:e.invoiceParams.deductionCode,callback:function(t){e.$set(e.invoiceParams,"deductionCode",t)},expression:"invoiceParams.deductionCode"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("通用名")]),t("div",{staticClass:"searchInput"},[t("el-input",{attrs:{size:"small",placeholder:"请输入药品通用名称"},model:{value:e.invoiceParams.catalogName,callback:function(t){e.$set(e.invoiceParams,"catalogName",t)},expression:"invoiceParams.catalogName"}})],1)])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"searchct"},[t("div",{staticClass:"searchLabel"},[e._v("支付时间")]),t("div",{staticClass:"searchInput"},[t("el-date-picker",{attrs:{size:"small","value-format":"yyyy-MM-dd HH:mm:ss",type:"daterange","picker-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left"},model:{value:e.invoiceParams.payDate,callback:function(t){e.$set(e.invoiceParams,"payDate",t)},expression:"invoiceParams.payDate"}})],1)])])],1)],1),t("div",{staticClass:"right"},[t("div",[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:e.onInvoiceSearch}},[e._v("查询")]),t("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(t){return e.onInvoiceSearch("reset")}}},[e._v("重置")])],1)])]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{key:Math.random(),staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.invoiceItemDataList,"highlight-current-row":"",height:e.tableHeight}},[t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"190"}}),t("el-table-column",{attrs:{prop:"deliveryName",label:"配送单位",width:"180"}}),t("el-table-column",{attrs:{prop:"itemNo",label:"发票号",width:"100"}}),t("el-table-column",{attrs:{prop:"bankName",label:"银行账户",width:"160"}}),t("el-table-column",{attrs:{prop:"bankCard",label:"银行卡号",width:"170"}}),t("el-table-column",{attrs:{prop:"taxesAmount",label:"金额"}}),t("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150"}}),t("el-table-column",{attrs:{prop:"num",label:"数量"}}),t("el-table-column",{attrs:{prop:"payStatus",label:"回款状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return["1"==a.row.payStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已回款")])],1):e._e(),"2"==a.row.payStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分回款")])],1):e._e(),"0"!=a.row.payStatus&&a.row.payStatus?e._e():t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未回款")])],1)]}}])}),t("el-table-column",{attrs:{prop:"payTime",label:"支付时间",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e._f("formatTime")(a.row.payTime)))])]}}])}),t("el-table-column",{attrs:{prop:"transactionNumber",align:"left",label:"支付凭证号",width:"260"}}),t("el-table-column",{attrs:{prop:"orderCode",label:"订单编号",width:"120"}}),t("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单号",width:"120"}}),t("el-table-column",{attrs:{prop:"stockInTime",label:"入库时间",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e._f("formatTime")(a.row.stockInTime)))])]}}])}),t("el-table-column",{attrs:{prop:"country",label:"来源",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return["1"==a.row.country?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("国家集采")])],1):e._e(),"0"==a.row.country?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("非国家集采")])],1):e._e(),"2"==a.row.country?t("div",[t("el-tag",{attrs:{type:"primary"}},[e._v("线下采购")])],1):e._e()]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.invoiceParams.records,"page-size":e.invoiceParams.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onInvoicePageClick}})],1)],1)])],1)},t.staticRenderFns=[]},"MwR+":function(e,t,a){"use strict";a.r(t);var i=a("Ou23"),s=a("XG4A");for(var n in s)["default"].indexOf(n)<0&&function(e){a.d(t,e,function(){return s[e]})}(n);a("pLI2");var o=a("gp09"),l=Object(o.a)(s.default,i.render,i.staticRenderFns,!1,null,"a56ae232",null);t.default=l.exports},Ou23:function(e,t,a){"use strict";var i=a("/nKX");a.o(i,"render")&&a.d(t,"render",function(){return i.render}),a.o(i,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return i.staticRenderFns})},Scca:function(e,t,a){},XG4A:function(e,t,a){"use strict";a.r(t);var i=a("+Xw+"),s=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,function(){return i[e]})}(n);t.default=s.a},pLI2:function(e,t,a){"use strict";a("Scca")}}]);