(window.webpackJsonp=window.webpackJsonp||[]).push([[60],{DOVb:function(t,e,a){"use strict";a.r(e);var s=a("IF4B"),r=a("mtW2");for(var n in r)["default"].indexOf(n)<0&&function(t){a.d(e,t,function(){return r[t]})}(n);a("SWXT");var o=a("gp09"),i=Object(o.a)(r.default,s.render,s.staticRenderFns,!1,null,"42cf1ef7",null);e.default=i.exports},IF4B:function(t,e,a){"use strict";var s=a("aLBw");a.o(s,"render")&&a.d(e,"render",function(){return s.render}),a.o(s,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return s.staticRenderFns})},SWXT:function(t,e,a){"use strict";a("aSy2")},aLBw:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left flex-row"},[e("span",[t._v("目录名称")]),e("el-input",{attrs:{placeholder:"请输入目录名称"},model:{value:t.params.name,callback:function(e){t.$set(t.params,"name",e)},expression:"params.name"}}),e("span",[t._v("目录类别")]),e("el-select",{attrs:{placeholder:"请选择目录类别"},model:{value:t.params.category,callback:function(e){t.$set(t.params,"category",e)},expression:"params.category"}},[e("el-option",{attrs:{label:"中药",value:"1"}}),e("el-option",{attrs:{label:"西药",value:"2"}})],1),e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1),e("el-button",{attrs:{icon:"el-icon-plus",type:"primary"},on:{click:function(e){return t.add("add")}}},[t._v("新增")])],1),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.dataList,"highlight-current-row":"",height:t.tableHeight},on:{"current-change":t.handleCurrentChange}},[e("el-table-column",{attrs:{prop:"name",label:"目录名称"}}),e("el-table-column",{attrs:{prop:"category",label:"目录类别"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.category?e("div",[e("el-tag",{attrs:{type:""}},[t._v("中药")])],1):t._e(),"2"==a.row.category?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("西药")])],1):t._e()]}}])}),e("el-table-column",{attrs:{label:"状态",prop:"status"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-switch",{attrs:{"active-value":"1","inactive-value":"0","active-color":"#13ce66","inactive-color":"#ff4949"},on:{change:function(e){return t.changeStatus(a.row.id,a.row.status)}},model:{value:a.row.status,callback:function(e){t.$set(a.row,"status",e)},expression:"scope.row.status"}})]}}])}),e("el-table-column",{attrs:{label:"操作",align:"right",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.add("edit",a.row)}}},[t._v("编辑")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.del(a.row)}}},[t._v("删除")])]}}])})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)]),e("el-dialog",{attrs:{title:"药品目录",visible:t.dialogVisible,width:"45%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("div",{staticClass:"dialog-content"},[e("el-form",{ref:"form",staticClass:"item-form",attrs:{model:t.formData,"label-width":"90px",rules:t.rules}},[e("el-row",{attrs:{type:"flex"}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"目录名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"请输入目录名称"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"目录类别",prop:"category"}},[e("el-select",{attrs:{placeholder:"请选择目录类别"},model:{value:t.formData.category,callback:function(e){t.$set(t.formData,"category",e)},expression:"formData.category"}},[e("el-option",{attrs:{label:"中药",value:"1"}}),e("el-option",{attrs:{label:"西药",value:"2"}})],1)],1)],1)],1)],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("确 定")])],1)])],1)},e.staticRenderFns=[]},aSy2:function(t,e,a){},jg0Y:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var s=n(a("Q9c5")),r=n(a("DWNM"));function n(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[r.default],data:function(){return{dataList:[],params:{page:1,limit:10,records:0,category:"",name:""},formData:{id:"",category:"",name:""},dialogVisible:!1,tableHeight:100,rules:{name:[{required:!0,message:"请输入药品目录名称",trigger:"blur"}],category:[{required:!0,message:"请选择药品目录类别",trigger:"change"}]}}},props:{},computed:{},watch:{},methods:{changeStatus:function(t,e){var a=this,r={id:t,status:e},n=this.openLoading();url=s.default.baseContext+"/supervise/supDrugCatalog/updateStatus",this.$http_post(url,r).then(function(t){1==t.state?(a.$message.success("操作成功"),a.onQuery()):a.$alert(t.message),n.close()})},add:function(t,e){var a=this;if("add"==t&&(this.formData={category:"",name:""},this.dialogVisible=!0),"edit"==t){var r=this.openLoading();this.$http_post(s.default.baseContext+"/supervise/supDrugCatalog/info/"+e.id,{}).then(function(t){1==t.state?null!=t.row?(a.formData={id:t.row.id,category:t.row.category,name:t.row.name},a.dialogVisible=!0):a.$message.error("系统异常"):null!=t.message?a.$message.error(t.message):a.$message.error("系统异常"),r.close()})}},save:function(){var t=this;this.$refs.form.validate(function(e){if(!e)return!1;var a;a=s.default.baseContext+"/supervise/supDrugCatalog/edit";var r=t.openLoading();t.$http_post(a,t.formData).then(function(e){1==e.state?(null!=t.formData.id&&""!=t.formData.id?t.$message.success("修改成功"):t.$message.success("添加成功"),t.onQuery(),t.dialogVisible=!1):t.$alert(e.message),r.close()})})},del:function(t){var e=this;this.$alert("确定删除【"+t.name+"】数据吗","确认信息",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,type:"warning"}).then(function(){var a=e.openLoading();e.$http_post(s.default.baseContext+"/supervise/supDrugCatalog/delete/"+t.id,null).then(function(t){1==t.state?(e.$message.success("删除成功"),e.onQuery(),a.close()):(a.close(),e.$message.error(t.message))})}).catch(function(t){console.log(t)})},handleCurrentChange:function(t){},onSearch:function(t){"reset"==t?(this.params.category="",this.params.name="",this.onQuery()):""!=this.params.category||""!=this.params.name?(this.params.page=1,this.onQuery()):this.$message.warning("请输入目录名称或类别查询")},onPageClick:function(t){this.params.page=t,this.onQuery()},onQuery:function(){var t=this,e=this.params,a=this.openLoading(),r=s.default.baseContext+"/supervise/supDrugCatalog/list";this.$http_post(r,e).then(function(e){if(1==e.state){var s=e.rows;t.dataList=s,t.params.records=e.records,a.close()}else a.close(),t.$alert(e.message)})}},mounted:function(){var t=this;this.onQuery(),this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null}}},mtW2:function(t,e,a){"use strict";a.r(e);var s=a("jg0Y"),r=a.n(s);for(var n in s)["default"].indexOf(n)<0&&function(t){a.d(e,t,function(){return s[t]})}(n);e.default=r.a}}]);