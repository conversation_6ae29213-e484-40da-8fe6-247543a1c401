(window.webpackJsonp=window.webpackJsonp||[]).push([[27],{"7sxM":function(e,t,o){},E1bj:function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{staticClass:"login flex-column"},[t("div",{staticClass:"login-content flex-column"},[t("h2",{staticStyle:{"font-weight":"bolder","font-family":"none"}},[e._v(e._s(e.title))]),t("div",{staticClass:"flex-row"},[t("img",{staticClass:"logo",attrs:{src:o("eR9v")}}),t("div",{staticClass:"login-box flex-column"},[t("div",{staticClass:"item flex-row"},[t("input",{ref:"username",staticClass:"el-input el-input__inner",attrs:{type:"text",placeholder:"请输入用户名"}})]),t("div",{staticClass:"item flex-row"},[t("input",{ref:"password",staticClass:"el-input el-input__inner",attrs:{type:"password",id:"pwd",placeholder:"请输入密码"},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.onLogin.apply(null,arguments)}}})]),t("div",{staticClass:"row"},[t("div",{staticClass:"item flex-row"},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.code,expression:"code"}],staticClass:"code",attrs:{type:"text",id:"code",placeholder:"请输入验证码"},domProps:{value:e.code},on:{input:function(t){t.target.composing||(e.code=t.target.value)}}})]),t("div",{staticClass:"login-code",on:{click:e.refreshCode}},[t("s-identify",{attrs:{identifyCode:e.identifyCode}})],1)]),t("button",{on:{click:e.onLogin}},[e._v("登   录")])])])]),t("el-dialog",{staticClass:"modify-pwd",attrs:{title:"修改密码",visible:e.modifyPwd,"close-on-click-modal":!1,"close-on-press-escape":!1,width:"35%"},on:{"update:visible":function(t){e.modifyPwd=t}}},[t("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:e.ruleForm,"label-position":"left",rules:e.rules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"旧密码",prop:"oldPwd"}},[t("el-input",{attrs:{type:"password"},model:{value:e.ruleForm.oldPwd,callback:function(t){e.$set(e.ruleForm,"oldPwd",t)},expression:"ruleForm.oldPwd"}})],1),t("el-form-item",{attrs:{label:"新密码",prop:"pass"}},[t("el-input",{attrs:{type:"password",autocomplete:"off"},model:{value:e.ruleForm.pass,callback:function(t){e.$set(e.ruleForm,"pass",t)},expression:"ruleForm.pass"}})],1),t("el-form-item",{attrs:{label:"确认密码",prop:"checkPass"}},[t("el-input",{attrs:{type:"password",autocomplete:"off"},model:{value:e.ruleForm.checkPass,callback:function(t){e.$set(e.ruleForm,"checkPass",t)},expression:"ruleForm.checkPass"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.modifyPwd=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.confirmModify}},[e._v("确 定")])],1)],1)],1)},t.staticRenderFns=[]},GNbS:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this._self._c;return e("div",{staticClass:"s-canvas"},[e("canvas",{attrs:{id:"s-canvas",width:this.contentWidth,height:this.contentHeight}})])},t.staticRenderFns=[]},MoP2:function(e,t,o){"use strict";o.r(t);var r=o("Z2xZ"),i=o("QWQr");for(var n in i)["default"].indexOf(n)<0&&function(e){o.d(t,e,function(){return i[e]})}(n);o("rALJ");var s=o("gp09"),a=Object(s.a)(i.default,r.render,r.staticRenderFns,!1,null,"a5dc5358",null);t.default=a.exports},NhQN:function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0});var r=a(o("DWNM")),i=a(o("Q9c5")),n=a(o("6Cps"));o("j5jW");var s=a(o("v7KA"));function a(e){return e&&e.__esModule?e:{default:e}}var d=o("LalF").Base64;t.default={mixins:[r.default],components:{SIdentify:s.default},name:"login1",data:function(){var e=this;return{identifyCodes:"1234567890",identifyCode:"",code:"",title:"",version:"1.0.0",modifyPwd:!1,ruleForm:{oldPwd:"",pass:"",checkPass:""},user:{},rules:{oldPwd:[{required:!0,message:"请输入原密码",trigger:"blur"}],pass:[{required:!0,validator:function(t,o,r){""===o?r(new Error("请输入新密码")):(new RegExp("(?=.*[0-9])(?=.*[a-zA-Z]).{8,30}").test(o)||r(new Error("您的密码复杂度太低（密码中必须包含8-30位的字母及数字）")),""!==e.ruleForm.checkPass&&e.$refs.ruleForm.validateField("checkPass"),r())},trigger:"blur"}],checkPass:[{required:!0,validator:function(t,o,r){""===o?r(new Error("请再次输入密码")):o!==e.ruleForm.pass?r(new Error("两次输入密码不一致!")):r()},trigger:"blur"}]}}},computed:{},methods:{randomNum:function(e,t){return Math.floor(Math.random()*(t-e)+e)},refreshCode:function(){this.identifyCode="",this.makeCode(this.identifyCodes,4)},makeCode:function(e,t){for(var o=0;o<t;o++)this.identifyCode+=this.identifyCodes[this.randomNum(0,this.identifyCodes.length)];console.log(this.identifyCode)},onLogin:function(){var e=this;if(""!=this.code){if(this.identifyCode!==this.code)return this.code="",this.refreshCode(),void alert("请输入正确的验证码");var t=this.$refs.username.value,o=this.$refs.password.value;return null==t||0==t.length?(alert("请输入用户名"),void this.$refs.username.focus()):null==o||0==o.length?(alert("请输入密码"),void this.$refs.password.focus()):void this.login(t,o,function(t){if(1==t.state){e.$store.dispatch("setCookieToken",t.row.jwtToken);var o=new Date;o.setTime(o.getTime()+864e5),document.cookie="ewtk="+t.message+";path=/;expires="+o.toGMTString();var r=e.$route.query.redirect;r=e.isNotNull(r)?d.decode(r):"/",0==t.row.user.attrMap.resetFlag?(e.user=t.row.user,e.modifyPwd=!0):e.$router.push(r)}else alert(t.message)})}alert("请输入验证码")},confirmModify:function(){var e=this;this.$refs.ruleForm.validate(function(t){if(!t)return!1;var o=e.user;if(null!=o){var r={id:o.id,oldPassword:n.default.MD5(e.ruleForm.oldPwd+"").toString(),newPassword:n.default.MD5(e.ruleForm.pass+"").toString()},s=e.openLoading("修改中...");e.$http_post(i.default.baseContext+"/bsp/pubUser/updatePassword",r).then(function(t){if(1==t.state){e.$message.success("修改成功"),e.modifyPwd=!1;var o=e.$route.query.redirect;o=e.isNotNull(o)?d.decode(o):"/",e.$router.push(o)}else null!=t.message?e.$message.error(t.message):e.$message.error("系统异常");s.close()}).catch(function(e){s.close(),console.log(e)})}})}},mounted:function(){this.identifyCode="",this.makeCode(this.identifyCodes,4),this.title=i.default.title,this.version=i.default.version;var e=this.$store.getters.curUser,t=this.$route.query.redirect;t=this.isNotNull(t)?d.decode(t):"/",this.isEmptyObject(e)||this.$router.push(t)},created:function(){this.refreshCode()}}},OrRo:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default={name:"SIdentify",props:{identifyCode:{type:String,default:"1234"},fontSizeMin:{type:Number,default:25},fontSizeMax:{type:Number,default:30},backgroundColorMin:{type:Number,default:255},backgroundColorMax:{type:Number,default:255},colorMin:{type:Number,default:0},colorMax:{type:Number,default:160},lineColorMin:{type:Number,default:100},lineColorMax:{type:Number,default:255},dotColorMin:{type:Number,default:0},dotColorMax:{type:Number,default:255},contentWidth:{type:Number,default:112},contentHeight:{type:Number,default:31}},methods:{randomNum:function(e,t){return Math.floor(Math.random()*(t-e)+e)},randomColor:function(e,t){return"rgb("+this.randomNum(e,t)+","+this.randomNum(e,t)+","+this.randomNum(e,t)+")"},drawPic:function(){var e=document.getElementById("s-canvas").getContext("2d");e.textBaseline="bottom",e.fillStyle=this.randomColor(this.backgroundColorMin,this.backgroundColorMax),e.fillRect(0,0,this.contentWidth,this.contentHeight);for(var t=0;t<this.identifyCode.length;t++)this.drawText(e,this.identifyCode[t],t);this.drawLine(e),this.drawDot(e)},drawText:function(e,t,o){e.fillStyle=this.randomColor(this.colorMin,this.colorMax),e.font=this.randomNum(this.fontSizeMin,this.fontSizeMax)+"px SimHei";var r=(o+1)*(this.contentWidth/(this.identifyCode.length+1)),i=this.randomNum(this.fontSizeMax,this.contentHeight-5),n=this.randomNum(-45,45);e.translate(r,i),e.rotate(n*Math.PI/180),e.fillText(t,0,0),e.rotate(-n*Math.PI/180),e.translate(-r,-i)},drawLine:function(e){for(var t=0;t<5;t++)e.strokeStyle=this.randomColor(this.lineColorMin,this.lineColorMax),e.beginPath(),e.moveTo(this.randomNum(0,this.contentWidth),this.randomNum(0,this.contentHeight)),e.lineTo(this.randomNum(0,this.contentWidth),this.randomNum(0,this.contentHeight)),e.stroke()},drawDot:function(e){for(var t=0;t<80;t++)e.fillStyle=this.randomColor(0,255),e.beginPath(),e.arc(this.randomNum(0,this.contentWidth),this.randomNum(0,this.contentHeight),1,0,2*Math.PI),e.fill()}},watch:{identifyCode:function(){this.drawPic()}},mounted:function(){this.drawPic()}}},QWQr:function(e,t,o){"use strict";o.r(t);var r=o("NhQN"),i=o.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){o.d(t,e,function(){return r[e]})}(n);t.default=i.a},XtEp:function(e,t,o){"use strict";var r=o("GNbS");o.o(r,"render")&&o.d(t,"render",function(){return r.render}),o.o(r,"staticRenderFns")&&o.d(t,"staticRenderFns",function(){return r.staticRenderFns})},Z2xZ:function(e,t,o){"use strict";var r=o("E1bj");o.o(r,"render")&&o.d(t,"render",function(){return r.render}),o.o(r,"staticRenderFns")&&o.d(t,"staticRenderFns",function(){return r.staticRenderFns})},eHVj:function(e,t,o){},eR9v:function(e,t,o){e.exports=o.p+"images/logo.ca64f4d9.png"},mRtE:function(e,t,o){"use strict";o("eHVj")},rALJ:function(e,t,o){"use strict";o("7sxM")},v7KA:function(e,t,o){"use strict";o.r(t);var r=o("XtEp"),i=o("vzqx");for(var n in i)["default"].indexOf(n)<0&&function(e){o.d(t,e,function(){return i[e]})}(n);o("mRtE");var s=o("gp09"),a=Object(s.a)(i.default,r.render,r.staticRenderFns,!1,null,"668ee6d8",null);t.default=a.exports},vzqx:function(e,t,o){"use strict";o.r(t);var r=o("OrRo"),i=o.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){o.d(t,e,function(){return r[e]})}(n);t.default=i.a}}]);