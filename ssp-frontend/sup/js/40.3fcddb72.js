(window.webpackJsonp=window.webpackJsonp||[]).push([[40],{"3iUb":function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left"},[e("el-row",{attrs:{gutter:10}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("订单号")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入订单号"},model:{value:t.params.orderCode,callback:function(e){t.$set(t.params,"orderCode",e)},expression:"params.orderCode"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("通用名")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入耗材通用名"},model:{value:t.params.catalogName,callback:function(e){t.$set(t.params,"catalogName",e)},expression:"params.catalogName"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("规格")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输注册证规格"},model:{value:t.params.specs,callback:function(e){t.$set(t.params,"specs",e)},expression:"params.specs"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("生产企业")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入生产企业名"},model:{value:t.params.drugCompanyName,callback:function(e){t.$set(t.params,"drugCompanyName",e)},expression:"params.drugCompanyName"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("注册证号")]),e("div",{staticClass:"searchInput"},[e("el-input",{attrs:{size:"small",placeholder:"请输入注册证号"},model:{value:t.params.regCredNum,callback:function(e){t.$set(t.params,"regCredNum",e)},expression:"params.regCredNum"}})],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("批次")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择批次"},model:{value:t.params.batch,callback:function(e){t.$set(t.params,"batch",e)},expression:"params.batch"}},[e("el-option",{attrs:{label:"全部",value:""}}),t._l(t.batchList,function(t){return e("el-option",{key:t.code,attrs:{label:t.batchName,value:t.code}})})],2)],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("发货状态")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",placeholder:"全部"},model:{value:t.params.orderItemStatus,callback:function(e){t.$set(t.params,"orderItemStatus",e)},expression:"params.orderItemStatus"}},[e("el-option",{key:"",attrs:{label:"全部",value:""}}),e("el-option",{key:"0",attrs:{label:"待确认",value:"0"}}),e("el-option",{key:"1",attrs:{label:"待发货",value:"1"}}),e("el-option",{key:"2",attrs:{label:"部分发货",value:"2"}}),e("el-option",{key:"3",attrs:{label:"已发货",value:"3"}}),e("el-option",{key:"4",attrs:{label:"已完成",value:"4"}}),e("el-option",{key:"5",attrs:{label:"已取消",value:"5"}})],1)],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("入库状态")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",placeholder:"全部"},model:{value:t.params.stockStatus,callback:function(e){t.$set(t.params,"stockStatus",e)},expression:"params.stockStatus"}},[e("el-option",{key:"",attrs:{label:"全部",value:""}}),e("el-option",{key:"0",attrs:{label:"未入库",value:"0"}}),e("el-option",{key:"1",attrs:{label:"已入库",value:"1"}}),e("el-option",{key:"2",attrs:{label:"部分入库",value:"2"}})],1)],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("支付状态")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",placeholder:"全部"},model:{value:t.params.payStatus,callback:function(e){t.$set(t.params,"payStatus",e)},expression:"params.payStatus"}},[e("el-option",{key:"",attrs:{label:"全部",value:""}}),e("el-option",{key:"0",attrs:{label:"未支付",value:"0"}}),e("el-option",{key:"1",attrs:{label:"已支付",value:"1"}}),e("el-option",{key:"2",attrs:{label:"部分支付",value:"2"}})],1)],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("医疗机构")]),e("div",{staticClass:"searchInput"},[this.adminRole||this.medicalAdmin||this.socialAdmin||this.areaRegionAdmin?e("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:t.params.hospitalName,callback:function(e){t.$set(t.params,"hospitalName",e)},expression:"params.hospitalName"}},t._l(t.hospitalList,function(t){return e("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})}),1):t._e(),this.adminRole||this.medicalAdmin||this.socialAdmin||this.areaRegionAdmin?t._e():e("el-select",{attrs:{size:"small",disabled:"",clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:t.params.hospitalName,callback:function(e){t.$set(t.params,"hospitalName",e)},expression:"params.hospitalName"}},t._l(t.hospitalList,function(t){return e("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})}),1)],1)])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("采购时间")]),e("div",{staticClass:"searchInput"},[e("el-date-picker",{attrs:{size:"small","unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":t.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",clearable:"",align:"left"},model:{value:t.params.submitTime,callback:function(e){t.$set(t.params,"submitTime",e)},expression:"params.submitTime"}})],1)])]),this.adminRole||this.medicalAdmin||this.socialAdmin?e("el-col",{attrs:{span:6}},[e("div",{staticClass:"searchct"},[e("div",{staticClass:"searchLabel"},[t._v("选择区划")]),e("div",{staticClass:"searchInput"},[e("el-select",{attrs:{size:"small",clearable:"",filterable:"",placeholder:"区划管理"},model:{value:t.params.regionId,callback:function(e){t.$set(t.params,"regionId",e)},expression:"params.regionId"}},t._l(t.regionList,function(t){return e("el-option",{key:t.name,attrs:{label:t.name,value:t.code}})}),1)],1)])]):t._e()],1)],1),e("div",{staticClass:"right"},[e("div",[e("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{size:"small",icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1)])]),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{key:Math.random(),staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.orderItemList,height:t.tableHeight,border:""}},[t._e(),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"200"}}),e("el-table-column",{attrs:{prop:"orderNum",label:"订单号",width:"150"}}),e("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"submitTime",label:"采购时间",formatter:t.time,width:"150"}}),e("el-table-column",{attrs:{prop:"ORDER_ITEM_STATUS",label:"发货状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return["0"==a.row.orderItemStatus?e("div",[e("el-tag",{attrs:{type:"info"}},[t._v("待确认")])],1):t._e(),"1"==a.row.orderItemStatus?e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("待发货")])],1):t._e(),"2"==a.row.orderItemStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分发货")])],1):t._e(),"3"==a.row.orderItemStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已发货")])],1):t._e(),"4"==a.row.orderItemStatus?e("div",[e("el-tag",{attrs:{type:"primary"}},[t._v("已完成")])],1):t._e(),"5"==a.row.orderItemStatus?e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("已取消")])],1):t._e(),a.row.orderItemStatus&&"6"!=a.row.orderItemStatus?t._e():e("div",[e("el-tag",{attrs:{type:"info"}},[t._v("无")])],1)]}}])}),e("el-table-column",{attrs:{prop:"stockStatus",label:"入库状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.stockStatus&&"0"!=a.row.stockStatus?t._e():e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("未入库")])],1),"1"==a.row.stockStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已入库")])],1):t._e(),"2"==a.row.stockStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分入库")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"payStatus",label:"支付状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.payStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已支付")])],1):t._e(),"2"==a.row.payStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分支付")])],1):t._e(),"0"!=a.row.payStatus&&a.row.payStatus||a.row.stockStatus&&"0"!=a.row.stockStatus?t._e():e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("未支付")])],1),"0"!=a.row.payStatus&&a.row.payStatus||!a.row.stockStatus||"0"==a.row.stockStatus?t._e():e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("未支付")])],1)]}}])}),e("el-table-column",{attrs:{prop:"regCredNum",label:"注册证号"}}),e("el-table-column",{attrs:{prop:"regCredSpec",label:"注册规格"}}),e("el-table-column",{attrs:{prop:"regCredName",label:"注册证名称"}}),e("el-table-column",{attrs:{prop:"regCredModel",label:"注册证型号"}}),e("el-table-column",{attrs:{prop:"meterialBatchName","min-width":"200px",label:"集采批次"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n              "+t._s("【非集采耗材】"==e.row.meterialBatchName?"-":e.row.meterialBatchName)+"\n            ")]}}])}),e("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"190","show-tooltip-when-overflow":""}}),e("el-table-column",{attrs:{prop:"unitPrice",label:"商品价(元)",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(e.row.unitPrice)+"\n          ")]}}])}),e("el-table-column",{attrs:{prop:"amount",label:"采购数量",width:"100"}}),e("el-table-column",{attrs:{prop:"itemPrice",label:"合计(元)",width:"130"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(e.row.itemPrice)+"\n          ")]}}])}),e("el-table-column",{attrs:{fixed:"right",label:"操作",width:"130"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.showDetail(a.row)}}},[t._v("查看详情")])]}}])})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)])],1)},e.staticRenderFns=[]},OUni:function(t,e,a){"use strict";a("s976")},Vvch:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var s=l(a("DWNM")),r=l(a("Q9c5"));l(a("rGKd"));function l(t){return t&&t.__esModule?t:{default:t}}e.default={mixins:[s.default],name:"drug_price_list",data:function(){return{adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1,areaRegionAdmin:!1,batchList:[],pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,s=new Date(a.getTime()-6048e5).format("yyyy-MM-dd");t.$emit("pick",[s,e])}},{text:"最近一个月",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,s=new Date(a.getTime()-2592e6).format("yyyy-MM-dd");t.$emit("pick",[s,e])}},{text:"最近三个月",onClick:function(t){var e=(new Date).format("yyyy-MM-dd"),a=new Date,s=new Date(a.getTime()-7776e6).format("yyyy-MM-dd");t.$emit("pick",[s,e])}}]},tableHeight:100,warningData:[],listLoading:!1,orderItemList:[],hospitalList:[],params:{systemContrast:"",source:"",catalogName:"",submitTime:"",hospitalName:"",country:"",page:1,limit:10,records:0,orderCode:"",orderItemStatus:"",stockStatus:"",payStatus:"",batch:"",stockInTime:"",regCredNum:"",regCredSpec:"",regCredName:"",regCredModel:"",drugCompanyName:"",regionId:""},dateChangeValue:0,regionList:[{code:"440703",name:"蓬江区"},{code:"440704",name:"江海区"},{code:"440705",name:"新会区"},{code:"440781",name:"台山市"},{code:"440783",name:"开平市"},{code:"440784",name:"鹤山市"},{code:"440785",name:"恩平市"},{code:"440700",name:"江门市"}]}},mounted:function(){this.initRole(),this.onQuery(),this.getHospitalList(),this.getBatchList(),this.initHeight()},beforeDestroy:function(){window.onresize=null},methods:{setDate:function(){var t=(new Date).format("yyyy-MM-dd"),e=new Date,a=new Date(e.getTime()-2592e6).format("yyyy-MM-dd");this.params.submitTime=[a,t]},setBatchList:function(t){for(var e=t.rows,a=0;a<e.length;a++)this.batchList.push(e[a])},initRole:function(){var t=this.$store.getters.curUser.roleCode;-1!=t.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=t.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=t.indexOf("DELIVERY_ADMIN")?this.areaRegionAdmin=!0:-1!=t.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=t.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)},getBatchList:function(){var t=this;this.$http_post(r.default.baseContext+"/supervise/conMaterialBatch/getBatchList").then(function(e){t.batchList=e.rows})},getHospitalList:function(){var t=this,e=r.default.baseContext+"/supervise/supHospital/all";this.$http_post(e,{}).then(function(e){1==e.state?t.hospitalList=e.row:t.$alert(e.message)})},time:function(t,e){if(void 0==t.submitTime||""==t.submitTime)return"";var a=new Date(t.submitTime),s=a.getFullYear()+"-",r=(a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-",l=a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ";a.getHours(),a.getHours(),a.getMinutes(),a.getMinutes(),a.getSeconds(),a.getSeconds();return s+r+l},initHeight:function(){var t=this;this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-180}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-180}},onQuery:function(){var t=this;0!=this.dateChangeValue&&""!=this.dateChangeValue||this.setDate();var e=this.params,a=this.openLoading(),s=r.default.baseContext+"/supervise/conMaterialOrderItem/materialPurchaseList";this.$http_post(s,e).then(function(e){if(1==e.state){var s=e.rows;t.orderItemList=s,t.params.records=e.records,a.close()}else a.close(),t.$alert(e.message)})},onSearch:function(t){"reset"==t?(this.params.systemContrast="",this.params.orderItemStatus="",this.params.orderCode="",this.params.hospitalName="",this.params.catalogName="",this.params.source="",this.params.stockStatus="",this.params.payStatus="",this.params.regionId="",this.params.regCredNum="",this.params.regCredSpec="",this.params.regCredName="",this.params.regCredModel="",this.params.drugCompanyName="",this.params.batch="",this.onQuery(),this.dateChangeValue=0):(this.params.page=1,this.dateChangeValue=1,this.onQuery())},onPageClick:function(t){this.params.page=t,this.onQuery()},formatBatch:function(t){if(void 0==t||""==t)return"";for(var e=0;e<this.batchList.length;e++)if(this.batchList[e].code==t)return this.batchList[e].batchName},showDetail:function(t){this.$router.push({name:"materialPurchase",query:{materialId:t.detailId}})}}}},oEYd:function(t,e,a){"use strict";a.r(e);var s=a("p1mD"),r=a("p4mp");for(var l in r)["default"].indexOf(l)<0&&function(t){a.d(e,t,function(){return r[t]})}(l);a("OUni");var i=a("gp09"),o=Object(i.a)(r.default,s.render,s.staticRenderFns,!1,null,"18f67786",null);e.default=o.exports},p1mD:function(t,e,a){"use strict";var s=a("3iUb");a.o(s,"render")&&a.d(e,"render",function(){return s.render}),a.o(s,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return s.staticRenderFns})},p4mp:function(t,e,a){"use strict";a.r(e);var s=a("Vvch"),r=a.n(s);for(var l in s)["default"].indexOf(l)<0&&function(t){a.d(e,t,function(){return s[t]})}(l);e.default=r.a},rGKd:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var s=l(a("Q9c5")),r=l(a("ERIh"));function l(t){return t&&t.__esModule?t:{default:t}}var i=s.default.baseContext+"/supervise/supDrugBatch/getBatchList";var o={getBatchList:function(t){r.default.$http_api("GET",i,{},{jsonContentType:!1,responseType:null,addBaseUrl:!0}).then(function(e){1==e.state?function(t){return null!=t&&void 0!==t&&"function"==typeof t}(t)&&t(e):console.warn("查询集采批次失败",e.message)}).catch(function(t){console.warn("查询集采批次失败",t.message)})}};e.default=o},s976:function(t,e,a){}}]);