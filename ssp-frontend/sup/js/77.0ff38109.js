(window.webpackJsonp=window.webpackJsonp||[]).push([[77],{JiVb:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=n(a("omC7")),o=n(a("Q9c5")),s=n(a("DWNM")),l=n(a("XRYr"));function n(e){return e&&e.__esModule?e:{default:e}}t.default={name:"countryPurchase",mixins:[s.default],data:function(){return{warningData:[],tableHeight:100,dialogVisible:!1,payDialogVisible:!1,downloadUrl:o.default.baseContext+"/file/download",headers:{},dataList:[],params:{page:1,limit:10,records:0,orderNum:"",warning:"",status:"",payStatus:""},orderData:{data:{},orderItem:[],hospital:{},orderPays:[]},orderPay:{orderId:"",payPrice:0,payTime:"",docIdList:[],remark:"",docInfo:""},fileList:[],rules:{payPrice:[{validator:function(e,t,a){0==/^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/.test(t)?a(new Error("请输入大于零的金额")):a()},trigger:"change"},{validator:function(e,t,a){0==/^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/.test(t)?a(new Error("请输入大于零的金额")):a()},trigger:"blur"},{type:"number",required:!0,message:"总金额不能为空",trigger:"blur"}]}}},props:{},computed:{uploadUrl:function(){return o.default.baseContext+"/file/upload"}},watch:{},methods:{getWaringType:function(e){for(var t=0;t<this.warningData.length;t++)if(this.warningData[t].value==e)return this.warningData[t].label},getWaring:function(e){for(var t=[],a=e.split(","),r=0;r<a.length;r++){var o=this.getWaringType(a[r]);o&&t.push({name:o})}return t},getDictItem:function(e){var t=this,a=this.openLoading();this.$http_post(o.default.baseContext+"/supervise/supDictItem/getItemList",{code:e}).then(function(r){var o=r.rows;1==r.state?("WARNING"==e&&(t.warningData=o),a.close()):(a.close(),t.$message.error(r.message))})},editPayVoucher:function(e){var t=this.$route.query.country;1==t?this.$router.push({name:"itemPayVoucher",query:{orderId:e,country:t}}):this.$router.push({name:"itemPayVoucher",query:{orderId:e}})},onSuccess:function(e,t,a){if(console.log(e),"1"!=e.state)return this.$message.error(e.message),this.$refs.upload.clearFiles(),!1;t.docId=e.row.id;var r={docId:e.row.id,name:e.row.name};this.orderPay.docIdList.push(r)},onError:function(e,t,a){this.rLoading.close(),console.log(e),this.$message.error("上传失败,请重试")},handlePreview:function(e){var t=this.downloadUrl+"?docId="+encodeURI(encodeURI(e.docId))+"&fileName="+e.name;console.log(e);var a=document.createElement("a");a.href=t,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(t)},beforeUpload:function(e){return!(e.size/1024/1024>10)||(this.$message.error("上传文件过大，请分开上传，单个文件最大为10M。"),!1)},beforeRemove:function(e,t){return!this.beforeUpload(e)||this.$confirm("确定移除 "+e.name+"？")},handleRemove:function(e,t){var a=this;this.orderPay.docIdList.some(function(t,r){if(t.docId==e.docId)return a.orderPay.docIdList.splice(r,1),!0}),console.log(e.docId,e,t)},queryOrderPays:function(){var e=this;this.$http_post(o.default.baseContext+"/supervise/supOrderPay/all",{orderId:this.orderData.data.id}).then(function(t){1==t.state?e.orderData.orderPays=t.row:(e.$alert(t.message),rLoading.close())})},deleteOrderPay:function(e){var t=this;this.$confirm("确定要删除该记录吗").then(function(){var a=t.openLoading("");t.$http_post(o.default.baseContext+"/supervise/supOrderPay/delete/"+e.id,{}).then(function(e){1==e.state?(t.$message.success("删除成功"),t.queryOrderPays(),a.close()):(t.$alert(e.message),a.close())})})},editOrderPay:function(e){console.log(e),this.orderPay={id:e.id,orderId:this.orderData.data.id,payPrice:e.payPrice,payTime:e.payTime,remark:e.remark,docInfo:e.docInfo,docIdList:JSON.parse(e.docInfo)},this.fileList=JSON.parse(e.docInfo)},saveOrderPay:function(){var e=this;if(!this.orderPay.docIdList||0==this.orderPay.docIdList.length)return this.$message.error("请上传支付凭证。"),!1;this.$refs.orderPay.validate(function(t){if(t){e.orderPay.docInfo=(0,r.default)(e.orderPay.docIdList);var a=e.openLoading("提交中...");e.$http_post(o.default.baseContext+"/supervise/supOrderPay/save",e.orderPay,!0).then(function(t){1==t.state?(e.$message.success("提交成功"),e.queryOrderPays(),e.orderPay={id:"",orderId:e.orderData.data.id,payPrice:0,payTime:"",remark:"",docInfo:"",docIdList:[]},e.fileList=[],a.close()):(e.$message.error(t.message),a.close())})}})},editPay:function(e){var t=this,a=this.openLoading(""),r=o.default.baseContext+"/supervise/supOrder/show/"+e.id;this.$http_post(r,{}).then(function(e){1==e.state?(t.orderData=e.row,t.orderPay.orderId=e.row.data.id,t.orderPay.payPrice=e.row.data.totalPrice,t.orderPay={id:"",orderId:e.row.data.id,payPrice:e.row.data.totalPrice,payTime:"",remark:"",docInfo:"",docIdList:[]},t.fileList=[],console.log(t.orderData),t.payDialogVisible=!0):a.close()})},showOrder:function(e){var t=this,a=this.openLoading("查询中"),r=o.default.baseContext+"/supervise/supOrder/show/"+e.id;this.$http_post(r,{}).then(function(e){1==e.state?(t.orderData=e.row,console.log(t.orderData),t.dialogVisible=!0,a.close()):(a.close(),t.$alert(e.message))})},updatePayStatus:function(e){var t=this,a=this.openLoading("");this.$http_post(o.default.baseContext+"/supervise/supOrder/checkPayStatus/"+e.id,{}).then(function(e){if(a.close(),1==e.state)return 0==e.row.status?t.$confirm("该订单存在【"+e.row.msg+"】预警,确定要结束凭证上传吗，点击确定结束。(结束后不可再更改)"):t.$confirm("确定要结束凭证上传吗，点击确定结束。(结束后不可再更改)");t.$alert(e.message)}).then(function(r){try{if(r&&"confirm"==r)return t.$http_post(o.default.baseContext+"/supervise/supOrder/updatePayStatus/"+e.id,{})}catch(e){a.close()}}).then(function(e){e&&1==e.state?(t.$message.success("修改成功"),t.onQuery()):t.$message.error(e.message),a.close()})},onSearch:function(e){"reset"==e?(this.params.payStatus="",this.params.orderNum="",this.params.warning="",this.params.status="",this.onQuery()):""!=this.params.orderNum||""!=this.params.warning||""!=this.params.payStatus?(this.params.page=1,this.onQuery()):this.$message.warning("请输入查询条件")},onPageClick:function(e){this.params.page=e,this.onQuery()},onQuery:function(){var e=this,t=this.params,a=this.openLoading(),r=o.default.baseContext+"/supervise/supOrder/list";this.$http_post(r,t).then(function(t){1==t.state?(e.dataList=t.rows,e.params.records=t.records,a.close()):(a.close(),e.$alert(t.message))})},time:function(e,t){if(void 0==e.submitTime||""==e.submitTime)return"";var a=new Date(e.submitTime);return a.getFullYear()+"-"+((a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-")+(a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ")+(a.getHours()<10?"0"+a.getHours()+":":a.getHours()+":")+(a.getMinutes()<10?"0"+a.getMinutes()+":":a.getMinutes()+":")+(a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds())},downloadFile:function(e,t){var a=this.downloadUrl+"?docId="+encodeURI(encodeURI(e))+"&fileName="+t,r=document.createElement("a");r.href=a,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(a)},init:function(){}},mounted:function(){var e=this,t=l.default.doCloundRequest(o.default.app_key,o.default.app_security,"");this.headers["x-aep-appkey"]=t["x-aep-appkey"],this.headers["x-aep-signature"]=t["x-aep-signature"],this.headers["x-aep-timestamp"]=t["x-aep-timestamp"],this.headers["x-aep-nonce"]=t["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,this.onQuery(),this.getDictItem("WARNING"),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null},filters:{formatTime:function(e){if(void 0==e||""==e)return"";var t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ")+(t.getHours()<10?"0"+t.getHours()+":":t.getHours()+":")+(t.getMinutes()<10?"0"+t.getMinutes()+":":t.getMinutes()+":")+(t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds())}}}},O4PE:function(e,t,a){"use strict";a("jQf4")},RLBC:function(e,t,a){"use strict";a.r(t);var r=a("JiVb"),o=a.n(r);for(var s in r)["default"].indexOf(s)<0&&function(e){a.d(t,e,function(){return r[e]})}(s);t.default=o.a},WwaR:function(e,t,a){"use strict";a.r(t);var r=a("vRJ1"),o=a("RLBC");for(var s in o)["default"].indexOf(s)<0&&function(e){a.d(t,e,function(){return o[e]})}(s);a("O4PE");var l=a("gp09"),n=Object(l.a)(o.default,r.render,r.staticRenderFns,!1,null,"03736284",null);t.default=n.exports},a6zc:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"},[t("span",[e._v("订单号")]),t("el-input",{attrs:{placeholder:"请输入订单号"},model:{value:e.params.orderNum,callback:function(t){e.$set(e.params,"orderNum",t)},expression:"params.orderNum"}}),t("span",[e._v("预警状态")]),t("el-select",{attrs:{placeholder:"请选择状态"},model:{value:e.params.warning,callback:function(t){e.$set(e.params,"warning",t)},expression:"params.warning"}},e._l(e.warningData,function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1),t("span",[e._v("支付状态")]),t("el-select",{attrs:{placeholder:"全部"},model:{value:e.params.payStatus,callback:function(t){e.$set(e.params,"payStatus",t)},expression:"params.payStatus"}},[t("el-option",{key:"",attrs:{label:"全部",value:""}}),t("el-option",{key:"0",attrs:{label:"未支付",value:"0"}}),t("el-option",{key:"1",attrs:{label:"已支付",value:"1"}}),t("el-option",{key:"2",attrs:{label:"部分支付",value:"2"}})],1),t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1)]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.dataList,"highlight-current-row":"",height:e.tableHeight}},[t("el-table-column",{attrs:{prop:"orderNum",label:"订单号",align:"center",width:"230"}}),t("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",align:"center",width:"200"}}),t("el-table-column",{attrs:{prop:"totalPrice",label:"订单总金额(元)",align:"center",width:"200"}}),t("el-table-column",{attrs:{prop:"status",label:"订单状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return["0"==a.row.status?t("span",[e._v("暂存")]):e._e(),"1"==a.row.status?t("span",[e._v("已提交")]):e._e(),"2"==a.row.status?t("span",[e._v("已完成")]):e._e()]}}])}),t("el-table-column",{attrs:{prop:"paySurplus",label:"所剩支付天数",align:"center",width:"120"}}),t("el-table-column",{attrs:{prop:"payStatus",label:"支付状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return["0"==a.row.payStatus?t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("未支付")])],1):e._e(),"1"==a.row.payStatus?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("已支付")])],1):e._e(),"2"==a.row.payStatus?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("部分支付")])],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"warning",label:"预警状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return["1"==a.row.warning?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("正常")])],1):e._e(),"1"!=a.row.warning?t("div",[t("el-popover",{attrs:{placement:"top-start",width:"250",trigger:"hover"}},[e._l(e.getWaring(a.row.warning),function(a,r){return t("div",{staticClass:"text item"},[e._v("\n                                    "+e._s(r+1+"、"+a.name)+"\n                                ")])}),t("el-tag",{attrs:{slot:"reference",type:"danger"},slot:"reference"},[e._v("查看预警")])],2)],1):e._e()]}}])}),t("el-table-column",{attrs:{prop:"submitTime",formatter:e.time,label:"采购时间",align:"center"}}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"200"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.editPayVoucher(a.row.id)}}},[e._v("支付凭证\n                        ")]),"1"!=a.row.payStatus?t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.updatePayStatus(a.row)}}},[e._v("支付完成\n                        ")]):e._e()]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)]),t("el-dialog",{attrs:{title:"【"+e.orderData.data.orderNum+"】订单详情",visible:e.dialogVisible,top:"10vh",width:"80%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",{staticClass:"dialog-content"},[t("el-form",{ref:"orderDataForm1",staticClass:"item-form",attrs:{id:"orderData",model:e.orderData,"label-width":"90px"}},[t("div",{staticClass:"fromBox",staticStyle:{"margin-bottom":"10px"}},[t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"订单号"}},[t("el-input",{attrs:{value:e.orderData.data.orderNum}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"医疗机构"}},[t("el-input",{attrs:{value:e.orderData.data.hospitalName}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"采购时间"}},[t("el-input",{attrs:{value:e._f("formatTime")(e.orderData.data.submitTime)}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"提交人"}},[t("el-input",{attrs:{value:e.orderData.data.userName}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"支付状态"}},["0"==e.orderData.data.payStatus?t("el-input",{attrs:{value:"未支付"}}):e._e(),"1"==e.orderData.data.payStatus?t("el-input",{attrs:{value:"已支付"}}):e._e(),"2"==e.orderData.data.payStatus?t("el-input",{attrs:{value:"部分支付"}}):e._e()],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"订单状态"}},["0"==e.orderData.data.status?t("el-input",{attrs:{value:"暂存"}}):e._e(),"1"==e.orderData.data.status?t("el-input",{attrs:{value:"已提交"}}):e._e(),"2"==e.orderData.data.status?t("el-input",{attrs:{value:"已完成"}}):e._e()],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"预警状态"}},[t("el-input",{attrs:{value:e.orderData.data.warning}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"结束时间"}},[t("el-input",{attrs:{value:e._f("formatTime")(e.orderData.data.endTime)}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"合计"}},[t("el-input",{attrs:{value:e.orderData.data.totalPrice+"元"}})],1)],1)],1)],1),t("div",[t("span",[e._v("药品订单列表：")]),t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.orderData.orderItem,border:"","highlight-current-row":""}},[t("el-table-column",{attrs:{prop:"source",label:"采购平台",width:"200"},scopedSlots:e._u([{key:"default",fn:function(a){return["1"==a.row.source?t("span",[e._v("\n                                    广东省药品交易平台\n                                ")]):e._e(),"2"==a.row.source?t("span",[e._v("\n                                    深圳市药品交易平台\n                                ")]):e._e(),"3"==a.row.source?t("span",[e._v("\n                                    广州市药品交易平台\n                                ")]):e._e()]}}])}),t("el-table-column",{attrs:{prop:"busiCode",label:"采购平台平台编码",width:"150"}}),t("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"200"}}),t("el-table-column",{attrs:{align:"center",prop:"country",label:"是否国家集中集采"},scopedSlots:e._u([{key:"default",fn:function(a){return["1"==a.row.country?t("span",[e._v("是")]):t("span",[e._v("否")])]}}])}),t("el-table-column",{attrs:{prop:"category",label:"类别"},scopedSlots:e._u([{key:"default",fn:function(a){return["1"==a.row.category?t("span",[e._v("中药\n                                ")]):e._e(),"2"==a.row.category?t("span",[e._v("西药\n                                ")]):e._e(),"1"!=a.row.category&&"2"!=a.row.category?t("span",[e._v("\n                                    其它\n                                ")]):e._e()]}}])}),t("el-table-column",{attrs:{prop:"specs",label:"规格"}}),t("el-table-column",{attrs:{prop:"dosageForm",label:"剂型"}}),t("el-table-column",{attrs:{prop:"drugCompanyName",label:"生产企业",width:"200"}}),t("el-table-column",{attrs:{prop:"unitPrice",label:"单价(元)"}}),t("el-table-column",{attrs:{prop:"amount",label:"采购数量",width:"100"}}),t("el-table-column",{attrs:{prop:"itemPrice",label:"价格(元)",width:"100"}}),t("el-table-column",{attrs:{prop:"systemContrast",label:"是否系统推荐",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return["1"==a.row.systemContrast?t("span",[e._v("是")]):e._e(),"0"==a.row.systemContrast?t("span",[e._v("否(原因："+e._s(a.row.reason)+")")]):e._e()]}}])})],1)],1)])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")])],1)]),t("el-dialog",{attrs:{title:"【"+e.orderData.data.orderNum+"】支付凭证",visible:e.payDialogVisible,top:"10vh","close-on-click-modal":!1,"close-on-press-escape":!1,width:"90%"},on:{"update:visible":function(t){e.payDialogVisible=t}}},[t("div",{staticClass:"dialog-content"},[t("el-form",{ref:"orderDataForm2",staticClass:"item-form",attrs:{id:"orderData2",model:e.orderData,"label-width":"90px"}},[t("div",{staticClass:"fromBox",staticStyle:{"margin-bottom":"10px"}},[t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"订单号"}},[e._v("\n                                "+e._s(e.orderData.data.orderNum)+"\n                            ")])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"医疗机构"}},[e._v("\n                                "+e._s(e.orderData.data.hospitalName)+"\n                            ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"采购时间"}},[t("span"),e._v(e._s(e._f("formatTime")(e.orderData.data.submitTime))+"\n                            ")])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"合计"}},[e._v("\n                                "+e._s(e.orderData.data.totalPrice)+"元\n                            ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"提交人"}},[e._v("\n                                "+e._s(e.orderData.data.userName)+"\n                            ")])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"订单状态"}},["0"==e.orderData.data.status?t("span",[e._v("暂存")]):e._e(),"1"==e.orderData.data.status?t("span",[e._v("已提交")]):e._e(),"2"==e.orderData.data.status?t("span",[e._v("已完成")]):e._e()])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"支付状态"}},["0"==e.orderData.data.payStatus?t("span",[e._v("未支付")]):e._e(),"1"==e.orderData.data.payStatus?t("span",[e._v("已支付")]):e._e(),"2"==e.orderData.data.payStatus?t("span",[e._v("部分支付")]):e._e()])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"支付时间"}},[e._v("\n                                "+e._s(e._f("formatTime")(e.orderData.data.payTime))+"\n                            ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"预警状态"}},["1"==e.orderData.data.warning?t("span",[e._v("正常")]):e._e(),"1"!=e.orderData.data.warning?t("span",[e._v("预警\n                                ")]):e._e()])],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"订单结束时间"}},[e._v("\n                                "+e._s(e._f("formatTime")(e.orderData.data.endTime))+"\n                            ")])],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{"label-width":"140px",label:"医院地址(收货地址)"}},[null!=e.orderData.hospital?t("span",[e._v(e._s(e.orderData.hospital.address))]):e._e()])],1)],1)],1)]),t("div",{staticClass:"pay-bottom"},[t("div",{staticClass:"left"},[t("el-form",{ref:"orderPay",staticClass:"item-form",attrs:{id:"orderPay",model:e.orderPay,"label-width":"90px"}},[t("div",{staticClass:"fromBox"},[t("span",[e._v("支付凭证上传：")]),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"支付金额",prop:"payPrice",rules:e.rules.payPrice}},[t("el-input",{staticStyle:{width:"200px"},attrs:{type:"number","inline-message":"true",placeholder:"请输入实际支付价格"},model:{value:e.orderPay.payPrice,callback:function(t){e.$set(e.orderPay,"payPrice",e._n(t))},expression:"orderPay.payPrice"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"支付时间",prop:"payTime",rules:e.rules.payTime}},[t("el-date-picker",{attrs:{type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss",size:"small",placeholder:"请选择支付时间"},model:{value:e.orderPay.payTime,callback:function(t){e.$set(e.orderPay,"payTime",t)},expression:"orderPay.payTime"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"凭证上传"}},[t("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{headers:e.headers,action:e.uploadUrl,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"before-upload":e.beforeUpload,"before-remove":e.beforeRemove,"on-success":e.onSuccess,"on-error":e.onError,multiple:"",accept:".gif,.jpg,.jpeg,.bmp,.png,.doc,.docx,.txt,.xlsx,.xls,.pdf","file-list":e.fileList}},[t("el-button",{attrs:{size:"small"}},[e._v("点击上传")])],1)],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"备注",prop:"remark"}},[t("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.orderPay.remark,callback:function(t){e.$set(e.orderPay,"remark",t)},expression:"orderPay.remark"}})],1)],1)],1),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-button",{staticStyle:{float:"right","margin-top":"5px"},attrs:{size:"small",type:"primary"},on:{click:e.saveOrderPay}},[e._v("确定\n                                    ")])],1)],1)],1)])],1),t("div",{staticClass:"right"},[t("span",[e._v("支付凭证列表：")]),t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.orderData.orderPays,border:"","highlight-current-row":""}},[t("el-table-column",{attrs:{label:"序号",width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(t.$index+1)+"\n                            ")]}}])}),t("el-table-column",{attrs:{label:"支付金额(元)",align:"center",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(t.row.payPrice)+"\n                            ")]}}])}),t("el-table-column",{attrs:{width:"180px",label:"支付时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(e._f("formatTime")(t.row.payTime))+"\n                            ")]}}])}),t("el-table-column",{attrs:{width:"300px",label:"支付凭证",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return e._l(JSON.parse(a.row.docInfo),function(a,r){return t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.downloadFile(a.docId,a.name)}}},[e._v("\n                                    "+e._s(a.name)+"\n                                ")])})}}])}),t("el-table-column",{attrs:{label:"备注",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(t.row.remark)+"\n                            ")]}}])}),t("el-table-column",{attrs:{width:"180px",label:"数据上传时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                                "+e._s(e._f("formatTime")(t.row.creationTime))+"\n                            ")]}}])}),t("el-table-column",{attrs:{label:"操作",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.editOrderPay(a.row)}}},[e._v("编辑")]),t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.deleteOrderPay(a.row)}}},[e._v("删除")])]}}])})],1)],1)])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.payDialogVisible=!1}}},[e._v("取 消")])],1)])],1)},t.staticRenderFns=[]},jQf4:function(e,t,a){},vRJ1:function(e,t,a){"use strict";var r=a("a6zc");a.o(r,"render")&&a.d(t,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return r.staticRenderFns})}}]);