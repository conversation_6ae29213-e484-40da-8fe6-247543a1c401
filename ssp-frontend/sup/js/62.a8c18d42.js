(window.webpackJsonp=window.webpackJsonp||[]).push([[62],{UQUM:function(t,e,a){"use strict";a.r(e);var o=a("Zm5j"),s=a.n(o);for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,function(){return o[t]})}(n);e.default=s.a},XDPV:function(t,e,a){"use strict";a("hclk")},XQ0F:function(t,e,a){"use strict";var o=a("uVwT");a.o(o,"render")&&a.d(e,"render",function(){return o.render}),a.o(o,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return o.staticRenderFns})},Zm5j:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var o=l(a("Q9c5")),s=l(a("DWNM")),n=l(a("XRYr")),i=l(a("cH4l"));function l(t){return t&&t.__esModule?t:{default:t}}e.default={name:"deductionList",mixins:[s.default],data:function(){return{detail:{supDeduction:{},transactionNumbers:[]},downloadUrl:o.default.baseContext+"/file/download",confirmData:{},confirmDataList:[],showBox:!1,supDeduction:{id:"",status:""},hospitalList:[],rLoading:{},fileList:[],headers:{},uploadData:{},importDialogVisible:!1,tableHeight:100,dialogVisible:!1,dataList:[],params:{page:1,limit:10,records:0,hospitalName:"",status:""},adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1}},props:{},watch:{},computed:{uploadUrl:function(){return o.default.baseContext+"/file/upload"}},methods:{getHospitalList:function(){var t=this,e=o.default.baseContext+"/supervise/supHospital/all";this.$http_post(e,{}).then(function(e){1==e.state?t.hospitalList=e.row:t.$alert(e.message)})},officialNotice:function(t){var e=this,a=this.$options.filters.formatTime(t.orderCreationTime),s=t.hospitalName;this.$confirm("是否向"+s+"以及社保局发出公文提醒？","公文通知",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var n=e.openLoading(),i={deductionCode:t.code,busiId:t.id,hospitalId:t.hospitalId,hospitalName:s,orderCreationTime:a,sourceName:"深圳市药品交易平台",orderNum:t.orderCode,invoiceItemNo:t.itemNo,drugCatalogName:t.catalogName,noticeNum:null==t.noticeNum?1:t.noticeNum},l=o.default.baseContext+"/supervise/supOfficial/sendTemplate/insurance_deduction_notice";e.$http_post(l,i,!0).then(function(t){n.close(),1==t.state?(e.$message({type:"success",message:"通知成功！"}),e.onInvoiceQuery()):e.$alert(t.message)})})},changeBankCard:function(t){var e=i.default.getBankName(t.bankCard);null!=this.confirmDataList&&this.confirmDataList.length>0&&this.confirmDataList.forEach(function(a){a.id==t.id&&(a.bankName=e.bankName)})},onSuccess:function(t,e,a,o){if("1"!=e.state)return this.$message.error(e.message),this.$refs.upload.clearFiles(),!1;a.docId=e.row.id;var s={docId:e.row.id,name:e.row.name},n=[];null!=this.confirmDataList&&this.confirmDataList.length>0&&this.confirmDataList.forEach(function(e){e.id==t.id&&(e.docIdList?e.docIdList.push(s):(n.push(s),e.docIdList=n))})},onError:function(t,e,a){this.rLoading.close(),this.$message.error("上传失败,请重试")},handlePreview:function(t){var e=this.downloadUrl+"?docId="+encodeURI(encodeURI(t.docId))+"&fileName="+t.name,a=document.createElement("a");a.href=e,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(e)},beforeUpload:function(t){return!(t.size/1024/1024>10)||(this.$message.error("上传文件过大，请分开上传，单个文件最大为10M。"),!1)},beforeRemove:function(t,e){return!this.beforeUpload(t)||this.$confirm("确定移除 "+t.name+"？")},handleRemove:function(t,e){null!=this.confirmDataList&&this.confirmDataList.length>0&&this.confirmDataList.forEach(function(a){a.id==t.id&&a.docIdList.some(function(t,o){if(t.docId==e.response.row.id)return a.docIdList.splice(o,1),!0})})},downloadFile:function(t,e){var a=this.downloadUrl+"?docId="+encodeURI(encodeURI(t))+"&fileName="+e,o=document.createElement("a");o.href=a,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(a)},goToItem:function(){this.$router.push({name:"insuranceLog"})},cancel:function(){this.onQuery(),this.dialogVisible=!1,this.showBox=!1},confirmDeduction:function(){var t=this,e=!0;if(this.confirmDataList.forEach(function(a,o){a.bankCard&&a.bankName||(t.$message.error("银行信息有误"),e=!1)}),e){var a=o.default.baseContext+"/supervise/supInsuranceLog/saveInsuranceLog",s=this.openLoading("提交中...");console.log(this.confirmDataList),this.$http_post(a,this.confirmDataList,!0).then(function(e){s.close(),1==e.state?(t.detail=e.row.supDeduction,t.detail.transactionNumbers=e.row.transactionNumbers,t.showBox=!1,t.dialogVisible=!0):t.$alert(e.message)})}},updateStatus:function(t,e){var a=this,s="";"-1"==e&&(s="确认申请撤销吗"),"3"==e&&(s="确认从周转户转款给药品配送商，生成支付指令"),"1"==e&&(s="社保确认扣款单"),"2"==e&&(s="社保经办机构 确认 已划款到医保基金周转户"),"4"==e&&(s="确认该扣款单已完成？"),this.$confirm(s,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){if("3"==e){var s=a.openLoading(),n=o.default.baseContext+"/supervise/supDeduction/show/"+t.id;a.$http_post(n,{}).then(function(t){1==t.state?(a.confirmDataList=t.rows,s.close()):(s.close(),a.$alert(t.message))}),a.showBox=!0}else a.update(t,e)}).catch(function(t){console.log(t)})},update:function(t,e){var a=this;this.supDeduction.id=t.id,this.supDeduction.code=t.code,this.supDeduction.status=e;var s=this.openLoading();this.$http_post(o.default.baseContext+"/supervise/supDeduction/update",this.supDeduction,!0).then(function(t){1==t.state?(a.$message.success("操作成功"),a.onQuery(),s.close()):(s.close(),a.$message.error(t.message))})},showDeduction:function(t){var e=this,a=this.openLoading(),s=o.default.baseContext+"/supervise/supDeduction/show/"+t.id;this.$http_post(s,{}).then(function(o){1==o.state?(e.dataList.forEach(function(a,s){a.id==t.id&&(e.dataList[s].itemDetails=o.rows)}),e.params.records=o.records,a.close()):(a.close(),e.$alert(o.message))})},onSearch:function(t){"reset"==t?(this.params.hospitalName="",this.params.status="",this.onQuery()):""!=this.params.hospitalName||""!=this.params.status?(this.params.page=1,this.onQuery()):this.$message.error("请输入查询条件")},onPageClick:function(t){this.params.page=t,this.onQuery()},initRole:function(){var t=this.$store.getters.curUser.roleCode;-1!=t.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=t.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=t.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=t.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)},onQuery:function(){var t=this,e=this.params,a=this.openLoading(),s=o.default.baseContext+"/supervise/supDeduction/list";this.$http_post(s,e).then(function(e){1==e.state?(e.rows.map(function(t){t.itemDetails=[]}),t.dataList=e.rows,t.params.records=e.records,a.close()):(a.close(),t.$alert(e.message))})},time:function(t,e){if(void 0==t.creationTime||""==t.creationTime)return"";var a=new Date(t.creationTime);return a.getFullYear()+"-"+((a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-")+(a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ")+(a.getHours()<10?"0"+a.getHours()+":":a.getHours()+":")+(a.getMinutes()<10?"0"+a.getMinutes()+":":a.getMinutes()+":")+(a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds())}},mounted:function(){var t=this,e=n.default.doCloundRequest(o.default.app_key,o.default.app_security,"");this.headers["x-aep-appkey"]=e["x-aep-appkey"],this.headers["x-aep-signature"]=e["x-aep-signature"],this.headers["x-aep-timestamp"]=e["x-aep-timestamp"],this.headers["x-aep-nonce"]=e["x-aep-nonce"],this.headers["x-aep-token"]=this.$store.getters.token,this.onQuery(),this.initRole(),this.getHospitalList(),this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null},filters:{formatDate:function(t){if(void 0==t||""==t)return"";var e=new Date(t);return e.getFullYear()+"-"+((e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-")+(e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ")+(e.getHours()<10?"0"+e.getHours()+":":e.getHours()+":")+(e.getMinutes()<10?"0"+e.getMinutes()+":":e.getMinutes()+":")+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds())},formatTime:function(t){if(void 0==t||""==t)return"";var e=new Date(t),a=e.getFullYear()+"-",o=(e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-",s=e.getDate()<10?"0"+e.getDate()+" ":e.getDate()+" ";e.getHours(),e.getHours(),e.getMinutes(),e.getMinutes(),e.getSeconds(),e.getSeconds();return a+o+s}}}},cNZq:function(t,e,a){"use strict";a.r(e);var o=a("XQ0F"),s=a("UQUM");for(var n in s)["default"].indexOf(n)<0&&function(t){a.d(e,t,function(){return s[t]})}(n);a("XDPV");var i=a("gp09"),l=Object(i.a)(s.default,o.render,o.staticRenderFns,!1,null,"0a974482",null);e.default=l.exports},hclk:function(t,e,a){},uVwT:function(t,e){Object.defineProperty(e,"__esModule",{value:!0});e.render=function(){var t=this,e=t._self._c;return e("div",{ref:"tableH",staticClass:"content"},[e("div",{staticClass:"search-header flex-row"},[e("div",{staticClass:"left flex-row"},[this.adminRole||this.medicalAdmin||this.socialAdmin?e("span",[t._v("医疗机构")]):t._e(),this.adminRole||this.medicalAdmin||this.socialAdmin?e("el-select",{staticStyle:{width:"15%"},attrs:{clearable:"",filterable:"",placeholder:"请选择医疗机构"},model:{value:t.params.hospitalName,callback:function(e){t.$set(t.params,"hospitalName",e)},expression:"params.hospitalName"}},t._l(t.hospitalList,function(t){return e("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})}),1):t._e(),e("span",[t._v("状态")]),e("el-select",{attrs:{placeholder:"请选择状态"},model:{value:t.params.status,callback:function(e){t.$set(t.params,"status",e)},expression:"params.status"}},[e("el-option",{key:"0",attrs:{label:"扣款单生成",value:"0"}}),e("el-option",{key:"1",attrs:{label:"社保已确认",value:"1"}}),e("el-option",{key:"2",attrs:{label:"周转帐户到账",value:"2"}}),e("el-option",{key:"3",attrs:{label:"划转供应商",value:"3"}}),e("el-option",{key:"4",attrs:{label:"已确认完成",value:"4"}})],1),e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.onSearch}},[t._v("查询")]),e("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(e){return t.onSearch("reset")}}},[t._v("重置")])],1)]),e("el-row",{staticClass:"con"},[e("div",{staticClass:"con-left"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.dataList,"highlight-current-row":"",height:t.tableHeight},on:{"expand-change":t.showDeduction}},[e("el-table-column",{attrs:{type:"expand"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:a.row.itemDetails,"highlight-current-row":""}},[e("el-table-column",{attrs:{prop:"code",label:"发票代码",width:"120"}}),e("el-table-column",{attrs:{prop:"itemNo",label:"发票号"}}),e("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单号",width:"120"}}),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"180"}}),e("el-table-column",{attrs:{prop:"catalogName",label:"通用名"}}),e("el-table-column",{attrs:{prop:"num",label:"数量"}}),e("el-table-column",{attrs:{prop:"unitPrice",label:"单价"}}),e("el-table-column",{attrs:{prop:"taxesAmount",label:"金额"}}),e("el-table-column",{attrs:{prop:"orderCode",label:"订单编号",width:"120"}}),e("el-table-column",{attrs:{prop:"invoiceDate",label:"开票时间"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t._f("formatTime")(a.row.invoiceDate)))])]}}],null,!0)}),e("el-table-column",{attrs:{prop:"stockInTime",label:"入库时间"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t._f("formatTime")(a.row.stockInTime)))])]}}],null,!0)}),e("el-table-column",{attrs:{prop:"payStatus",label:"回款状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.payStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已回款")])],1):t._e(),"2"==a.row.payStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分回款")])],1):t._e(),"0"!=a.row.payStatus&&a.row.payStatus?t._e():e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("未回款")])],1)]}}],null,!0)}),e("el-table-column",{attrs:{prop:"country",label:"来源",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.country?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("国家集采")])],1):t._e(),"0"==a.row.country?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("非国家集采")])],1):t._e(),"2"==a.row.country?e("div",[e("el-tag",{attrs:{type:"primary"}},[t._v("线下采购")])],1):t._e()]}}],null,!0)})],1)]}}])}),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构"}}),e("el-table-column",{attrs:{prop:"totalPrice",label:"扣款单总金额(元)",align:"center"}}),e("el-table-column",{attrs:{prop:"status",label:"状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return["0"==a.row.status?e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("扣款单生成")])],1):t._e(),"1"==a.row.status?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("医院已确认")])],1):t._e(),"2"==a.row.status?e("div",[e("el-tag",{attrs:{type:"primary"}},[t._v("社保已确认")])],1):t._e(),"3"==a.row.status?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("银行已回款")])],1):t._e(),"4"==a.row.status?e("div",[e("el-tag",{attrs:{type:"info"}},[t._v("已确认完成")])],1):t._e()]}}])}),e("el-table-column",{attrs:{prop:"creationTime",formatter:t.time,label:"创建时间",align:"center"}}),e("el-table-column",{attrs:{label:"操作",align:"right",width:"800"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticClass:"flex-row",staticStyle:{"justify-content":"space-between"}},[e("el-steps",{staticStyle:{width:"100%"},attrs:{active:parseInt(a.row.status),space:200,"align-center":"","finish-status":"success","process-status":"finish"}},[e("el-step",{attrs:{title:"社保确认"},nativeOn:{click:function(e){(t.adminRole||t.hospitalAdmin)&&"0"==a.row.status&&t.updateStatus(a.row,"1")}}}),e("el-step",{attrs:{title:"周转户到账"},nativeOn:{click:function(e){(t.adminRole||t.medicalAdmin||t.socialAdmin)&&"1"==a.row.status&&t.updateStatus(a.row,"2")}}}),e("el-step",{attrs:{title:"划转供应商"},nativeOn:{click:function(e){(t.adminRole||t.medicalAdmin)&&"2"==a.row.status&&t.updateStatus(a.row,"3")}}}),e("el-step",{attrs:{title:"确认完成"},nativeOn:{click:function(e){(t.adminRole||t.hospitalAdmin)&&"3"==a.row.status&&t.updateStatus(a.row,"4")}}}),e("el-step",{attrs:{title:"公文通知"},nativeOn:{click:function(e){(t.adminRole||t.medicalAdmin)&&"4"==a.row.status&&t.officialNotice(a.row)}}})],1),e("el-button",{attrs:{type:"text",disabled:"0"==a.row.status,size:"small"},on:{click:function(e){return t.updateStatus(a.row,"-1")}}},[t._v("申请撤销")])],1)]}}])})],1),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{total:t.params.records,"page-size":t.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":t.onPageClick}})],1)],1)]),e("el-dialog",{attrs:{title:"生成支付指令",visible:t.showBox,width:"90%"},on:{close:function(e){t.showBox=!1}}},[e("div",{staticClass:"dialog-content"},[e("div",{staticClass:"header-table"},[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:t.confirmDataList,"highlight-current-row":""}},[e("el-table-column",{attrs:{prop:"deliveryName",label:"收款单位",width:"300px"}}),e("el-table-column",{attrs:{prop:"taxesAmount",label:"扣款金额(元)",width:"250px"}}),e("el-table-column",{attrs:{label:"银行卡号",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-input",{staticClass:"input",attrs:{placeholder:"请输入银行账号"},on:{input:function(e){return t.changeBankCard(a.row)}},model:{value:a.row.bankCard,callback:function(e){t.$set(a.row,"bankCard",e)},expression:"scope.row.bankCard"}})]}}])}),e("el-table-column",{attrs:{label:"银行账户",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-input",{staticClass:"input",attrs:{readonly:"",placeholder:"银行账户根据银行卡号自动回填"},model:{value:a.row.bankName,callback:function(e){t.$set(a.row,"bankName",e)},expression:"scope.row.bankName"}})]}}])}),e("el-table-column",{attrs:{type:"expand",label:"查看详情",width:"200px"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:[t.confirmDataList[a.$index]],"highlight-current-row":""}},[e("el-table-column",{attrs:{prop:"itemNo",label:"发票号"}}),e("el-table-column",{attrs:{prop:"deliveryCode",label:"配送单号",width:"120"}}),e("el-table-column",{attrs:{prop:"hospitalName",label:"医疗机构",width:"180"}}),e("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150"}}),e("el-table-column",{attrs:{prop:"num",label:"数量"}}),e("el-table-column",{attrs:{prop:"noUnitPrice",label:"不含税单价"}}),e("el-table-column",{attrs:{prop:"taxesAmount",label:"含税金额"}}),e("el-table-column",{attrs:{prop:"orderCode",label:"订单编号",width:"120"}}),e("el-table-column",{attrs:{prop:"stockInTime",label:"入库时间",width:"150"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t._f("formatTime")(a.row.stockInTime)))])]}}],null,!0)}),e("el-table-column",{attrs:{prop:"country",label:"来源",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.country?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("国家集采")])],1):t._e(),"0"==a.row.country?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("非国家集采")])],1):t._e(),"2"==a.row.country?e("div",[e("el-tag",{attrs:{type:"primary"}},[t._v("线下采购")])],1):t._e()]}}],null,!0)}),e("el-table-column",{attrs:{prop:"payStatus",label:"回款状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return["1"==a.row.payStatus?e("div",[e("el-tag",{attrs:{type:"success"}},[t._v("已回款")])],1):t._e(),"2"==a.row.payStatus?e("div",[e("el-tag",{attrs:{type:"warning"}},[t._v("部分回款")])],1):t._e(),"0"!=a.row.payStatus&&a.row.payStatus?t._e():e("div",[e("el-tag",{attrs:{type:"danger"}},[t._v("未回款")])],1)]}}],null,!0)}),e("el-table-column",{attrs:{label:"超时天数",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                    "+t._s(null==e.row.overDay?30:-e.row.overDay)+"\n                  ")]}}],null,!0)}),e("el-table-column",{attrs:{label:"已通知次数",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                    "+t._s(null==e.row.noticeNum?0:e.row.noticeNum)+"\n                  ")]}}],null,!0)})],1)]}}])})],1)],1)]),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.showBox=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.confirmDeduction}},[t._v("确 定")])],1)]),e("el-dialog",{attrs:{title:"医保基金支付",visible:t.dialogVisible,width:"30%"}},[e("div",{staticClass:"form"},[e("el-form",{ref:"dataForm",staticClass:"item-form",attrs:{model:t.detail,"label-position":"left","label-width":"110px"}},[e("div",{staticClass:"fromBox"},[e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"支付状态"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:"success"}},[t._v("支付成功")])]}}])})],1)],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"医疗机构"}},[e("el-input",{attrs:{value:t.detail.hospitalName}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"扣款单号"}},[e("el-input",{attrs:{value:t.detail.code}})],1)],1)],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"交易单号"}},[e("table",[e("tbody",t._l(t.detail.transactionNumbers,function(a,o){return e("tr",{key:o},[e("td",[t._v("\n                      "+t._s(a)+"\n                    ")])])}),0)])])],1)],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"采购时间"}},[e("el-input",{attrs:{value:t._f("formatDate")(t.detail.lastModifitionTime)}})],1)],1)],1)],1)])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:t.cancel}},[t._v("关 闭")]),e("el-button",{attrs:{type:"primary"},on:{click:t.goToItem}},[t._v("前往医保基金查询")])],1)])],1)},e.staticRenderFns=[]}}]);