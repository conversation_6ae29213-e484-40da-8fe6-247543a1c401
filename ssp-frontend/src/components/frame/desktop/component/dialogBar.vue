<template>
	<div class="dialog" v-if="showMask">
		<div class="dialog-container">
			<div class="dialog-title"><span>{{title}}</span></div>
			<div class="content">
				<slot></slot>
			</div>
			<div class="footer">
				<div class="btns" v-if="type==='confirm'">
					<div class="cancel-btn" @click="closeMask">
						<i class="iconfont icon-cancel"></i>
						<span>{{cancelText}}</span>
					</div>
					<div class="confirm-btn" @click="confirmBtn">
						<i class="iconfont icon-queren1"></i>
						<span>{{confirmText}}</span>
					</div>
				</div>
				<div class="btnc" v-if="type==='close'">
					<div class="close-btn" @click="closeBtn">
						<span>{{closeText}}</span>
					</div>
				</div>
			</div>
			<div class="pageSide2" v-if="totalPage > 1">
				<div class="pageBar flex flex-v flex-align-center">
					<i :class="upClass" @click="pageClick('up')"></i>
					<p>
						<span>{{nowPage}}</span>/{{totalPage}}
					</p>
					<i :class="downClass" @click="pageClick('down')"></i>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	import pageBar from "./pageBar"
	export default {
		props: {
			totalPage: {
				type: Number,
				default: 1
			},
			value: {
				type: Boolean,
				default: false
			},
			// 类型包括 defalut 默认, confirm 确认，
			type: {
				type: String,
				default: 'default'
			},
			content: {
				type: String,
				default: ''
			},
			title: {
				type: String,
				default: ''
			},
			cancelText: {
				type: String,
				default: '取消'
			},
			confirmText: {
				type: String,
				default: '确认'
			},
			closeText: {
				type: String,
				default: '关闭'
			},
      curPage: {
        type: Number,
        default: 1
      }
		},
		data() {
			return {
				showMask: false,
				nowPage: 1,
				pageShow: true,
			}
		},
    computed:{
      upClass(){
        const nowPage = this.nowPage;
        if(nowPage == 1){
          return ['iconfont','icon-shangjiantou'];
        }else{
          return ['iconfont','icon-shangjiantou','icon-blue'];
        }
      },
      downClass(){
        const nowPage = this.nowPage;
        const totalPage = this.totalPage
        if(nowPage == totalPage){
          return ['iconfont','icon-xiajiantou'];
        }else{
          return ['iconfont','icon-xiajiantou','icon-blue'];
        }
      }
    },
		methods: {
			closeMask() {
				this.$emit('close');
			},
			confirmBtn() {
				this.$emit('confirm');
			},
			closeBtn() {
				this.$emit('close');
			},
			pageClick(type) {
				if (type === "up") {
					if (this.nowPage <= 1) return;
					this.nowPage -= 1
					this.$emit("pageClick", this.nowPage)
					this.$emit("pageUp")
				} else if (type === "down") {
					if (this.nowPage >= this.totalPage) return;
					this.nowPage += 1;
					this.$emit("pageDown")
					this.$emit("pageClick", this.nowPage)
				}
			},
			changeType(type){
				this.type = type;
			}

		},
		mounted() {
			this.showMask = this.value;
			if (this.nowPageStatus) {
				this.nowPage = 1
			}
		},
		components: {
			pageBar
		},
		watch: {
			value(newVal, oldVal) {
				this.showMask = newVal;
				if(this.nowPageStatus){
				  this.nowPage =1;
        }
			},
			showMask(val) {
				this.$emit('input', val);
			},
      curPage(val){
        this.nowPage = val;
      }
		},
		activated() {

		}
	}
</script>

<style scoped>
	.dialog {
		position: fixed;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		width: 19.2rem;
		/* height:10.8rem; */
    height: 100%;
		/*height: 9.63rem;*/
		background: rgba(0, 0, 0, 0.7);
		z-index: 100;

	}

	.dialog .dialog-container {
		max-width: 13.66rem;
		min-width: 10.28rem;
		min-height: 4.3rem;
		background: #ffffff;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		border-radius: .2rem;
		position: relative;
	}

	.dialog .dialog-title {
		width: 100%;
		height: .78rem;
		line-height: .78rem;
		font-weight: 600;
		box-sizing: border-box;
		background: #59A9FF;
		border-radius: .2rem .2rem 0 0;
		text-align: center;
	}

	.dialog .dialog-title span {
		font-size: .3rem;
		color: #fff;
	}

	.dialog .content {
		color: #797979;
		line-height: .36rem;
		padding: 0 .2rem;
		box-sizing: border-box;
	}

	.dialog-container {
		position: relative;
	}

	.footer {
		padding-bottom: .2rem;
	}

	.matters {
		margin-bottom: .72rem;
	}

	.btns {
		width: 100%;
		/* height: .84rem; */
		height: 1rem;
		text-align: right;
		padding: 0 .16rem;
		box-sizing: border-box;
		border-top: 1px solid #dedede;
		position: absolute;
		bottom: 0;
		background: #FFF;
		border-radius: 0 0 .2rem .2rem/0 0 .2rem .2rem;
	}

	.btns>div {
		display: inline-block;
		height: 100%;
		/* line-height: .84rem; */
		line-height: 1rem;
		cursor: pointer;
		text-align: center;
		width: 50%;
	}

	.btns>div span {
		color: #59A9FF;
		font-size: .3rem;

	}

	.confirm-btn {
		color: #59A9FF;
	}

	.cancel-btn {
		float: left;
		border-right: 1px solid #dedede;
	}

	.cancel-btn span {
		color: #666 !important;
	}

	.iconfont {
		font-size: .3rem;
	}

	.close-btn {
		display: inline-block;
		width: 2.5rem;
		padding: .26rem 0;
		background: #59A9FF;
		border-radius: .12rem;
	}
	
	.close-btn span {
		font-size: .3rem;
		color: #fff !important;
	}
	.close-btn:hover{
		cursor:pointer;
		background:#2990ff;
	}
	.btnc {
		position: absolute;
		bottom: 0;
		width: 100%;
		height: 1.2rem;
		text-align: center;
		background: #FFF;
		border-radius: 0 0 .2rem .2rem/0 0 .2rem .2rem;
	}

	.pageSide2 {
		position: absolute;
		right: -1.4rem;
		bottom: 1.4rem;
	}

	.pageBar i {
		display: inline-block;
		width: .88rem;
		height: .88rem;
		line-height: .88rem;
		background: #fff;
		border-radius: 50%;
		font-size: .8rem;
		text-align: center;
		color: #999;
	}

	.pageBar i.icon-blue {
		color: #59a9ff;
	}
  .pageBar .colorGreen{
    background-color:#0091FF !important;
  }
	.pageBar p {
		text-align: center;
		font-size: .3rem;
		line-height: .76rem;
		color: #fff;
	}

	.pageBar span {
		font-size: .3rem;
		color: #59a9ff;
	}
</style>
