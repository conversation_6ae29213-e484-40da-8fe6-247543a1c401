(window.webpackJsonp=window.webpackJsonp||[]).push([[47],{DpLX:function(t,e,a){"use strict";a.r(e);var s=a("cZ1X"),l=a("dlnP");for(var r in l)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return l[t]})}(r);a("Vg7c");var o=a("gp09"),i=Object(o.a)(l.default,s.render,s.staticRenderFns,!1,null,"4cbb255a",null);e.default=i.exports},Vg7c:function(t,e,a){"use strict";a("rnls")},VhH7:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var s=o(a("DWNM")),l=o(a("Q9c5")),r=o(a("rGKd"));function o(t){return t&&t.__esModule?t:{default:t}}e.default={name:"drugSourceStatistics",mixins:[s.default],data:function(){return{hospitalList:[],drugSourceList:[],disabled:!0,dataList:[],brandFold:!1,drugSourceOption:[],drugCatalogList:[],params:{hospitalName:"",code:"",specs:"",catalogName:"",page:1,limit:10,records:0,standardCode:"",goodsName:"",source:"",catalogId:"",country:"0",countryType:"",electionScope:"",dosageForm:"",company:"",approvalNumber:"",status:"1",batch:"",medicalInsuranceCode:"",attribute:""},drugDetail:{code:"",drugCompanyName:"",category:"",id:"",source:"",goodsName:"",catalogName:"",catalogId:"",country:"0",countryType:"",electionScope:"",dosageForm:"",specs:"",packingSpecs:"",packing:"",unit:"",qualityLevel:"",company:"",approvalNumber:"",standardCode:"",medicalInsurance:"",attribute:"",adjuvant:"0",net:"0",coefficient:"",remark:"",version:"",status:""},dialogVisible:!1,tableHeight:100,batchList:[],adminRole:!1,hospitalAdmin:!1,socialAdmin:!1,medicalAdmin:!1}},mounted:function(){var t=this;this.$nextTick(function(){t.tableHeight=t.$refs.tableH.offsetHeight-100}),window.onresize=function(){t.tableHeight=t.$refs.tableH.offsetHeight-100},this.params=this.$route.query,this.initRole(),this.getHospitalList(),this.initData(),r.default.getBatchList(this.setBatchList),document.addEventListener("click",this.onHideSearchCon)},beforeDestroy:function(){window.onresize=null,document.removeEventListener("click",this.onHideSearchCon)},methods:{initRole:function(){var t=this.$store.getters.curUser.roleCode;-1!=t.indexOf("SUPER_ADMIN")?this.adminRole=!0:-1!=t.indexOf("HOSPITAL_ADMIN")?this.hospitalAdmin=!0:-1!=t.indexOf("SOCIAL_SECURITY_ADMIN")?this.socialAdmin=!0:-1!=t.indexOf("MEDICAL_INSURANCE_ADMIN")&&(this.medicalAdmin=!0)},getHospitalList:function(){var t=this,e=l.default.baseContext+"/supervise/supHospital/all";this.$http_post(e,{}).then(function(e){1==e.state?t.hospitalList=e.row:t.$alert(e.message)})},setBatchList:function(t){for(var e=t.rows,a=0;a<e.length;a++)this.batchList.push(e[a])},formatBatch:function(t){if(void 0==t||""==t)return"";for(var e=0;e<this.batchList.length;e++)if(this.batchList[e].code==t)return this.batchList[e].batchName},goBack:function(){this.$router.push({name:"drugList",query:{country:this.$route.query.country}})},getPrice:function(t,e){if(t){var a=t.match(/\d+/g);return e?(e/a.join("")).toFixed(3):""}return""},getDictItemName:function(t){for(var e=0;e<this.drugSourceList.length;e++)if(this.drugSourceList[e].value==t)return this.drugSourceList[e].label},getDictItem:function(t){var e=this,a=this.openLoading();this.$http_post(l.default.baseContext+"/supervise/supDictItem/getItemList",{code:t}).then(function(s){var l=s.rows;1==s.state?("SOURCE"==t&&(e.drugSourceList=l),a.close()):(a.close(),e.$message.error(s.message))})},show:function(t){this.drugDetail=t,this.dialogVisible=!0},initData:function(){this.initDrugList(),this.getDictItem("SOURCE")},onHideSearchCon:function(t){for(var e=0;e<t.path.length;e++){var a=t.path[e];if(a==this.$refs.searchCon||a==this.$refs.foldBtn)return}this.brandFold=!1},handleCurrentChange:function(t){},onSearch:function(t){"reset"==t?(this.params={hospitalName:"",code:"",specs:"",page:1,limit:10,records:0,standardCode:"",source:"",goodsName:"",catalogId:"",country:"0",countryType:"",electionScope:"",dosageForm:"",company:"",approvalNumber:"",status:"1",catalogName:"",batch:"",medicalInsuranceCode:"",attribute:""},this.initDrugList()):(this.params.page=1,this.initDrugList()),this.brandFold=!1},onPageClick:function(t){this.params.page=t,this.initData()},initDrugList:function(){var t=this,e=this.$route.query.country,a=this.$route.query.source,s=this.params;this.params.country=e,this.params.source=a;var r=this.openLoading(),o=l.default.baseContext+"/supervise/supDrugDetail/getSupDrugListBySource";this.$http_post(o,s).then(function(e){if(1==e.state){var a=e.rows;t.dataList=a,t.params.records=e.records,r.close()}else r.close(),t.$alert(e.message)})},changeFoldState:function(){this.brandFold=!this.brandFold}}}},ZuMg:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0}),e.staticRenderFns=e.render=void 0;var s=function(t){return t&&t.__esModule?t:{default:t}}(a("/umX"));e.render=function(){var t,e=this,a=e._self._c;return a("div",{ref:"tableH",staticClass:"content"},[a("div",{staticClass:"search-header flex-row"},[a("div",{staticClass:"left"},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[e._v("通用名")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入通用名"},model:{value:e.params.catalogName,callback:function(t){e.$set(e.params,"catalogName",t)},expression:"params.catalogName"}})],1)])]),"2"==e.$route.query.country&&(this.adminRole||this.medicalAdmin||this.socialAdmin)?a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[e._v("医疗机构")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:(t={size:"small",clearable:""},(0,s.default)(t,"clearable",""),(0,s.default)(t,"filterable",""),(0,s.default)(t,"placeholder","请选择医疗机构"),t),model:{value:e.params.hospitalName,callback:function(t){e.$set(e.params,"hospitalName",t)},expression:"params.hospitalName"}},e._l(e.hospitalList,function(t){return a("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})}),1)],1)])]):e._e(),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[e._v("基药属性")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择基药属性"},model:{value:e.params.attribute,callback:function(t){e.$set(e.params,"attribute",t)},expression:"params.attribute"}},[a("el-option",{attrs:{label:"空",value:"0"}}),a("el-option",{attrs:{label:"国基",value:"1"}}),a("el-option",{attrs:{label:"省基",value:"2"}})],1)],1)])]),"1"==e.$route.query.country?a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[e._v("批次")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择批次"},model:{value:e.params.batch,callback:function(t){e.$set(e.params,"batch",t)},expression:"params.batch"}},e._l(e.batchList,function(t){return a("el-option",{key:t.code,attrs:{label:t.batchName,value:t.code}})}),1)],1)])]):e._e(),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[e._v("药品本位码")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入药品本位码"},model:{value:e.params.standardCode,callback:function(t){e.$set(e.params,"standardCode",t)},expression:"params.standardCode"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[e._v("药品状态")]),a("div",{staticClass:"searchInput"},[a("el-select",{attrs:{size:"small",clearable:"",placeholder:"请选择药品状态"},model:{value:e.params.status,callback:function(t){e.$set(e.params,"status",t)},expression:"params.status"}},[a("el-option",{attrs:{label:"已上架",value:"1"}}),a("el-option",{attrs:{label:"已下架",value:"0"}})],1)],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[e._v("剂型")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入药品剂型"},model:{value:e.params.dosageForm,callback:function(t){e.$set(e.params,"dosageForm",t)},expression:"params.dosageForm"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[e._v("规格")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入药品规格"},model:{value:e.params.specs,callback:function(t){e.$set(e.params,"specs",t)},expression:"params.specs"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[e._v("生产企业")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入药品生产企业"},model:{value:e.params.company,callback:function(t){e.$set(e.params,"company",t)},expression:"params.company"}})],1)])]),a("el-col",{attrs:{span:6}},[a("div",{staticClass:"searchct"},[a("div",{staticClass:"searchLabel"},[e._v("医保药品码")]),a("div",{staticClass:"searchInput"},[a("el-input",{attrs:{size:"small",placeholder:"请输入医保药品代码"},model:{value:e.params.medicalInsuranceCode,callback:function(t){e.$set(e.params,"medicalInsuranceCode",t)},expression:"params.medicalInsuranceCode"}})],1)])])],1)],1),a("div",{staticClass:"right"},[a("div",{staticStyle:{"margin-bottom":"5px"}},[a("el-button",{attrs:{type:"info",icon:"el-icon-arrow-left",size:"small"},on:{click:e.goBack}},[e._v("返回上一页")])],1),a("div",[a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),a("el-button",{attrs:{size:"small",type:"primary",plain:"",icon:"el-icon-refresh-left"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1)])]),a("el-row",{staticClass:"con"},[a("div",{staticClass:"con-left"},[a("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.dataList,"highlight-current-row":"",height:e.tableHeight,border:""},on:{"current-change":e.handleCurrentChange}},[a("el-table-column",{attrs:{prop:"source",label:"平台",width:"180",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[e._v("\n                                 "+e._s(e.getDictItemName(t.row.source))+"\n                               ")])]}}])}),a("el-table-column",{attrs:{prop:"catalogName",label:"通用名",width:"150","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"dosageForm",label:"剂型","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"specs",label:"规格","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"unit",label:"单位","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"packingSpecs",label:"包装规格","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"packing",width:"100","show-tooltip-when-overflow":"",label:"包装材质"}}),a("el-table-column",{attrs:{prop:"attribute",width:"80","show-tooltip-when-overflow":"",label:"基药属性"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",["0"==t.row.attribute?a("div",{attrs:{type:"success"}},[e._v("空")]):e._e(),"1"==t.row.attribute?a("div",{attrs:{type:"success"}},[e._v("国基")]):e._e(),"2"==t.row.attribute?a("div",{attrs:{type:"success"}},[e._v("省基")]):e._e()])]}}])}),a("el-table-column",{attrs:{prop:"standardCode",label:"药品本位码",width:"140","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"medicalInsuranceCode","show-tooltip-when-overflow":"",label:"医保药品代码"}}),a("el-table-column",{attrs:{prop:"qualityLevel",width:"150",label:"质量层次","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{prop:"unitPrice",label:"最小单位价(元)",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.getPrice(t.row.packingSpecs,t.row.unitPrice)?a("div",[a("el-tag",{attrs:{type:"success"}},[e._v(e._s(e.getPrice(t.row.packingSpecs,t.row.unitPrice)))])],1):a("div",[a("el-tag",{attrs:{type:"warning"}},[e._v("暂无")])],1)]}}])}),a("el-table-column",{attrs:{prop:"unitPrice",label:"商品价(元)",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.unitPrice?a("div",[a("el-tag",{attrs:{type:"success"}},[e._v(e._s(t.row.unitPrice))])],1):a("div",[a("el-tag",{attrs:{type:"warning"}},[e._v("暂无")])],1)]}}])}),a("el-table-column",{attrs:{prop:"drugCompanyName","min-width":"200px",label:"生产企业","show-tooltip-when-overflow":""}}),"2"==e.$route.query.country?a("el-table-column",{attrs:{prop:"hospitalName",width:"160","show-tooltip-when-overflow":"",label:"医疗机构"}}):e._e(),"1"==e.$route.query.country?a("el-table-column",{attrs:{prop:"batch",width:"120",label:"批次","show-tooltip-when-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.formatBatch(t.row.batchCode?t.row.batchCode:t.row.batch)))])]}}],null,!1,1899905313)}):e._e(),a("el-table-column",{attrs:{label:"操作",align:"right",fixed:"right",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.show(t.row)}}},[e._v("查看详情\n                               ")])]}}])})],1),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)]),a("el-dialog",{attrs:{title:"【"+e.drugDetail.catalogName+"】药品详情",visible:e.dialogVisible,width:"45%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("div",[a("el-form",{ref:"showDrugDetailForm",staticClass:"item-form",attrs:{model:e.drugDetail,"label-width":"110px"}},[a("div",{staticClass:"fromBox"},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"采购平台"}},[e._v("\n                     "+e._s(e.getDictItemName(e.drugDetail.source))+"\n                   ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"药品本位码"}},[e._v("\n                     "+e._s(e.drugDetail.standardCode)+"\n                   ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"通用名"}},[e._v("\n                     "+e._s(e.drugDetail.catalogName)+"\n                   ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"商品名"}},[e._v("\n                     "+e._s(e.drugDetail.goodsName)+"\n                   ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"商品价格"}},[e._v("\n                     "+e._s(e.drugDetail.unitPrice+"元")+"\n                   ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"最小单位价"}},[e._v("\n                     "+e._s(e.getPrice(e.drugDetail.packingSpecs,e.drugDetail.unitPrice)+"元")+"\n                   ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"药品编码"}},[e._v("\n                     "+e._s(e.drugDetail.code)+"\n                   ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"药品类别"}},["1"==e.drugDetail.category?a("span",[e._v("中药")]):e._e(),"2"==e.drugDetail.category?a("span",[e._v("西药")]):e._e(),"1"!=e.drugDetail.category&&"2"!=e.drugDetail.category?a("span",[e._v("其它")]):e._e()])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"剂型"}},[e._v("\n                     "+e._s(e.drugDetail.dosageForm)+"\n                   ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"规格"}},[e._v("\n                     "+e._s(e.drugDetail.specs)+"\n                   ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包装规格"}},[e._v("\n                     "+e._s(e.drugDetail.packingSpecs)+"\n                   ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"单位"}},[e._v("\n                     "+e._s(e.drugDetail.unit)+"\n                   ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"医保目录"}},["1"==e.drugDetail.medicalInsurance?a("span",[e._v("甲类")]):e._e(),"2"==e.drugDetail.medicalInsurance?a("span",[e._v("乙类")]):e._e()])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"药品类型",prop:"country"}},[a("el-select",{attrs:{disabled:!0},model:{value:e.drugDetail.country,callback:function(t){e.$set(e.drugDetail,"country",t)},expression:"drugDetail.country"}},[a("el-option",{attrs:{label:"国家集采",value:"1"}}),a("el-option",{attrs:{label:"非国家集采",value:"0"}}),a("el-option",{attrs:{label:"医院线上药品",value:"3"}}),a("el-option",{attrs:{label:"医院线下药品",value:"2"}})],1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"生产企业",prop:"company"}},[e._v("\n                     "+e._s(e.drugDetail.drugCompanyName)+"\n                   ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"批准文号"}},[e._v("\n                     "+e._s(e.drugDetail.approvalNumber)+"\n                   ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"基药属性",prop:"attribute"}},[a("el-select",{attrs:{placeholder:"请输入药品基药属性",disabled:!0},model:{value:e.drugDetail.attribute,callback:function(t){e.$set(e.drugDetail,"attribute",t)},expression:"drugDetail.attribute"}},[a("el-option",{attrs:{label:"--请选择--",value:""}}),a("el-option",{attrs:{label:"空",value:"0"}}),a("el-option",{attrs:{label:"国基",value:"1"}}),a("el-option",{attrs:{label:"省基",value:"2"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"医保药品代码",prop:"medicalInsuranceCode"}},[e._v("\n                     "+e._s(e.drugDetail.medicalInsuranceCode)+"\n                   ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包装材质"}},[e._v("\n                             "+e._s(e.drugDetail.packing)+"\n                         ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"质量层次"}},[e._v("\n                             "+e._s(e.drugDetail.qualityLevel)+"\n                       ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{staticClass:"col-left",attrs:{label:"备注"}},[e._v("\n                     "+e._s(e.drugDetail.remark)+"\n                   ")])],1)],1)],1)])],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1,e.drugDetail={}}}},[e._v("关 闭")])],1)])],1)},e.staticRenderFns=[]},cZ1X:function(t,e,a){"use strict";var s=a("ZuMg");a.o(s,"render")&&a.d(e,"render",function(){return s.render}),a.o(s,"staticRenderFns")&&a.d(e,"staticRenderFns",function(){return s.staticRenderFns})},dlnP:function(t,e,a){"use strict";a.r(e);var s=a("VhH7"),l=a.n(s);for(var r in s)["default"].indexOf(r)<0&&function(t){a.d(e,t,function(){return s[t]})}(r);e.default=l.a},rGKd:function(t,e,a){Object.defineProperty(e,"__esModule",{value:!0});var s=r(a("Q9c5")),l=r(a("ERIh"));function r(t){return t&&t.__esModule?t:{default:t}}var o=s.default.baseContext+"/supervise/supDrugBatch/getBatchList";var i={getBatchList:function(t){l.default.$http_api("GET",o,{},{jsonContentType:!1,responseType:null,addBaseUrl:!0}).then(function(e){1==e.state?function(t){return null!=t&&void 0!==t&&"function"==typeof t}(t)&&t(e):console.warn("查询集采批次失败",e.message)}).catch(function(t){console.warn("查询集采批次失败",t.message)})}};e.default=i},rnls:function(t,e,a){}}]);